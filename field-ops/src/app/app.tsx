import { useMemo } from 'react';
import {
  createBrowser<PERSON>outer,
  json,
  Navigate,
  Outlet,
  redirect,
  RouterProvider,
} from 'react-router-dom';
import { FieldOpsRoutes } from './pages/field-ops/FieldOpsRoutes';

import {
  desktopDark,
  useSpaKeycloak,
  useGetOpenTasks,
} from '@4-sure/ui-platform';
import 'styled-components';
import { AppWrapper } from './app-wrapper';
import { ManageSpsRoutes } from './pages/manage-sp/ManageSPRoutes';
import { SettingsRoutes } from './pages/settings/SettingsRoutes';

type CustomTheme = typeof desktopDark;

declare module 'styled-components' {
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  export interface DefaultTheme extends CustomTheme {}
}

export function App() {
  const { keycloak } = useSpaKeycloak();
  useGetOpenTasks({
    _keycloak: keycloak,
    isAdmin: true,
    isAuthorised: true,
    apiEnv: 'VITE_SP_SERVER',
    apiUrl: '/api/v1/task_actions/get_tasks',
    errorKey: 'field-ops-tasks-list-fetch-call',
  });

  const router = useMemo(
    () =>
      createBrowserRouter([
        {
          path: '',
          element: <AppWrapper />,
          loader: async ({ request }) => {
            console.log('you called app level loader');
            const res = await fetch(
              `${
                import.meta.env.VITE_STAFF_SERVER
              }/api/v1/profile_actions/get_profile`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${keycloak.token}`,
                },
              }
            );
            const profile = (await res.json())?.payload;
            console.log('profile', profile);
            const roles = profile?.roles;

            // Role id 44 is the field ops role
            if (!roles?.includes(44)) {
              return redirect(`${import.meta.env.VITE_LANDING_PAGE_URL}`);
            }
            return {};
          },
          children: [
            {
              path: '/',
              element: <Navigate to={'/field-ops'} />,
            },
            FieldOpsRoutes(keycloak),
            SettingsRoutes(keycloak),
            ManageSpsRoutes(keycloak),
          ],
        },
      ]),
    []
  );

  return <RouterProvider router={router} />;
}

export default App;

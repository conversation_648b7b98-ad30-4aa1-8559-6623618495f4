import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import styled from 'styled-components';
import { DocumentViewer } from '../../../Components/Cards/DocumentCard/DocumentViewer';
import { getNestedProperty } from '../../../Engine';
import {
  makeFetchCall,
  previewFile,
  TemplateLiteralLogger,
} from '../../../Utilities';
import { PaginationBar } from '../../Controllers/Pagination/PaginationBar';
import {
  PaginationPageCount,
  useListPagination2,
} from '../../Controllers/Pagination/PaginationPageCount';
import { Icon } from '../../Icons';
import { List, withItems } from '../../List/List';
import { ListItem, ListItemProps } from '../../ListItem/ListItem';
import { Loader } from '../../Loader/Loader';
import { JobImageItem, JobImageItemProps } from '../JobImageItem/JobImageItem';
import { JobImageViewer } from '../JobImageViewer/JobImageViewer';

export interface JobImageListItemProps extends Omit<ListItemProps, 'children'> {
  jobImageItems: any[];
  usecase: string;
  mediaType: string;
  getFileUrl?: string;
  itemClickApiConfig?: ItemClickApiConfig;
  fileDataKey?: string;
  category?: string;
  noDataMessage?: string;
  debug?: boolean;
}

export interface ItemClickApiConfig {
  url: string;
  tokenType?: 'Bearer' | 'Token';
  token?: string;
  headers?: Record<string, string>;
  contentType?: 'application/json' | 'application/x-www-form-urlencoded';
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  bodyObj?: { [key: string]: string | number | boolean };
}

const ListWrapper = styled(List)<{ usecase?: string }>`
  width: 100%;
  padding-inline-start: unset;
  margin: unset;
  margin: ${(props) => props.theme.SpacingMd} 0 0;

  /* Conditional grid layout for DocumentView usecase */
  ${(props) =>
    props.usecase === 'DocumentView'
      ? `
        display: grid;
        grid-template-columns: repeat(2, 1fr); /* 2 columns */
        grid-template-rows: repeat(3, auto); /* 3 rows */
      `
      : `
        display: grid;
        grid-auto-flow: column;
      `}
`;

const NoData = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  height: 150px;
  width: 100%;
  padding: ${(props) => `${props.theme.SpacingXl} ${props.theme.SpacingXs}`};
`;

const InlineSpan = styled.div`
  display: grid;
  grid-auto-flow: column;
  gap: ${(props) => props.theme.GapXs};
  align-items: center;
  text-align: center;
  justify-content: center;
  color: ${(props) => props.theme.ColorsUtilityColorFocus};
`;

const compDbg = new TemplateLiteralLogger({
  prefix: '[Job Image List Item debug]:',
  enabled: false,
  options: { style: { color: '#095d0c' } },
});

const Component = styled(
  ({
    jobImageItems,
    usecase,
    category,
    noDataMessage,
    debug,
    ...rest
  }: JobImageListItemProps) => {
    // I removed ...rest from the bottom const
    const { pages, currentPage, pageItems, ...res } = useListPagination2({
      items: jobImageItems,
      itemsPerPage: 6,
    });

    compDbg.configure({ enabled: debug });
    const log = compDbg.log;
    log`Job Image Items ${{ jobImageItems }}`;
    if (jobImageItems.length < 1)
      return (
        <NoData>
          <InlineSpan>
            <Icon type="warning-circle" size={24} fill="#118ab2bf" />
            <div>
              {noDataMessage
                ? noDataMessage
                : category
                ? `No ${category} found`
                : 'No data found'}
            </div>
          </InlineSpan>
        </NoData>
      );
    return (
      <ListItem {...rest}>
        <ListWrapper usecase={usecase}>
          {withItems(JobImageItem)(pageItems)}
        </ListWrapper>
        {usecase === 'DocumentView' && (
          <PaginationBar
            paginationItems={
              <PaginationPageCount
                pages={pages}
                currentPage={currentPage}
                {...res}
              />
            }
          />
        )}
      </ListItem>
    );
  }
)`
  width: 100%;
  height: 100%;
  position: relative;
  display: grid;
  grid-template-rows: repeat(2, auto);
  text-align: left;
  font-size: ${(props) => props.theme.FontSize2}px;
  color: ${(props) => props.theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  list-style-type: none;
`;

const warn = TemplateLiteralLogger.createLog(
  {
    prefix: '[Job Image List Item warning]:',
    enabled: true,
    options: { style: { color: '#a03f0b' } },
  },
  'warn'
);

const dbg = new TemplateLiteralLogger({
  prefix: '[Job Image List Item debug]:',
  enabled: false,
  options: { style: { color: '#0b5bc4' } },
});
/**
 * A component that displays a list of job image items and supports opening an image viewer
 * when an item is clicked.
 * @param {JobImageItemProps[]} jobImageItems - The list of job image items to display.
 * @param {ReactNode} [children] - The children elements to display.
 * @return {ReactElement} A React element that displays the list of job image items and an
 * image viewer when an item is clicked.
 */
export const JobImageListItem: React.FC<JobImageListItemProps> = ({
  jobImageItems,
  itemClickApiConfig,
  fileDataKey = 'file',
  debug = false,
  ...rest
}) => {
  const [viewerOpen, setViewerOpen] = useState(false);
  const [currentItem, setCurrentItem] = useState<any | null>(null);
  const [loading, setLoading] = useState(false);

  dbg.configure({ enabled: debug });
  const log = dbg.log;

  /**
   * Handles the click event of an item in the list.
   * @param {JobImageItemProps} item - The item that was clicked.
   * Sets the `currentItem` state to the item that was clicked and opens the image viewer.
   */

  const handleItemClick = async (item: JobImageItemProps) => {
    setCurrentItem(item);
    try {
      if (itemClickApiConfig && itemClickApiConfig?.token) {
        setLoading(true);
        // Use config from props
        const {
          url,
          tokenType = 'Bearer',
          token,
          headers,
          contentType = 'application/json',
          method = 'POST',
          bodyObj = { file_id: 'id', return_type: 1 },
        } = itemClickApiConfig;

        const body = Object.keys(bodyObj).reduce(
          (acc: Record<string, any>, key: string) => {
            const setAccValue = (value: any) => {
              const bodyValue = bodyObj[key as keyof typeof bodyObj];
              if (typeof bodyValue === 'string')
                return getNestedProperty(item, bodyValue);
              if (
                typeof bodyValue === 'number' ||
                typeof bodyValue === 'boolean'
              )
                return bodyValue;
              return value;
            };
            const bodyValue = bodyObj[key as keyof typeof bodyObj];
            if (
              typeof bodyValue !== 'boolean' &&
              typeof bodyValue !== 'number' &&
              !bodyValue
            ) {
              return acc;
            }
            acc[key] = setAccValue(bodyValue);
            return acc;
          },
          {} as Record<string, any>
        );

        const result = await makeFetchCall(
          url,
          {
            token,
            headers,
            contentType,
            method,
          },
          body,
          tokenType
        );
        setLoading(false);
        // Add a type assertion to inform TypeScript about the expected structure
        const { payload } = result as { payload?: Record<string, any> };
        if (!payload) {
          warn`No preview available as there is no payload`;
          return;
        }
        log`Handle Item Click Payload: ${payload}`;
        const b64File = payload?.[fileDataKey];
        if (!b64File) {
          warn`No preview available as there is no 'data' in the payload`;
          return;
        }
        const filePreview = await previewFile({
          base64: b64File,
          filename: item.filename,
          purpose: item.purpose,
          created: item.created,
          closePreviewFunc: handleClose,
        });
        log`Handle Item Click File Preview Data: ${filePreview}`;
        setCurrentItem(filePreview);
        setViewerOpen(true);
      } else {
        warn`No preview available as there is no 'itemClickApiConfig' or 'token' provided`;
      }
    } catch (err) {
      console.error(err);
      setLoading(false);
    }
  };

  /**
   * Closes the image viewer and resets the current item to null.
   * This is called when the user closes the image viewer.
   */
  const handleClose = () => {
    setViewerOpen(false);
    setCurrentItem(null);
  };

  /**
   * Handles the next button click event in the viewer.
   * Updates the `currentItem` state with the next item in the list.
   * If the current item is the last item in the list, sets the `currentItem` to the first item in the list.
   */
  const handleNext = () => {
    if (Array.isArray(jobImageItems)) {
      const currentIndex = jobImageItems.findIndex(
        (item: any) =>
          item.name === currentItem?.name &&
          item.TeamMemberName === currentItem?.TeamMemberName &&
          item.date === currentItem?.date &&
          item.time === currentItem?.time
      );
      const nextIndex = (currentIndex + 1) % jobImageItems.length;
      setCurrentItem(jobImageItems[nextIndex]);
    }
  };

  /**
   * Handles the previous button click event in the viewer.
   * Updates the `currentItem` state with the previous item in the list.
   * If the current item is the first item in the list, sets the `currentItem` to the last item in the list.
   */
  const handlePrevious = () => {
    if (Array.isArray(jobImageItems)) {
      const currentIndex = jobImageItems.findIndex(
        (item: any) =>
          item.name === currentItem?.name &&
          item.TeamMemberName === currentItem?.TeamMemberName &&
          item.date === currentItem?.date &&
          item.time === currentItem?.time
      );
      const previousIndex =
        (currentIndex - 1 + jobImageItems.length) % jobImageItems.length;
      setCurrentItem(jobImageItems[previousIndex]);
    }
  };

  const itemsArray = Array.isArray(jobImageItems)
    ? jobImageItems
    : [jobImageItems];

  return (
    <>
      <Component
        {...rest}
        jobImageItems={itemsArray.map((item: JobImageItemProps) => ({
          ...item,
          onClick: () => handleItemClick(item),
        }))}
      />
      {loading && createPortal(<Loader type={'alert'} />, document.body)}
      {viewerOpen &&
        currentItem &&
        createPortal(<DocumentViewer {...currentItem} />, document.body)}
    </>
  );
};

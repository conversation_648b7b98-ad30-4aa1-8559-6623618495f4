import { CSSProperties } from 'react';
import styled from 'styled-components';

const LogoWrapper = styled.div`
  display: grid;
  place-items: center;
`;

export const SantamLogo = ({
  width,
  height,
}: {
  width: CSSProperties['width'];
  height: CSSProperties['height'];
}) => {
  return (
    <LogoWrapper>
      <svg 
        width={width || "125"}
        height={(width && height ? height : 'auto') || "51"}
        viewBox="0 0 125 51"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clip-path="url(#clip0_3831_1996)">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M66.6406 38.7516C66.078 38.7516 65.0901 38.5411 64.8184 37.1609C64.7691 36.9011 64.7461 36.5492 64.7461 36.1208L64.7484 30.8794H67.7845L67.7812 27.6978H64.8198L64.8301 22.7939H60.7047L60.6973 36.7164C60.6955 37.2461 60.742 37.7583 60.8356 38.2423C61.0418 39.2886 61.4937 40.1487 62.1759 40.7826C62.7676 41.344 63.3763 41.7285 63.9851 41.9341C64.6073 42.1447 65.2312 42.2504 65.8382 42.2504C67.1554 42.2504 68.3711 41.9304 69.4534 41.2985L69.0062 37.8982C68.1673 38.4616 67.3759 38.7516 66.6406 38.7516Z"
            fill="#0044C7"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M50.2022 31.5491C50.7296 31.1615 51.314 30.9663 51.9345 30.9663C52.5535 30.9663 53.6301 31.1925 53.8678 32.7275C53.9061 32.9738 53.9232 33.1652 53.9186 33.3076L53.9061 42.0157H58.0224L58.0107 33.6013C57.99 32.8471 57.9596 32.3035 57.9176 32.0326C57.7579 31.0037 57.3104 30.0476 56.5854 29.1802C55.8929 28.3537 54.8574 27.9507 53.4245 27.9507C52.4807 27.9507 51.7061 28.1066 51.1308 28.4104C50.5108 28.7387 49.7913 29.2206 48.9942 29.8342L48.246 30.4024L48.2367 28.2124H44.1582L44.1638 42.0132L48.2215 42.0157L48.2232 33.399L48.347 33.273C49.114 32.4658 49.7382 31.8876 50.2022 31.5491Z"
            fill="#0044C7"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M84.7783 41.988L84.7769 28.3485C83.8007 28.2066 83.0576 28.1074 82.5214 28.0492C81.9204 27.9824 81.1461 27.9507 80.2182 27.9507C78.0368 27.9507 76.2235 28.2248 74.831 28.7621C73.4673 29.2895 72.4464 29.9813 71.7965 30.8172C71.1348 31.6661 70.7229 32.5614 70.5628 33.48C70.3961 34.4226 70.3848 35.3586 70.5228 36.2611C70.6922 37.3438 71.0689 38.3547 71.6386 39.268C72.2108 40.1786 72.9366 40.9106 73.8035 41.4516C74.6526 41.9808 75.5897 42.2501 76.5832 42.2501C77.3547 42.2501 77.9697 42.128 78.3994 41.8876C78.8718 41.6246 79.3523 41.2575 79.8337 40.7969L80.6382 40.0256L80.6423 41.9914L84.7783 41.988ZM80.4791 38.078C79.9218 38.4588 79.3478 38.8495 78.9214 39.0288C78.4814 39.21 78.0607 39.3038 77.6733 39.3038C76.8798 39.3038 76.2119 39.0151 75.6932 38.4513C75.2086 37.9234 74.9041 37.2795 74.7919 36.5376C74.6272 35.5003 74.7096 34.5192 75.0271 33.6226C75.3473 32.7064 75.8929 31.9678 76.6448 31.425C77.4004 30.877 78.2975 30.5999 79.3087 30.5999H80.7175L80.7137 37.9051L80.4791 38.078Z"
            fill="#0044C7"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M105.408 32.7131C105.453 32.9978 105.476 33.3608 105.482 33.7965L105.488 42.0157H109.586L109.588 33.8898C109.579 32.9666 109.548 32.2974 109.493 31.948C109.345 30.9757 108.886 30.0486 108.132 29.1865C107.411 28.3674 106.226 27.9507 104.605 27.9507C103.943 27.9507 103.341 28.0575 102.821 28.2589C102.283 28.4687 101.821 28.7222 101.444 29.0113C101.055 29.308 100.71 29.6233 100.422 29.9491L100.015 30.4023L99.6786 29.8979C98.8047 28.59 97.6083 27.9507 96.0176 27.9507C95.1991 27.9507 94.4389 28.1406 93.7577 28.5135C93.0566 28.902 92.4951 29.3278 92.0951 29.7757L91.2866 30.6793L91.2718 28.2124H87.2165L87.2109 42.0157H91.2718L91.2866 32.9887L91.4011 32.8612C92.0225 32.1871 92.5459 31.7036 92.9937 31.3725C93.4964 31.0049 94.0219 30.8207 94.557 30.8207C95.1 30.8207 95.5144 30.9804 95.7961 31.2923C96.0407 31.567 96.2062 31.9589 96.2836 32.4514C96.3257 32.7168 96.3461 33.276 96.354 34.2161L96.3582 41.9939H100.426L100.413 32.6394L100.542 32.5134C101.716 31.3725 102.714 30.8207 103.597 30.8207C104.165 30.8207 105.155 31.065 105.408 32.7131Z"
            fill="#0044C7"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M41.7285 41.9877L41.7253 28.3502C40.7478 28.2072 40.0079 28.1108 39.4727 28.0526C38.8713 27.9871 38.0959 27.9514 37.1672 27.9514C34.987 27.9514 33.1734 28.2247 31.781 28.7627C30.4163 29.2909 29.3964 29.9811 28.7428 30.8186C28.0889 31.6663 27.6742 32.5625 27.5121 33.4811C27.3473 34.4241 27.3338 35.361 27.4744 36.2617C27.6443 37.3451 28.0175 38.3576 28.5921 39.2698C29.1586 40.1775 29.8842 40.9118 30.7542 41.4525C31.6004 41.9807 32.536 42.2509 33.5345 42.2509C34.3045 42.2509 34.9189 42.1279 35.3495 41.886C35.8196 41.6263 36.3024 41.2603 36.7859 40.7979L37.5782 40.0328L37.5772 41.9877H41.7285ZM37.4757 38.0724C36.7253 38.5867 36.2314 38.8789 35.8702 39.0277C35.4331 39.2096 35.014 39.3008 34.6246 39.3008C33.8275 39.3008 33.1611 39.0155 32.6467 38.4501C32.1607 37.9249 31.8567 37.2768 31.7395 36.5364C31.5807 35.4994 31.6589 34.5216 31.9782 33.6221C32.3009 32.7031 32.8464 31.9647 33.5953 31.4231C34.3499 30.8776 35.245 30.6 36.2607 30.6H37.6684L37.6702 37.943L37.4757 38.0724Z"
            fill="#0044C7"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M25.149 40.4819C25.6582 39.7802 25.9164 38.9271 25.9164 37.9537C25.9164 37.0863 25.7078 36.354 25.2961 35.7843C24.872 35.1901 24.2708 34.6544 23.509 34.1965C22.7185 33.7198 21.6414 33.1701 20.3087 32.5637C19.5092 32.1958 18.9618 31.8842 18.6346 31.6143C18.2463 31.2923 18.0469 30.9015 18.0469 30.4447C18.0469 29.974 18.2518 29.5566 18.6346 29.2413C19.0083 28.9313 19.5615 28.779 20.3289 28.779C21.3886 28.779 22.3977 28.9916 23.3283 29.4043C23.9767 29.6917 24.5897 30.0136 25.1563 30.3602V27.3388C24.9873 27.2499 24.7166 27.1195 24.2838 26.9379C23.7681 26.7177 23.0995 26.5188 22.2986 26.339C21.4994 26.1645 20.6157 26.0757 19.669 26.0757C18.3617 26.0757 17.2422 26.2848 16.3484 26.6984C15.4707 27.104 14.8145 27.6487 14.3899 28.3184C13.9666 28.9916 13.7549 29.7577 13.7549 30.595C13.7549 31.4611 13.9653 32.2169 14.3807 32.8426C14.8089 33.4883 15.3682 34.0332 16.0429 34.4606C16.7446 34.9057 17.5749 35.3258 18.5096 35.7057C19.397 36.0505 20.0955 36.3915 20.5781 36.7237C21.1441 37.1126 21.431 37.6082 21.431 38.198C21.431 38.5492 21.2623 38.9114 20.9305 39.2741C20.5794 39.6529 19.9066 39.8391 18.8731 39.8391C17.8424 39.8391 16.8594 39.6529 15.9597 39.2822C15.2869 39.0079 14.5985 38.6731 13.9061 38.2905V41.5055C14.7401 41.8443 15.5539 42.1075 16.3255 42.289C17.1733 42.4872 17.8613 42.6093 18.3695 42.6532C18.881 42.6947 19.3212 42.7192 19.669 42.7192C20.9098 42.7192 22.02 42.5193 22.9703 42.1297C23.9014 41.7499 24.6329 41.195 25.149 40.4819Z"
            fill="#0044C7"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M64.7644 8.25977H60.4872V9.24165C43.2935 9.53811 27.5153 15.1784 14.9697 24.4345C14.9697 24.4345 16.9104 23.4784 18.8571 22.8864C29.1592 19.7717 39.2058 20.4846 45.5192 24.3454C49.9324 22.2081 55.4886 20.948 61.9147 20.9333C68.3393 20.948 73.8961 22.2081 78.3089 24.3454C84.6221 20.4846 94.6651 19.7717 104.973 22.8864C106.918 23.4784 108.857 24.4345 108.857 24.4345C96.6604 15.4352 81.4047 9.86669 64.7644 9.29677V8.25977Z"
            fill="#FFC520"
          />
        </g>
        <defs>
          <clipPath id="clip0_3831_1996">
            <rect
              width="95.8333"
              height="34.4595"
              fill="white"
              transform="translate(13.7549 8.25977)"
            />
          </clipPath>
        </defs>
      </svg>
    </LogoWrapper>
  );
};

import React, { CSSProperties, memo } from 'react';

import { BetterSureLogo } from './BetterSureLogo/BettersureLogo';
import { ClaimConnectLogo } from './ClaimConnectLogo/ClaimConnectLogo';
import { ClientLogoHOCModel } from './ClientLogoHOCModel';
import { DstvLogo } from './DstvLogo/DstvLogo';
import { FourSureLogo } from './FourSureLogo/FourSureLogo';
import { GameLogo } from './GameLogo/GameLogo';
import { MultiChoiceLogo } from './MultiChoiceLogo/MultiChoiceLogo';
import { PinggoLogo } from './PinggoLogo/PinggoLogo';
import { SilLogo } from './SilLogo/SilLogo';
import { KingPriceLogo } from './KingPriceLogo/KingPriceLogo';
import { SantamLogo } from './SantamLogo/SantamLogo';

export const ClientLogoHOC = ({
  WrappedClientLogo,
  label,
}: ClientLogoHOCModel) => {
  return (props: any) => {
    return (
      <>
        <WrappedClientLogo {...props} />
        {!!label && (
          <p
            style={{
              margin: '0',
              fontSize: '15px',
              fontWeight: 600,
              color: '#fff',
            }}
          >
            {label}
          </p>
        )}
      </>
    );
  };
};

interface ClientLogosProps
  extends Omit<ClientLogoHOCModel, 'WrappedClientLogo'> {
  theme?: 'dark' | 'light';
  client?: string;
  style?: CSSProperties;
}

export const ClientLogos = memo(
  ({ client, theme, label, ...style }: ClientLogosProps) => {
    const logos = {
      pinghoc: ClaimConnectLogo,
      game: GameLogo,
      multichoice: MultiChoiceLogo,
      pinggo: PinggoLogo,
      sil: SilLogo,
      dstv: DstvLogo,
      bettersure: BetterSureLogo,
      default: FourSureLogo,
      kingprice: KingPriceLogo,
      santam: SantamLogo,
    };

    if (client && client in logos) {
      return ClientLogoHOC({
        WrappedClientLogo: logos[client as keyof typeof logos],
        label,
      })({
        theme,
        style,
      });
    }
    return ClientLogoHOC({
      WrappedClientLogo: logos.default,
      label,
    })({
      theme,
      style,
    });
  }
);

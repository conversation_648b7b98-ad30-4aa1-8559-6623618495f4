import React from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { PermissionsIndicator } from '../PermissionsIndicator/PermissionsIndicator';
import { ButtonContextMenu, useContextMenuOptions } from '../../ContextMenu';
import { Icon } from '../../Icons/Icon';
import { MenuItemConfig } from '../../../Engine/models/menu-list-item.config';
import { withItems } from '../../List/List';
import { MenuItem } from '../../Menu';
import useViewportDevice from '../../../Hooks/useViewportDevice';
import { MappedClaim } from '../../../Engine/models/mapped-claim';

interface JobCardProps {
  claim?: MappedClaim;
  JobLinkRouter: any;
  getJobMenuItems: (job: any) => {icon: string; label: string; path: string}[];
}

const JobCardContainer = styled.div`
  display: grid;
  grid-template-rows: auto;
  border-left: solid 1px ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  min-width: 480px;
  > :first-child {
    border-top-right-radius: 4px;
  }
  > div:last-child {
    border-bottom: none;
    border-bottom-right-radius: 4px;
    align-self: center;
  }
`;

const JobCard = styled.div`
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  display: grid;
  box-sizing: border-box;
  grid-template-columns: 0.1fr 1fr 0.1fr 1fr 1fr 0.5fr;
  padding: 0 0 0 0;
  min-height: 36px;
  align-items: center;
  border-bottom: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  cursor: pointer;
  width: 100%;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  > :last-child {
    border-bottom: none;
    align-self: center;
  }
`;

const JobCardIndicator = styled.div`
  align-content: center;
  justify-self: flex-start;
  width: 6px;
  height: 100%;
`;

const JobCardSection = styled.div`
  padding: 6px 0 6px 8px;
  line-height: 1.2;
`;

const JobCardNotesAndCMenuSection = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;

  > :first-child {
    justify-self: end;
    padding-right: 10px;
  }

  > :last-child {
    justify-self: start;
    padding-left: 10px;
  }
`;

const JobCardText = styled.div`
  overflow: hidden;
  font-weight: 200;
  font-size: 0.72rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
  max-width: 120px;
`;

const WithMultipleItems = ({ menuItems, navigate, setShowMenu }: any) => {
  const items: MenuItemConfig[] = menuItems?.map((item: any) => ({
    icon: item.icon,
    label: item.label,
    onClick: (event: React.MouseEvent) => {
      event.stopPropagation();
      if (item.path) {
        navigate(item.path);
      }
      if (setShowMenu) {
        setShowMenu(false);
      }
    },
  }));
  return withItems(MenuItem)(items);
};

export const JobSection: React.FC<JobCardProps> = ({
  claim,
  JobLinkRouter,
  getJobMenuItems,
}) => {
  const navigate = useNavigate();
  const { showMenu, setShowMenu } = useContextMenuOptions();

  return (
    <JobCardContainer>
      {claim?.jobs?.map((job, index: number) => (
        <JobLinkRouter job={job} key={job.id}>
          <JobCard key={index}>
            <JobCardIndicator>
              {job?.permissionGranted && (
                <PermissionsIndicator color={'green'} size="" position="" />
              )}
            </JobCardIndicator>
            <JobCardSection>
              <JobCardText>{job.skillName}</JobCardText>
              <JobCardText>{job.teamleaderName || job.spName}</JobCardText>
            </JobCardSection>
            <JobCardSection>
              <JobCardText>
                <div>{job.formattedDate}</div>
              </JobCardText>
              <JobCardText>
                <div>{job.appointmentTime}</div>
              </JobCardText>
            </JobCardSection>
            <JobCardSection>
              {/* Appointment display logic */}
              <JobCardText>
                {(!job.appointment || job.appointment.reason === 'No Appointment') ? (
                  <div style={{ color: '#888', fontWeight: 400 }}>No Appointment</div>
                ) : null}
                {job.stateTextDisplay}
              </JobCardText>
              <JobCardText>Placeholder</JobCardText>
            </JobCardSection>
            <JobCardSection>
              <JobCardText>Placeholder</JobCardText>
            </JobCardSection>
            <JobCardNotesAndCMenuSection>
              {(() => {
                // Debug logging to see note count values
                // console.log('Job ID:', job.id, 'Note count:', job.claim?.note_count);
                // console.log('Job claim object:', job.claim);          
                
                // Only show notes icon if note_count is more than 0
                return (job.claim?.note_count ?? 0) > 0 ? (
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      if (setShowMenu) {
                        setShowMenu(!showMenu);
                      }
                    }}
                  >
                    <Icon type="notes" size={20} />
                  </div>
                ) : null;
              })()}
              <ButtonContextMenu
                children={<WithMultipleItems menuItems={getJobMenuItems(job)} navigate={navigate} setShowMenu={setShowMenu} />}
                orientation="right"
                additionalStyling="&:hover {background-color: rgba(40, 48, 51, 0.84); color: unset;}"
                dropdownClassName="job-card-dropdown"
              />
            </JobCardNotesAndCMenuSection>
          </JobCard>
        </JobLinkRouter>
      ))}
    </JobCardContainer>
  );
};
/* eslint-disable @typescript-eslint/no-explicit-any */
import type { Meta, StoryObj } from '@storybook/react';
import { JobCardThinSize } from './JobCardThinSize';
import { job } from './job';

const meta: Meta<typeof JobCardThinSize> = {
  component: JobCardThinSize,
  title: 'Components/Cards/JobCardThinSize',
};
export default meta;

type Story = StoryObj<typeof JobCardThinSize>;

export const DefaultJobCardThinSize: Story = {
  args: {
    jobCardNumberPrefix: 'Job',
    job,
    LinkRouter: ({ children }: { children: any }) => <>{children}</>,
    getMenuItems: (job: any) => [
        { icon: 'file-07', label: 'View Details', path: `/jobs/${job.id}` },
        { icon: 'pencil-01', label: 'Edit Job', path: `/jobs/${job.id}/edit` },
        { icon: 'alert-diamond', label: 'Delete Job', path: `/jobs/${job.id}/delete` }
    ],
    callClientAction: (config: any) => console.log('Client action called:', config),
    notesLink: 'https://daily.dev/',
  },
};
import React from 'react';
import styled from 'styled-components';
import { ClaimSection } from '../ClaimSection/ClaimSection';
import { JobSection } from '../JobSection/JobSection';

interface ClaimCardProps {
  claim?: any;
  jobCardNumberPrefix?: string;
  ClaimLinkRouter: any;
  JobLinkRouter: any;
  displayLogo?: boolean;
  menuItems?: { icon: string; label: string; path: string }[];
  getClaimMenuItems?: (claim: any) => { icon: string; label: string; path: string }[];
  getJobMenuItems: (job: any) => { icon: string; label: string; path: string }[];
}

const ClaimAndJobCardContainer = styled.div`
  width: 100%;
  display: grid;
  grid-template-columns: 2fr 5fr;
  border-radius: 4px;
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  height: auto;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  box-sizing: border-box;
`;

export const ClaimCard: React.FC<ClaimCardProps> = (props) => {
  return (
    <ClaimAndJobCardContainer>
      <ClaimSection {...(props as any)} />
      <JobSection {...props} />
    </ClaimAndJobCardContainer>
  );
};

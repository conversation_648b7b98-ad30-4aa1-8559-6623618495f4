import React from 'react';
import styled from 'styled-components';
import { TimeStampCard } from '../TimeStampCard/TimeStampCard';
import { Accordion, AccordionItem, AccordionHeading, AccordionContent } from '../../Accordion';

interface TimeStampEntry {
  dateTime: string;
  modifier: string;
  state: string;
  stateDescription: string;
  reason?: string;
}

interface JobTimeStamp {
  job_id: string;
  title: string;
  entries: TimeStampEntry[];
}

interface TimeStampData {
  jobs: JobTimeStamp[];
  claimStates: TimeStampEntry[];
}

interface TimeStampListProps {
  timeStamps: TimeStampData;
  showClaimUpdates?: boolean;
  showJobUpdates?: boolean;
}

const TimeStampListContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
`;

const SectionHeading = styled.h3`
  font-size: 1rem;
  font-weight: 600;
  color: ${(props) => props?.theme?.ColorsTypographySecondary || '#666'};
  margin: 0 0 0.5rem 0;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid ${(props) => props?.theme?.ColorsInputsInverse || '#e9ecef'};
`;

const EmptyState = styled.div`
  text-align: center;
  color: ${(props) => props?.theme?.ColorsTypographySecondary || '#666'};
  font-style: italic;
  padding: 2rem;
`;

export const TimeStampList: React.FC<TimeStampListProps> = ({
  timeStamps,
  showClaimUpdates = true,
  showJobUpdates = true,
}) => {
  const hasClaimUpdates = timeStamps?.claimStates && timeStamps.claimStates.length > 0;
  const hasJobUpdates = timeStamps?.jobs && timeStamps.jobs.length > 0;

  if (!hasClaimUpdates && !hasJobUpdates) {
    return (
      <TimeStampListContainer>
        <EmptyState>No time stamps</EmptyState>
      </TimeStampListContainer>
    );
  }

  return (
    <TimeStampListContainer>
      {showClaimUpdates && hasClaimUpdates && (
        <>
          <SectionHeading>Claim updates:</SectionHeading>
          {timeStamps.claimStates.map((claimState, index) => (
            <TimeStampCard
              key={`claim-${index}`}
              dateTime={claimState.dateTime}
              modifier={claimState.modifier}
              state={claimState.state}
              stateDescription={claimState.stateDescription}
              reason={claimState.reason}
              indentLevel={1}
            />
          ))}
        </>
      )}

      {showJobUpdates && hasJobUpdates && (
        <>
          <SectionHeading>Job updates:</SectionHeading>
          <Accordion allowMultipleExpanded={true}>
            {timeStamps.jobs.map((jobStamp) => (
              <AccordionItem key={jobStamp.job_id}>
                <AccordionHeading>
                  {`${jobStamp.job_id}: ${jobStamp.title}`}
                </AccordionHeading>
                <AccordionContent>
                  <>
                    {jobStamp.entries.map((entry, index) => (
                      <TimeStampCard
                        key={`job-${jobStamp.job_id}-${index}`}
                        dateTime={entry.dateTime}
                        modifier={entry.modifier}
                        state={entry.state}
                        stateDescription={entry.stateDescription}
                        reason={entry.reason}
                        indentLevel={1}
                      />
                    ))}
                  </>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </>
      )}
    </TimeStampListContainer>
  );
}; 
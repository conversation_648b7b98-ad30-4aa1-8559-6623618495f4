# TimeStampList Component

A React component that displays a list of time stamp entries for both claims and jobs. This component provides an organized view of state changes with collapsible job sections and configurable display options.

## Features

- Displays both claim updates and job updates in separate sections
- Uses the existing Accordion component for collapsible job sections
- Configurable visibility for claim and job sections
- Empty state handling
- Responsive design with smooth animations
- Themed styling that matches the platform design system

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `timeStamps` | `TimeStampData` | Yes | - | The time stamp data object |
| `showClaimUpdates` | `boolean` | No | `true` | Whether to show claim updates section |
| `showJobUpdates` | `boolean` | No | `true` | Whether to show job updates section |

## Data Structure

```typescript
interface TimeStampEntry {
  dateTime: string;
  modifier: string;
  state: string;
  stateDescription: string;
  reason?: string;
}

interface JobTimeStamp {
  job_id: string;
  title: string;
  entries: TimeStampEntry[];
}

interface TimeStampData {
  jobs: JobTimeStamp[];
  claimStates: TimeStampEntry[];
}
```

## Usage

```tsx
import { TimeStampList } from '@your-org/ui-platform';

const timeStampData = {
  claimStates: [
    {
      dateTime: '2024-01-15T10:30:00Z',
      modifier: 'John Doe',
      state: 'Status',
      stateDescription: 'Changed from Pending to Approved',
      reason: 'Claim approved after review',
    },
  ],
  jobs: [
    {
      job_id: 'JOB-001',
      title: 'Plumbing Repair',
      entries: [
        {
          dateTime: '2024-01-15T11:00:00Z',
          modifier: 'Mike Johnson',
          state: 'Status',
          stateDescription: 'Changed from Scheduled to In Progress',
          reason: 'Technician started work',
        },
      ],
    },
  ],
};

// Basic usage
<TimeStampList timeStamps={timeStampData} />

// Hide claim updates
<TimeStampList 
  timeStamps={timeStampData} 
  showClaimUpdates={false} 
/>

// Hide job updates
<TimeStampList 
  timeStamps={timeStampData} 
  showJobUpdates={false} 
/>
```

## Sections

### Claim Updates
- Displays all claim state changes in chronological order
- Each entry shows timestamp, modifier, state change, and optional reason
- No collapsible functionality - always visible

### Job Updates
- Groups job updates by job ID and title
- Uses the existing Accordion component for collapsible sections
- Each job section can be expanded/collapsed independently
- Shows all time stamp entries for that specific job
- Supports multiple expanded sections simultaneously

## Empty State

When no time stamps are available, the component displays a centered message: "No time stamps"

## Styling

The component uses styled-components and provides:

- Consistent spacing and typography
- Smooth expand/collapse animations (provided by Accordion component)
- Hover effects for interactive elements
- Section headers with visual separation
- Responsive design
- Accessible color contrast

## Accessibility

- Proper semantic HTML structure
- ARIA labels for collapsible sections (provided by Accordion component)
- Keyboard navigation support
- Screen reader friendly text structure
- Focus management for interactive elements

## Dependencies

- Uses the existing `Accordion`, `AccordionItem`, `AccordionHeading`, and `AccordionContent` components
- Leverages the platform's established accordion patterns and styling

## Related Components

- `TimeStampCard` - Individual time stamp entry component
- `TimeStamps` - Fragment component for complete time stamps functionality
- `Accordion` - Platform accordion component used for job sections 
import type { Meta, StoryObj } from '@storybook/react';
import { TimeStampList } from './TimeStampList';

const meta: Meta<typeof TimeStampList> = {
  title: 'Components/Cards/TimeStampList',
  component: TimeStampList,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    showClaimUpdates: {
      control: 'boolean',
      description: 'Whether to show claim updates section',
    },
    showJobUpdates: {
      control: 'boolean',
      description: 'Whether to show job updates section',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const mockTimeStampData = {
  claimStates: [
    {
      dateTime: '2024-01-15T10:30:00Z',
      modifier: '<PERSON>',
      state: 'Status',
      stateDescription: 'Changed from Pending to Approved',
      reason: 'Claim approved after review',
    },
    {
      dateTime: '2024-01-15T14:45:00Z',
      modifier: '<PERSON>',
      state: 'Priority',
      stateDescription: 'Changed from Normal to High',
      reason: 'Urgent customer request',
    },
    {
      dateTime: '2024-01-15T09:15:00Z',
      modifier: 'Admin User',
      state: 'Assigned',
      stateDescription: 'Assigned to Team A',
    },
  ],
  jobs: [
    {
      job_id: 'JOB-001',
      title: 'Plumbing Repair',
      entries: [
        {
          dateTime: '2024-01-15T11:00:00Z',
          modifier: 'Mike Johnson',
          state: 'Status',
          stateDescription: 'Changed from Scheduled to In Progress',
          reason: 'Technician started work',
        },
        {
          dateTime: '2024-01-15T13:30:00Z',
          modifier: 'Mike Johnson',
          state: 'Status',
          stateDescription: 'Changed from In Progress to Completed',
          reason: 'Repair completed successfully',
        },
      ],
    },
    {
      job_id: 'JOB-002',
      title: 'Electrical Inspection',
      entries: [
        {
          dateTime: '2024-01-15T15:00:00Z',
          modifier: 'Sarah Wilson',
          state: 'Status',
          stateDescription: 'Changed from Pending to Scheduled',
          reason: 'Appointment scheduled',
        },
      ],
    },
  ],
};

const emptyTimeStampData = {
  claimStates: [],
  jobs: [],
};

const claimOnlyData = {
  claimStates: [
    {
      dateTime: '2024-01-15T10:30:00Z',
      modifier: 'John Doe',
      state: 'Status',
      stateDescription: 'Changed from Pending to Approved',
      reason: 'Claim approved after review',
    },
    {
      dateTime: '2024-01-15T14:45:00Z',
      modifier: 'Jane Smith',
      state: 'Priority',
      stateDescription: 'Changed from Normal to High',
      reason: 'Urgent customer request',
    },
  ],
  jobs: [],
};

const jobsOnlyData = {
  claimStates: [],
  jobs: [
    {
      job_id: 'JOB-001',
      title: 'Plumbing Repair',
      entries: [
        {
          dateTime: '2024-01-15T11:00:00Z',
          modifier: 'Mike Johnson',
          state: 'Status',
          stateDescription: 'Changed from Scheduled to In Progress',
          reason: 'Technician started work',
        },
        {
          dateTime: '2024-01-15T13:30:00Z',
          modifier: 'Mike Johnson',
          state: 'Status',
          stateDescription: 'Changed from In Progress to Completed',
          reason: 'Repair completed successfully',
        },
      ],
    },
  ],
};

export const Default: Story = {
  args: {
    timeStamps: mockTimeStampData,
    showClaimUpdates: true,
    showJobUpdates: true,
  },
};

export const EmptyState: Story = {
  args: {
    timeStamps: emptyTimeStampData,
    showClaimUpdates: true,
    showJobUpdates: true,
  },
};

export const ClaimUpdatesOnly: Story = {
  args: {
    timeStamps: claimOnlyData,
    showClaimUpdates: true,
    showJobUpdates: true,
  },
};

export const JobUpdatesOnly: Story = {
  args: {
    timeStamps: jobsOnlyData,
    showClaimUpdates: true,
    showJobUpdates: true,
  },
};

export const HideClaimUpdates: Story = {
  args: {
    timeStamps: mockTimeStampData,
    showClaimUpdates: false,
    showJobUpdates: true,
  },
};

export const HideJobUpdates: Story = {
  args: {
    timeStamps: mockTimeStampData,
    showClaimUpdates: true,
    showJobUpdates: false,
  },
}; 
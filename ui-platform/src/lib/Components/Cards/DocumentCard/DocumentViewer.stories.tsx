import React, { useState } from 'react';
import { DocumentViewer, DocumentViewerProps } from './DocumentViewer';

export default {
  title: 'Components/DocumentViewer',
  component: DocumentViewer,
  parameters: {
    docs: {
      description: {
        component: `
The **DocumentViewer** component provides a modal-like overlay for previewing documents of various types, including images, PDFs, and other files. 
It supports both remote URLs and base64-encoded sources, and offers actions such as download and open-in-new-tab.

**Props:**
- \`src\`: The document source. Can be a URL or a base64 data URI.
- \`mediaType\`: Determines how the document is rendered. Supports \`'image'\`, \`'pdf'\`, and \`'other'\`.
- \`fileName\`: The name of the file, used for display and download.
- \`purpose\`: A description or label for the document.
- \`created\`: Creation date or timestamp.
- \`onClose\`: Callback to close the viewer.
- \`onDownload\`: Optional callback for download action.
- \`onOpenInTab\`: Optional callback to open the document in a new browser tab.
- \`downloadable\`: If true, shows the download button.
- \`meta\`: Optional metadata to display (e.g., author, size).

**Usage Notes:**
- For base64 sources, use a valid data URI (e.g., \`data:image/png;base64,...\` or \`data:application/pdf;base64,...\`).
- The component automatically chooses the appropriate preview method based on \`mediaType\`.
- The overlay can be closed by calling the \`onClose\` prop.
- The \`onDownload\` and \`onOpenInTab\` handlers are optional but recommended for user convenience.
        `,
      },
    },
  },
};

const imageUrl =
  'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80';
const pdfUrl =
  'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';

// Placeholder base64 strings (replace with real base64 data for actual testing)
const base64Image = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...'; // <-- replace with full base64
const base64Pdf = 'data:application/pdf;base64,JVBERi0xLjQKJcfs...'; // <-- replace with full base64

const commonProps: Omit<DocumentViewerProps, 'src' | 'mediaType' | 'onClose'> =
  {
    purpose: 'Sample Document',
    fileName: 'sample',
    created: '2024-07-17',
    downloadable: true,
    onDownload: () => alert('Download clicked'),
    onOpenInTab: () => window.open(imageUrl, '_blank'),
    meta: { author: 'John Doe', size: '2MB' },
  };

/**
 * Preview an image from a remote URL.
 *
 * This story demonstrates how DocumentViewer can display an image using a direct URL.
 * The download and open-in-tab actions are enabled.
 */
export const ImageFromUrl = () => {
  const [open, setOpen] = useState(true);
  return (
    open && (
      <DocumentViewer
        {...commonProps}
        src={imageUrl}
        mediaType="image"
        onClose={() => setOpen(false)}
        fileName="sample-image.jpg"
      />
    )
  );
};

/**
 * Preview a PDF from a remote URL.
 *
 * This story demonstrates PDF rendering from a direct URL.
 * The viewer provides controls for downloading and opening the PDF in a new tab.
 */
export const PdfFromUrl = () => {
  const [open, setOpen] = useState(true);
  return (
    open && (
      <DocumentViewer
        {...commonProps}
        src={pdfUrl}
        mediaType="pdf"
        onClose={() => setOpen(false)}
        fileName="sample.pdf"
      />
    )
  );
};

/**
 * Preview an image from a base64-encoded string.
 *
 * This story shows how to use DocumentViewer with a base64-encoded image.
 * Replace the placeholder with a real base64 string for actual image preview.
 */
export const ImageFromBase64 = () => {
  const [open, setOpen] = useState(true);
  return (
    open && (
      <DocumentViewer
        {...commonProps}
        src={base64Image}
        mediaType="image"
        onClose={() => setOpen(false)}
        fileName="base64-image.png"
      />
    )
  );
};

/**
 * Preview a PDF from a base64-encoded string.
 *
 * This story demonstrates PDF preview using a base64-encoded data URI.
 * Replace the placeholder with a real base64 string for actual PDF preview.
 */
export const PdfFromBase64 = () => {
  const [open, setOpen] = useState(true);
  return (
    open && (
      <DocumentViewer
        {...commonProps}
        src={base64Pdf}
        mediaType="pdf"
        onClose={() => setOpen(false)}
        fileName="base64-file.pdf"
      />
    )
  );
};

/**
 * Preview an unsupported file type (e.g., DOCX).
 *
 * This story demonstrates the fallback UI for file types that cannot be previewed directly.
 * The viewer will typically show a generic icon and offer download/open actions.
 */
export const OtherFileType = () => {
  const [open, setOpen] = useState(true);
  return (
    open && (
      <DocumentViewer
        {...commonProps}
        src="https://example.com/file.docx"
        mediaType="other"
        onClose={() => setOpen(false)}
        fileName="file.docx"
      />
    )
  );
};

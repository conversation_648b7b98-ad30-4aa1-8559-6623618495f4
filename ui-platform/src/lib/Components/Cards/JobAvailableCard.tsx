/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import { Icon } from '../Icons';
import { IAvailableJob } from '../Models/IAvailableJob';
import { debounce } from 'lodash';
import './styles.css';
// import { useAvailableJobsStore } from '../../Engine';

enum Interested {
  available = 1,
  decline = -1,
  ignore = 0,
}

interface IJobAvailableCardProps {
  available_job: IAvailableJob;
  setDetailsView?: (d: any) => any;
  onInterestExpression?: (job_id: number | string, interest: number, staffId?: string | number) => any;
  children?: any;
  isClicking: boolean;
  declineClicking: boolean;
  isAnimating?: boolean;
  skills: Array<{id: number, name: string}>;
}



const JobAvailableCardContainer = styled.div<{
  isInterested: Interested;
  animate: boolean;
  isClicking: boolean;
  declineClicking: boolean;
}>`
  width: 100%;
  display: grid;
  grid-template-columns: 5fr 4fr 2.8fr;
  border-radius: 4px;
  background: ${(props) => {
    if (props.isClicking || (props.animate && props.isInterested === Interested.available)) {
      return props.theme?.ColorsCardColorCalendarSuccess;
    }
    if (props.declineClicking || (props.animate && props.isInterested === Interested.decline)) {
      return props.theme?.ColorsCardColorCalendarError;
    }
    return props.theme?.ColorsCardColorJobCardPrimary;
  }};
border: ${(props) => {
    if (props.isClicking) return '2px solid green';
    if (props.declineClicking) return '2px solid red';
    return '1px solid transparent';
  }};
  height: 78px;
  overflow: hidden;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  padding: 16px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  box-sizing: border-box;
  margin-bottom: 14px;
  // transition: background 0s, transform 0s ease-in-out;
  > :last-child {
    margin-bottom: 0;
  }
  > :nth-child(2) {
    padding-left: 10px;
    box-sizing: border-box;
  }
  position: relative;
  ${props => props.animate && `
    animation: enlarge-and-disappear 0.3s ease-in-out forwards;
  `}

  @keyframes enlarge-and-disappear {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(0);
      opacity: 0;
      // margin-top: -78px; /* Match the height of the card */
      // margin-bottom: -82px;
    }
  }
  &:hover {
    background: rgba(125, 125, 125, 0.6);
  }
`;

const JobAvailableCardSection = styled.div`
  display: grid;
  grid-template-rows: 1fr 1fr;
  align-self: center;
  color: ${(props) => props.theme?.ColorsTypographyPrimary};
  padding: 4px 0 4px 0;
`;

const JobAvailableCardSectionIcons = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  align-self: center;
  padding: 4px 0 4px 0;
`;

const JobAvailableCardSectionLineItem = styled.div`
  padding: 2px 0 2px 0;
`;

/**
 * JobAvailableCard displays a single job in a list of available jobs.
 *
 * Properties:
 * - available_job: The job to display.
 * - setDetailsView: A callback to set the details view to true or false.
 * - onInterestExpression: A callback to remove the job from the list.
 *
 * @param {{available_job: IAvailableJob, setDetailsView: (listView: boolean) => void, onInterestExpression: (job_id: number) => void}} props
 * @returns {JSX.Element}
 */
const IconWrapper = styled.div<{ iconType: 'check-circle' | 'x-xircle' | 'minus-xircle' | 'map-01' }>`
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    transition: stroke 0.2s ease-in-out;
  }

  &:hover svg {
    stroke: ${props => {
      switch (props.iconType) {
        case 'check-circle':
          return props.theme.ColorsCardColorCalendarSuccess || 'green';
        case 'x-xircle':
          return props.theme.ColorsCardColorCalendarError || 'red';
        default:
          return 'inherit';
      }
    }};
  }
`;

export const JobAvailableCard: React.FC<IJobAvailableCardProps> = ({
  available_job,
  setDetailsView,
  onInterestExpression,
  isAnimating,
  skills,
}) => {
  const [interest, setInterest] = useState<Interested>(Interested.ignore);
  const [animate, setAnimate] = useState<boolean>(false);
  const [isClicking, setIsClicking] = useState<boolean>(false);
  const [declineClicking, setDeclineClicking] = useState<boolean>(false);
  // const { allInfo } = useAvailableJobsStore();
  
  // const [listView, setListView] = useState(false);

  /**
   * Handles a click on one of the interest icons.
   *
   * @param {string} iconType The type of icon that was clicked.
   * @returns {void}
   */

  const formatDateTime = (dateString: string, appointmentName: string) => {
    const date = new Date(dateString);
    const month = date.toLocaleString('default', { month: 'long' });
    const day = date.getDate();
    const time = date.toLocaleTimeString('default', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
    
    return `${month} ${day} ${appointmentName.toLowerCase()} ${time}`;
  };
  const getSkillNameById = (skillId: string | number | undefined, skills: Array<{id: number, name: string}> = []) => {
    if (!skills || !skillId) return 'No skill or id available';
    const skill = skills.find(s => s.id === Number(skillId));
    return skill?.name || 'Unknown Skill';
};

  const handleInterestClick = useCallback(
    (iconType: string) => {
      let newInterest = Interested.ignore;
  
      if (iconType === 'check-circle') {
        newInterest = Interested.available;
        setAnimate(true);
        if (onInterestExpression) {
          // Delay the onInterestExpression call until animation completes
          setTimeout(() => {
            onInterestExpression(available_job.id, newInterest);
          }, 300); // Match animation duration
        }
      } else if (iconType === 'x-xircle') {
        newInterest = Interested.decline;
        setAnimate(true);
        if (onInterestExpression) {
          setTimeout(() => {
            onInterestExpression(available_job.id, newInterest);
          }, 300);
        }
      } else if (iconType === 'minus-xircle') {
        newInterest = Interested.ignore;
        if (onInterestExpression) {
        onInterestExpression(available_job.id, newInterest);
      }
      }
  
      setInterest(newInterest);
    }
    ,[available_job.id, onInterestExpression, setAnimate, setInterest])
  
  // Create debounced version outside of the component or use useMemo
    // const debouncedHandleInterestClick = debounce(handleInterestClick, 300);
    const debouncedHandleInterestClick = useMemo(
      () => debounce(handleInterestClick, 300),
      [handleInterestClick]
    )

    useEffect(() => {
      return () => {
        debouncedHandleInterestClick.cancel();
      };
    }, [debouncedHandleInterestClick]);

  const handleAcceptMouseDown = () => {
    setIsClicking(true);
  };
  const handleDeclineMouseDown = () => {
    setDeclineClicking(true);
  }

  const handleAcceptMouseUp = () => {
    setIsClicking(false);
    handleInterestClick('check-circle');
  };
  const handleDeclineMouseUp = () => {
    setDeclineClicking(false);
    setInterest(Interested.decline);
    handleInterestClick('x-xircle');
  };

  /**
   * Handles a click on the details button.
   *
   * @returns {void}
   */
  const handleDetailsClick = () => {
    if (setDetailsView) {
      setDetailsView(available_job.id);
    }
  };

  return (
    <JobAvailableCardContainer animate={animate} isInterested={interest} isClicking={isClicking} declineClicking={declineClicking}  >
      <JobAvailableCardSection>
        <div>{available_job.claim_type}</div>
        <div>
          <strong>
          {formatDateTime(available_job.appointment.range_start, available_job.appointment.appointment_name)}
          </strong>
        </div>
      </JobAvailableCardSection>
      <JobAvailableCardSection>
        <div>{getSkillNameById(available_job.skill, skills)}</div>
        <div>
          <strong>{available_job.suburb}</strong>
        </div>
      </JobAvailableCardSection>

      <JobAvailableCardSectionIcons>
     <IconWrapper iconType="map-01">
     <Icon
          type="map-01"
          onClick={handleDetailsClick}
          style={{ cursor: 'pointer' }}
        />
     </IconWrapper>
      <IconWrapper iconType="x-xircle">
      <Icon
          type="x-xircle"
          onMouseDown={handleDeclineMouseDown}
          onMouseUp={handleDeclineMouseUp}
          onMouseLeave={() => setDeclineClicking(false)}
          style={{ cursor: 'pointer' }}
        />
      </IconWrapper>
       
       <IconWrapper iconType="minus-xircle">
       <Icon
          type="minus-xircle"
          onClick={() => handleInterestClick('minus-xircle')}
          style={{ cursor: 'pointer' }}
        />
       </IconWrapper>
       <IconWrapper iconType="check-circle">
       <Icon
          type="check-circle"
          onMouseDown={handleAcceptMouseDown}
          onMouseUp={handleAcceptMouseUp}
          onMouseLeave={() => setIsClicking(false)}
          style={{ cursor: 'pointer' }}
        />
       </IconWrapper>
       
      </JobAvailableCardSectionIcons>
    </JobAvailableCardContainer>
  );
};

/**
 * JobAvailableCardStory displays a single job in a list of available jobs.
 *
 * Properties:
 * - available_job: The job to display.
 * - setDetailsView: A callback to set the details view to true or false.
 * - onInterestExpression: A callback to remove the job from the list.
 *
 * @param {{available_job: IAvailableJob, setDetailsView: (listView: boolean) => void, onInterestExpression: (job_id: number) => void}} props
 * @returns {JSX.Element}
 */
export const JobAvailableCardStory: React.FC = () => {
  const [isClicking] = useState<boolean>(false);
  const [declineClicking] = useState<boolean>(false);
  // const [isClicking, setIsClicking] = useState<boolean>(false);  // Add this line
  // const [interest, setInterest] = useState<Interested>(Interested.ignore);
  // const [animate, setAnimate] = useState<boolean>(false);
  // const [declineClicking, setDeclineClicking] = useState<boolean>(false);
  // const [listView, setListView] = useState(false);
  
  const mockData = [
    {
      address: '24a 8th Avenue, Melville',
      appointment_date: '24 August 2022',
      skill: 'Airconditioning',
      hometype: 'Double Storey House',
      customer: 'Bethany Grace JR Lancaster',
      cellnumber: '0982340989',
      claim_type: 'Home Repair',
      area: 'Victoria, BC',
      time: 'between 10:00 - 12:00 AM',
    },
  ];

  return (
    <JobAvailableCardContainer animate={false} isInterested={Interested.ignore} isClicking={isClicking} declineClicking={declineClicking}>
      <JobAvailableCardSection>
        <JobAvailableCardSectionLineItem>
          {mockData[0].skill}
        </JobAvailableCardSectionLineItem>
        <JobAvailableCardSectionLineItem>
          <strong>{mockData[0].appointment_date} {mockData[0].time}</strong>
        </JobAvailableCardSectionLineItem>
      </JobAvailableCardSection>
      <JobAvailableCardSection>
        <JobAvailableCardSectionLineItem>
          {mockData[0].area}
        </JobAvailableCardSectionLineItem>
        <JobAvailableCardSectionLineItem>
          <strong>{mockData[0].hometype}</strong>
        </JobAvailableCardSectionLineItem>
      </JobAvailableCardSection>

      <JobAvailableCardSectionIcons>
        <JobAvailableCardSectionLineItem>
          <Icon type="map-01" />
        </JobAvailableCardSectionLineItem>
        <JobAvailableCardSectionLineItem>
          <Icon type="minus-xircle" />
        </JobAvailableCardSectionLineItem>
        <JobAvailableCardSectionLineItem>
          <Icon type="check-circle" />
        </JobAvailableCardSectionLineItem>
        <JobAvailableCardSectionLineItem>
          <Icon type="x-xircle" />
        </JobAvailableCardSectionLineItem>
      </JobAvailableCardSectionIcons>
    </JobAvailableCardContainer>
  );
};

import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { ActionConfig, MappedJob, MenuItemConfig } from '../../../Engine/models';
import { ButtonContextMenu } from '../../ContextMenu/ButtonWithContextMenu/ButtonContextMenu';
import { useContextMenuOptions } from '../../ContextMenu/hooks/useContextMenuOptions';
import { Icon } from '../../Icons';
import { withItems } from '../../List/List';
import { MenuItem } from '../../Menu';
import { PermissionsIndicator } from '../PermissionsIndicator/PermissionsIndicator';
import { B64StringLogo } from '../../ClientLogos/DynamicB64StringLogo';

// import { FilterMenuProps } from "./FilterMenu";

interface WorkflowClaimAndJobCardProps {
  jobCardNumberPrefix: string;
  job?: MappedJob;
  LinkRouter: any;
  getMenuItems: (job: any) => { icon: string; label: string; path: string }[];
  callClientAction: (config: ActionConfig) => void;
  notesLink?: string;
}
interface WorkflowClaimCardProps {
  children?: any;
}
interface WorkflowJobCardProps {
  children?: any;
}
interface WorkflowJobCardIndicatorProps {
  children?: any;
}

const WorkflowClaimAndJobCardContainer = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  grid-template-columns: 120px 1fr 2fr;
  border-radius: 4px;
  margin-top: 1px;
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  height: auto;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  box-sizing: border-box;

  @media (max-width: 768px) {
    grid-template-columns: 1fr 2fr;
  }
  @media (max-width: 414px) {
    grid-template-columns: 1fr 2fr;
    // max-width: 414px;
  }
`;

const LogoColumn = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  height: 100%;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
`;
const WorkflowClaimCardContainer = styled(
  ({ ...rest }: WorkflowClaimCardProps) => <div {...rest}></div>
)`
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  border-radius: 4px;

  display: grid;
  grid-template-columns: 1fr;
  width: 100%;
  > :first-child {
  }
  @media (min-width: 768px) and (max-width: 1200px) {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
    > :first-child {
      width: 100%;
      padding-right: 10px;
      box-sizing: border-box;
      // max-height: 100px;
    }
  }
  @media (max-width: 768px) {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;

    > :first-child {
      display: grid;
      //  width: 100%;
      max-height: 100px;
      text-align: center;
    }
  }

  //   ${(props) => props?.theme.ColorsCardColorDashboardError};
  line-height: 1.5;
`;
const WorkflowClaimCard = styled(({ ...rest }: WorkflowClaimCardProps) => (
  <div {...rest}></div>
))`
  display: grid;
  border-radius: 4px;
  grid-template-columns: 1fr;
  padding: 25px 17px 25px 0;
  line-heigth: 1.5;
  > :nth-child(2) {
    align-self: center;
  }
  > :nth-child() {
    min-width: 175px;
  }
  > :first-child {
    align-self: center;
  }
  @media (max-width: 1200px) and (min-width: 768px) {
  }
  @media (min-width: 1200px) {
    border-left: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  }
  @media (max-width: 768px) {
    padding: 25px 17px 25px 0;

    // grid-template-columns: 1fr;
    grid-template-columns: 0.1fr 1.5fr 3.5fr;
  }
  @media (max-width: 414px) {
    padding: 25px 12px 25px 0;
    grid-template-columns: 0.1fr 4fr 1fr;
    // max-width: 414px;
  }
`;
const WorkflowClaimCardActions = styled(({ ...rest }) => <div {...rest}></div>)`
  // padding-top: 25px;
  align-self: center;
`;

const WorkflowClaimCardDetails = styled(({ ...rest }) => <div {...rest}></div>)`
  display: grid;
  grid-auto-rows: min-content;
  align-self: top;
  line-height: 1.5;
  grid-gap: 2px;
  font-size: smaller;
  text-align: right;
  //   background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
`;
const WorkflowJobCardContainer = styled(({ ...rest }) => <div {...rest}></div>)`
  display: grid;
  border-left: solid 1px ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  grid-template-rows: auto;
  > :first-child {
    border-top-right-radius: 4px;
  }
  > div:last-child {
    border-bottom: none;
    border-bottom-right-radius: 4px;
    align-self: center;
  }
  @media (max-width: 414px) {
    display: grid;
    width: 100%;
    // max-width: 768px;
    grid-template-rows: auto;
    > :first-child {
      border-top-right-radius: 0px;
    }
    > :last-child {
      border-bottom-right-radius: 0px;
      border: none;
      align-self: center;
    }
  }
`;

const WorkflowJobCard = styled(({ ...rest }: WorkflowJobCardProps) => (
  <div {...rest}></div>
))`
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  display: grid;
  box-sizing: border-box;
  grid-template-columns: 0.1fr 1.3fr 1fr 1.4fr 1fr;
  padding: 0 0 0 0;
  height: auto;
  align-items: center;
  width: 100%;

  // border-bottom: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke};
  // > :last-child {
  //   border-bottom: none;
  //   align-self: center;
  // }

  @media (min-width: 1200px) {
    display: grid;
    grid-template-columns: 0.1fr 1.5fr 1.3fr 1fr 0.5fr 0.5fr;
    > :nth-child(5) {
      align-self: center;
      justify-self: center;
    }
  }
  @media (max-width: 768px) {
    display: grid;
    width: 100%;
    max-width: 768px;
    grid-template-columns: 0.1fr 1fr 1fr 0.3fr;
  }
  @media (max-width: 414px) {
    display: grid;
    width: 100%;
    // max-width: 768px;
    grid-template-rows: auto;
    padding: 10px 0 10px 0;
  }
`;

const WorkflowJobCardIndicator = styled(
  ({ children, ...rest }: WorkflowJobCardIndicatorProps) => (
    <div {...rest}>{children}</div>
  )
)`
  align-content: center;
  justify-self: flex-start;
  // border-left: 1px solid black;
  width: 6px;
  height: 100%;
  children: any;
`;

const WorkflowJobCardSection = styled(({ ...rest }) => <div {...rest}></div>)`
  // display: flex;
  // flex-direction: column;
  // background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary};
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  padding: 15px 0 15px 15px;
  line-height: 1.5;

  @media (max-width: 1560px) {
    // width: 100%;
    max-width: 200px;
  }
  @media (max-width: 768px) {
    // width: 100%;
    max-width: 120px;
  }
`;

const WorkflowMobileJobcardSection = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  display: grid;
  grid-template-columns: 0.1fr 0.2fr 1fr;
  // max-height: 20px;
  // background:blue;
  border-bottom: 1px solid ${(props) => props?.theme.ColorsInputsPrimaryStroke}; 
  padding: 10px 10px 10px 0;
  align-self: center;
`;

const WorkflowJobCardNotesAndCMenuSection = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  display: grid;
  grid-template-columns: 0.5fr 0.7fr;
  > :first-child {
    align-self: center;
    justify-self: center;
    padding: 0px 10px 0 10px;
    cursor: pointer;
  }
  @media (min-width: 768px) and (max-width: 1200px) {
    > :last-child {
      padding-right: 17px;
    }
  }

  @media (max-width: 768px) {
    > :last-child {
      padding-left: 22px;
    }
  }
`;

// FUNCTIONS =================

/**
 * A hook that returns the current window size and updates when the window is resized.
 *
 * @returns {{width: number, height: number}} The current window size.
 */
const useScreenSize = () => {
  const [screenSize, setScreenSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });
  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  return screenSize;
};

const Expander = styled.div`
  width: 100%;
  height: 100%;
  background: ${(props) => props?.theme.ColorsCardColorCalendarSuccess};
  align-content: center;
  text-align: center;
  border-radius: 0 4px 4px 0;
`;

const WorkflowJobCardText = styled.div`
  overflow: hidden;
  font-weight: 200;
  font-size: 0.8rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
`;

const NotesIconButton = styled.button`
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 36px;
  height: 36px;

  &:hover {
    background-color: rgba(40, 48, 51, 0.84);
    color: unset;
  }
`;

// Add this helper function near the top of the file, after the imports
const handleNavigation = (url: string, navigate: any) => {
  console.log('notesLink', url);
  if (url.startsWith('http')) {
    window.open(url, '_blank');
  } else {
    console.log('....................>')
    navigate(url);
  }
};

const IsolatedContextMenuWrapper = styled.div`
  isolation: isolate;
  position: relative;
  z-index: 1000;
  
  .job-card-dropdown {
    isolation: isolate;
    position: absolute;
    right: 0;
    top: 100%;
    z-index: 1001;
    background-color: rgba(40, 48, 51, 0.84);
    border-radius: 4px;
    box-shadow: 0px 12px 23px 5px rgba(7, 14, 17, 0.25);
    backdrop-filter: blur(9px);
    border: 0.5px solid #696969;

    &:hover {
      background-color: rgba(40, 48, 51, 0.93);
    }
  }
`;


/**
 * @function WorkflowClaimAndJobCard
 * @description This component renders a card containing a claim and its associated jobs.
 * @param {object} claim - The claim object.
 * @returns {ReactElement} The rendered card.
 * @example
 * <WorkflowClaimAndJobCard claim={claim} />P
 */

export const JobCardNormalSize: React.FC<WorkflowClaimAndJobCardProps> = ({
  job,
  jobCardNumberPrefix,
  LinkRouter,
  callClientAction,
  getMenuItems, // icon, label, path
  notesLink,
}) => {
  const screenSize = useScreenSize();
  const navigate = useNavigate();
  const isMobile = screenSize.width < 414;
  const isScreenSize768 = screenSize.width <= 769 && screenSize.width >= 414;
  const isScreenSize769Plus =
    screenSize.width >= 769 && screenSize.width <= 1200;
  const isScreenSize769PlusExtra = screenSize.width > 1200;

  const WithMultipleItems = (): JSX.Element[] => {
    const { showMenu, setShowMenu } = useContextMenuOptions();
    const items: MenuItemConfig[] = getMenuItems(job)?.map((item: any) => ({
      icon: item.icon,
      label: item.label,
      onClick: async (event: React.MouseEvent) => {
        event.stopPropagation();
        if (item.path) {
          navigate(item.path);
        } else if (item.onClick) {
          for (const cf of item.onClick) {
            await callClientAction(cf);
          }
        }
        if (setShowMenu) {
          setShowMenu(false);
        }
      },
      onMouseOver: (event: React.MouseEvent) => {
        event.stopPropagation();
      },
      onMouseEnter: (event: React.MouseEvent) => {
        event.stopPropagation();
      },
    }));

    return withItems(MenuItem)(items);
  };

  const logoSource = (job?.claim && 'source' in job.claim ? (job.claim as any).source : undefined) || job?.source;

  if (isScreenSize768) {
    return (
      <WorkflowClaimAndJobCardContainer data-testid="workflow-claim-and-job-card">
        
        {logoSource && (
          <LogoColumn>
            <B64StringLogo id_string={logoSource} maximumWidth={100} minimumWidth={80} />
          </LogoColumn>
        )}
        <WorkflowClaimCardContainer>
          <WorkflowClaimCard>
            <WorkflowClaimCardDetails>
              <div style={{ fontWeight: 'bold' }}>{job?.customer}</div>
              <div>{job?.suburb}</div>
              <div>
                {jobCardNumberPrefix}: {job?.claim?.mid}
              </div>
              <div style={{ fontSize: '0.75rem', color: '#bdbdbd' }}>{job?.stateTextDisplay}</div>
            {/* <div style={{ fontSize: '0.75rem', color: '#bdbdbd' }}>{job?.timeRemaining}</div> */}
            </WorkflowClaimCardDetails>
          </WorkflowClaimCard>
        </WorkflowClaimCardContainer>
        <WorkflowJobCardContainer>
          <LinkRouter job={job}>
            <WorkflowJobCard>
              <WorkflowJobCardIndicator>
                {job?.permissionGranted && (
                  <PermissionsIndicator
                    color={'green'}
                    size=""
                    position=""
                  ></PermissionsIndicator>
                )}
              </WorkflowJobCardIndicator>
              <WorkflowJobCardSection>
                <WorkflowJobCardText>{job?.skillName}</WorkflowJobCardText>
                <WorkflowJobCardText>{job?.teamleaderName}</WorkflowJobCardText>
                <WorkflowJobCardText>
                  {job?.stateTextDisplay}
                </WorkflowJobCardText>
                {/* <WorkflowJobCardText>{job?.timeRemaining}</WorkflowJobCardText> */}
              </WorkflowJobCardSection>
              <WorkflowJobCardSection>
                <WorkflowJobCardText>
                  <div>{job?.formattedDate}</div>
                </WorkflowJobCardText>
                <WorkflowJobCardText>
                  <div>{job?.appointmentTime}</div>
                </WorkflowJobCardText>
                <WorkflowJobCardText>
                  {job?.stateTextDisplay}
                </WorkflowJobCardText>
                {/* <WorkflowJobCardText>{job?.description}</WorkflowJobCardText> */}
              </WorkflowJobCardSection>

              <WorkflowJobCardNotesAndCMenuSection>
                {(job?.note_count || 0) > 0 && (
                  <NotesIconButton onClick={(e) => {
                    e.stopPropagation();
                    if (job?.notesLink) {
                      handleNavigation(job?.notesLink, navigate);
                    }
                  }}>
                    <Icon type="notes" size={20} />
                  </NotesIconButton>
                )}
                <WorkflowJobCardSection>
                  <IsolatedContextMenuWrapper>
                    <ButtonContextMenu
                      children={<WithMultipleItems />}
                      orientation="right"
                      additionalStyling="&:hover {background-color: transparent; color: unset;}"
                      dropdownClassName="job-card-dropdown"
                    />
                  </IsolatedContextMenuWrapper>
                </WorkflowJobCardSection>
              </WorkflowJobCardNotesAndCMenuSection>
            </WorkflowJobCard>
          </LinkRouter>
        </WorkflowJobCardContainer>
      </WorkflowClaimAndJobCardContainer>
    );
  } else if (isMobile === true && !isScreenSize768) {
    return (
      <WorkflowClaimAndJobCardContainer>
        {/* LOGO SECTION */}
        {logoSource && (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', background: 'transparent', minWidth: 80 }}>
            <B64StringLogo id_string={logoSource} maximumWidth={80} minimumWidth={60} />
          </div>
        )}
        <WorkflowClaimCard>
          <WorkflowClaimCardDetails>
            <WorkflowJobCardText>
              {/* <div style={{ fontWeight: 'bold' }}>Xitshembiso Warrant Shondlani</div> */}
              <div>{job?.customer}</div>
            </WorkflowJobCardText>

            <div>{job?.suburb}</div>
            <div>
              {jobCardNumberPrefix}: {job?.claim?.mid}
            </div>
          </WorkflowClaimCardDetails>
        </WorkflowClaimCard>
        <WorkflowJobCardContainer>
          <LinkRouter job={job}>
            <WorkflowMobileJobcardSection>
              <WorkflowJobCardIndicator>
                {job?.permissionGranted && (
                  <PermissionsIndicator
                    color={'green'}
                    size=""
                    position=""
                  ></PermissionsIndicator>
                )}
              </WorkflowJobCardIndicator>
              <WorkflowClaimCardActions>
                {/* <Icon type="dots-vertical" /> */}
                <IsolatedContextMenuWrapper>
                  <ButtonContextMenu
                    children={<WithMultipleItems />}
                    orientation="right"
                    additionalStyling="&:hover {background-color: transparent; color: unset;}"
                    dropdownClassName="job-card-dropdown"
                  />
                </IsolatedContextMenuWrapper>
              </WorkflowClaimCardActions>
              <div style={{ display: 'grid', gridTemplateRows: '1fr' }}>
                <WorkflowJobCardText>
                  <div style={{ fontWeight: 'bold' }}> {job?.skillName}</div>
                </WorkflowJobCardText>
                <WorkflowJobCardText>{job?.spName}</WorkflowJobCardText>
                {/* <WorkflowJobCardText>{job?.description}</WorkflowJobCardText> */}
                <WorkflowJobCardText>
                  <div style={{ fontWeight: 'bold' }}> {job?.formattedDate}</div>
                </WorkflowJobCardText>
                <WorkflowJobCardText>
                  <div> {job?.appointmentTime}</div>
                </WorkflowJobCardText>
              </div>
            </WorkflowMobileJobcardSection>
          </LinkRouter>
        </WorkflowJobCardContainer>
        <Expander>
          <Icon type="chevron-down" />
        </Expander>
      </WorkflowClaimAndJobCardContainer>
    );
  } else if (isScreenSize769Plus) {
    return (
      <WorkflowClaimAndJobCardContainer>
        {/* LOGO SECTION */}
        {logoSource && (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', background: 'transparent', minWidth: 120 }}>
            <B64StringLogo id_string={logoSource} maximumWidth={100} minimumWidth={80} />
          </div>
        )}
        <WorkflowClaimCardContainer>
          <WorkflowClaimCard>
            <WorkflowClaimCardDetails>
              <div style={{ fontWeight: 'bold' }}>{job?.customer}</div>
              <div>{job?.suburb}</div>
              <div>
                {jobCardNumberPrefix}: {job?.claim?.mid}
              </div>
            </WorkflowClaimCardDetails>
          </WorkflowClaimCard>
        </WorkflowClaimCardContainer>

        <WorkflowJobCardContainer>
          <LinkRouter job={job}>
            <WorkflowJobCard>
              <WorkflowJobCardIndicator>
                {job?.permissionGranted && (
                  <PermissionsIndicator
                    color={'green'}
                    size=""
                    position=""
                  ></PermissionsIndicator>
                )}
              </WorkflowJobCardIndicator>
              <WorkflowJobCardSection>
                <WorkflowJobCardText>{job?.skillName}</WorkflowJobCardText>
                <WorkflowJobCardText>{job?.teamleaderName}</WorkflowJobCardText>
              </WorkflowJobCardSection>
              <WorkflowJobCardSection>
                <WorkflowJobCardText>
                  <div>{job?.formattedDate}</div>
                </WorkflowJobCardText>
                <WorkflowJobCardText>
                  <div>{job?.appointmentTime}</div>
                </WorkflowJobCardText>
              </WorkflowJobCardSection>
              <WorkflowJobCardSection>
                <WorkflowJobCardText>
                  {job?.stateTextDisplay}
                </WorkflowJobCardText>
                {/* <WorkflowJobCardText>{job?.description}</WorkflowJobCardText> */}
              </WorkflowJobCardSection>
              {/* <WorkflowJobCardSection>
                    <WorkflowJobCardText>
                      <div>{job.customer}</div>
                      <div style={{ color: 'red', fontWeight: 'bold' }}>
                        {job.sla}
                      </div>
                    </WorkflowJobCardText>
                  </WorkflowJobCardSection> */}
              <WorkflowJobCardNotesAndCMenuSection>
                {(job?.note_count || 0) > 0 && (
                  <NotesIconButton onClick={(e) => {
                    e.stopPropagation();
                    if (job?.notesLink) {
                      handleNavigation(job?.notesLink, navigate);
                    }
                  }}>
                    <Icon type="notes" size={20} />
                  </NotesIconButton>
                )}
                <WorkflowJobCardSection>
                  <IsolatedContextMenuWrapper>
                    <ButtonContextMenu
                      children={<WithMultipleItems />}
                      orientation="right"
                      additionalStyling="&:hover {background-color: transparent; color: unset;}"
                      dropdownClassName="job-card-dropdown"
                    />
                  </IsolatedContextMenuWrapper>
                </WorkflowJobCardSection>
              </WorkflowJobCardNotesAndCMenuSection>
            </WorkflowJobCard>
          </LinkRouter>
        </WorkflowJobCardContainer>
      </WorkflowClaimAndJobCardContainer>
    );
  } else if (isScreenSize769PlusExtra) {
    return (
      <WorkflowClaimAndJobCardContainer>
        {/* LOGO SECTION */}
        {logoSource && (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', background: 'transparent', minWidth: 120 }}>
            <B64StringLogo id_string={logoSource} maximumWidth={100} minimumWidth={80} />
          </div>
        )}
        <WorkflowClaimCardContainer>
          <WorkflowClaimCard>
            <WorkflowClaimCardDetails>
              <div style={{ fontWeight: 'bold' }}>{job?.customer}</div>
              <div>{job?.suburb}</div>
              <div>
                {jobCardNumberPrefix}: {job?.claim?.mid}
              </div>
            </WorkflowClaimCardDetails>
          </WorkflowClaimCard>
        </WorkflowClaimCardContainer>

        <WorkflowJobCardContainer>
          <LinkRouter job={job}>
            <WorkflowJobCard>
              <WorkflowJobCardIndicator>
                {job?.permissionGranted && (
                  <PermissionsIndicator
                    color={'green'}
                    size=""
                    position=""
                  ></PermissionsIndicator>
                )}
              </WorkflowJobCardIndicator>
              <WorkflowJobCardSection>
                <WorkflowJobCardText>{job?.skillName}</WorkflowJobCardText>
                <WorkflowJobCardText>{job?.teamleaderName}</WorkflowJobCardText>
              </WorkflowJobCardSection>
              <WorkflowJobCardSection>
                <WorkflowJobCardText>
                  <div>{job?.formattedDate}</div>
                </WorkflowJobCardText>
                <WorkflowJobCardText>
                  <div>
                    {job?.appointmentType} {job?.appointmentTime}
                  </div>
                </WorkflowJobCardText>
              </WorkflowJobCardSection>
              <WorkflowJobCardSection>
                <WorkflowJobCardText>
                  {job?.stateTextDisplay}
                </WorkflowJobCardText>
                {/* <WorkflowJobCardText>{job?.description}</WorkflowJobCardText> */}
              </WorkflowJobCardSection>
              {/* <WorkflowJobCardSection>
                    <WorkflowJobCardText>
                      <div>{job.customer}</div>
                      <div style={{ color: 'red', fontWeight: 'bold' }}>
                        {job.sla}
                      </div>
                    </WorkflowJobCardText>
                  </WorkflowJobCardSection> */}
              <WorkflowJobCardSection>
                {(job?.note_count || 0) > 0 && (
                  <NotesIconButton onClick={(e) => {
                    e.stopPropagation();
                    if (job?.notesLink) {
                      handleNavigation(job?.notesLink, navigate);
                    }
                  }}>
                    <Icon type="notes" size={20} />
                  </NotesIconButton>
                )}
              </WorkflowJobCardSection>
              <WorkflowJobCardSection>
                {/* <Icon type="dots-vertical" /> */}
                <IsolatedContextMenuWrapper>
                  <ButtonContextMenu
                    children={<WithMultipleItems />}
                    orientation="right"
                    additionalStyling="&:hover {background-color: transparent; color: unset;}"
                    dropdownClassName="job-card-dropdown"
                  />
                </IsolatedContextMenuWrapper>
              </WorkflowJobCardSection>
            </WorkflowJobCard>
          </LinkRouter>
        </WorkflowJobCardContainer>
      </WorkflowClaimAndJobCardContainer>
    );
  }
};

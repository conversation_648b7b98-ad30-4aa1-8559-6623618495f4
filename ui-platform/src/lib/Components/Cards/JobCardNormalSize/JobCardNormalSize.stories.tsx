/* eslint-disable @typescript-eslint/no-explicit-any */
import type { Meta, StoryObj } from '@storybook/react';
import { JobCardNormalSize } from './JobCardNormalSize';
import { sampleJobs } from '../../../Screens/WorkflowModule/JobsOnly/JobsDetailedView/sample-jobs';

const meta: Meta<typeof JobCardNormalSize> = {
  component: JobCardNormalSize,
  title: 'Components/Cards/JobCardNormalSize',
};
export default meta;

type Story = StoryObj<typeof JobCardNormalSize>;

const job = { ...sampleJobs[0],
  permissionGranted: true,
  claim: {
    ...sampleJobs[0].claim,
    source: 'kingprice', 
  },
  // source: 'absa', 
};

export const DefaultJobCardNormalSize: Story = {
  args: {
    job,
    LinkRouter: ({ children }: {children: any}) => children,
    getMenuItems: (job: any) => [
      { icon: 'file-07', label: 'View Details', path: `/jobs/${job.id}` },
      { icon: 'pencil-01', label: 'Edit Job', path: `/jobs/${job.id}/edit` },
      { icon: 'alert-diamond', label: 'Delete Job', path: `/jobs/${job.id}/delete` }
    ],
    callClientAction: (config: any) => console.log('Client action called:', config),
    jobCardNumberPrefix: 'Job',
    notesLink: `https://daily.dev/`,
  },
};

import React from 'react';
import styled from 'styled-components';
import { B64StringLogo } from '../../ClientLogos/DynamicB64StringLogo';
import { PermissionsIndicator } from '../PermissionsIndicator/PermissionsIndicator';
import useViewportDevice from '../../../Hooks/useViewportDevice';
import { ButtonContextMenu, useContextMenuOptions } from '../../ContextMenu';
import { Icon } from '../../Icons';
import { useNavigate } from 'react-router-dom';
import { MenuItemConfig } from '../../../Engine/models/menu-list-item.config';
import { withItems } from '../../List/List';
import { MenuItem } from '../../Menu';
import { MappedClaim } from '../../../Engine/models/mapped-claim';

interface ClaimCardProps {
  claim?: MappedClaim;
  ClaimLinkRouter: any;
  displayLogo?: boolean;
  getClaimMenuItems: (claim: any) => {icon: string; label: string; path: string}[];
}

const ClaimCardContainer = styled.div`
  display: grid;
  grid-template-columns: 1.2fr 2fr;
  width: 100%;
  min-width: auto;
  line-height: 1.2;
  cursor: pointer;
`;

const ClaimCardContent = styled.div`
  display: grid;
  grid-template-columns: 0.08fr 1fr 0.4fr;
  padding: 8px 8px 8px 0;
  line-height: 1.2;
  min-height: 36px;
  align-items: center;
  transition: background 0.2s, box-shadow 0.2s;
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  > :nth-child(2) {
    align-self: center;
  }
  > :first-child {
    align-self: center;
  }
`;

const ClaimCardDetails = styled.div`
  display: flex;
  flex-direction: column;
  align-self: center;
  line-height: 1.2;
  gap: 2px;
  text-align: right;
  padding: 4px 8px;
`;

const ClaimCardNotesAndCMenuSection = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;

  > :first-child {
    justify-self: end;
  }

  > :last-child {
    justify-self: start;
    padding-right: 2rem;
  }
`;

const JobCardText = styled.div`
  overflow: hidden;
  font-weight: 200;
  font-size: 0.72rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
`;

const ClaimCardActions = styled.div`
  align-self: center;
`;

const WithMultipleItems = ({ menuItems, navigate, setShowMenu }: any) => {
    const items: MenuItemConfig[] = menuItems?.map((item: any) => ({
      icon: item.icon,
      label: item.label,
      onClick: (event: React.MouseEvent) => {
        event.stopPropagation();
        if (item.path) {
          navigate(item.path);
        }
        if (setShowMenu) {
          setShowMenu(false);
        }
      },
    }));
    return withItems(MenuItem)(items);
  };

export const ClaimSection: React.FC<ClaimCardProps> = ({
  claim,
  ClaimLinkRouter,
  displayLogo,
  getClaimMenuItems,
}) => {
  const logoSource = claim?.jobs?.[0]?.source || claim?.source;
  const navigate = useNavigate();
  const { showMenu, setShowMenu } = useContextMenuOptions();

  // Mocked or derived fields for demo (replace with real claim fields as needed)
  const claimType = 'Geyser Only -- TODO'; // claim?.type 
  const reference = claim?.mid;
  const customer = claim?.customer;
  const stateTextDisplay = claim?.stateTextDisplay;
  const timeRemaining = '1d 03h 25m -- TODO'; // claim?.timeRemaining
  const teamleadName = 'John Doe -- TODO';
  const spName = 'Big Joe-- TODO';

  return (
    <ClaimCardContainer>
      <ClaimLinkRouter claim={claim}>
        {logoSource && displayLogo && (
          <B64StringLogo
            id_string={logoSource}
            maximumWidth={100}
            minimumWidth={135}
          />
        )}
        <ClaimCardContent>
          <ClaimCardNotesAndCMenuSection>
            <ButtonContextMenu
              children={<WithMultipleItems menuItems={getClaimMenuItems(claim)} navigate={navigate} setShowMenu={setShowMenu} />}
              orientation="right"
              additionalStyling="&:hover {background-color: rgba(40, 48, 51, 0.84); color: unset;}"
              dropdownClassName="job-card-dropdown"
            />
            <div>
              <Icon type="notes" size={20} />
            </div>
          </ClaimCardNotesAndCMenuSection>
          {claim?.permissionGranted && (
            <PermissionsIndicator color={'blue'} size="" position="" />
          )}
          <ClaimCardDetails>
            <div style={{ fontSize: '0.8rem', color: '#bdbdbd' }}>{claimType} <span style={{ color: '#fff' }}>{reference}</span></div>
            <div style={{ fontSize: '0.75rem', color: '#bdbdbd' }}>{customer}</div>
            <div style={{ fontSize: '0.75rem', color: '#bdbdbd' }}>{stateTextDisplay}</div>
            <div style={{ fontSize: '0.75rem', color: '#bdbdbd' }}>{timeRemaining}</div>
          </ClaimCardDetails>
        </ClaimCardContent>
      </ClaimLinkRouter>
    </ClaimCardContainer>
  );
};
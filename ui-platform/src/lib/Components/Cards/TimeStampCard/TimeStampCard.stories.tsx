import type { Meta, StoryObj } from '@storybook/react';
import { TimeStampCard } from './TimeStampCard';

const meta: Meta<typeof TimeStampCard> = {
  title: 'Components/Cards/TimeStampCard',
  component: TimeStampCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    dateTime: {
      control: 'text',
      description: 'The timestamp in ISO format',
    },
    modifier: {
      control: 'text',
      description: 'The user who made the change',
    },
    state: {
      control: 'text',
      description: 'The state that was changed',
    },
    stateDescription: {
      control: 'text',
      description: 'Description of the state change',
    },
    reason: {
      control: 'text',
      description: 'Optional reason for the change',
    },
    indentLevel: {
      control: { type: 'select' },
      options: [1, 2],
      description: 'Indentation level for the card',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    dateTime: '2024-01-15T10:30:00Z',
    modifier: '<PERSON>',
    state: 'Status',
    stateDescription: 'Changed from Pending to Approved',
    reason: 'Claim approved after review',
    indentLevel: 1,
  },
};

export const WithReason: Story = {
  args: {
    dateTime: '2024-01-15T14:45:00Z',
    modifier: 'Jane Smith',
    state: 'Priority',
    stateDescription: 'Changed from Normal to High',
    reason: 'Urgent customer request',
    indentLevel: 1,
  },
};

export const WithoutReason: Story = {
  args: {
    dateTime: '2024-01-15T09:15:00Z',
    modifier: 'Admin User',
    state: 'Assigned',
    stateDescription: 'Assigned to Team A',
    indentLevel: 1,
  },
};

export const IndentedLevel2: Story = {
  args: {
    dateTime: '2024-01-15T16:20:00Z',
    modifier: 'System',
    state: 'Auto-Update',
    stateDescription: 'Automatic status update',
    reason: 'Scheduled maintenance',
    indentLevel: 2,
  },
};

export const RecentTimestamp: Story = {
  args: {
    dateTime: new Date().toISOString(),
    modifier: 'Current User',
    state: 'Last Modified',
    stateDescription: 'Updated claim details',
    reason: 'Added additional documentation',
    indentLevel: 1,
  },
}; 
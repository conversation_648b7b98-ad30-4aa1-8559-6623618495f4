import React from 'react';
import styled from 'styled-components';
import moment from 'moment';

interface TimeStampCardProps {
  dateTime: string;
  modifier: string;
  state: string;
  stateDescription: string;
  reason?: string;
  indentLevel?: 1 | 2;
}

const TimeStampCardContainer = styled.div`
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: ${(props) => props?.theme?.RadiusXxs || '4px'};
  background: ${(props) => props?.theme?.ColorsBackgroundShell || '#f8f9fa'};
  border: 1px solid ${(props) => props?.theme?.ColorsInputsInverse || '#e9ecef'};
`;

const TimeStampInfo = styled.div<{ indentLevel: number }>`
  margin-left: ${(props) => props.indentLevel * 1}rem;
  color: ${(props) => props?.theme?.ColorsTypographyPrimary || '#333'};
  font-size: 0.875rem;
  line-height: 1.4;
`;

const TimeStampHeader = styled(TimeStampInfo)`
  font-weight: 600;
  color: ${(props) => props?.theme?.ColorsTypographyPrimary || '#333'};
  margin-bottom: 0.25rem;
`;

const TimeStampState = styled(TimeStampInfo)`
  color: ${(props) => props?.theme?.ColorsTypographySecondary || '#666'};
  margin-bottom: 0.25rem;
`;

const TimeStampReason = styled(TimeStampInfo)`
  color: ${(props) => props?.theme?.ColorsTypographySecondary || '#666'};
  font-style: italic;
`;

const GlowLine = styled.div`
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    ${(props) => props?.theme?.ColorsInputsInverse || '#e9ecef'} 50%,
    transparent 100%
  );
  margin: 1rem 0;
`;

export const TimeStampCard: React.FC<TimeStampCardProps> = ({
  dateTime,
  modifier,
  state,
  stateDescription,
  reason,
  indentLevel = 1,
}) => {
  const formattedDateTime = moment(dateTime).format('YYYY-MM-DD HH:mm');

  return (
    <TimeStampCardContainer>
      <TimeStampHeader indentLevel={indentLevel}>
        {formattedDateTime} - {modifier}
      </TimeStampHeader>
      <TimeStampState indentLevel={indentLevel + 1}>
        {state}: {stateDescription}
      </TimeStampState>
      {reason && (
        <TimeStampReason indentLevel={indentLevel + 1}>
          {reason}
        </TimeStampReason>
      )}
      <GlowLine />
    </TimeStampCardContainer>
  );
}; 
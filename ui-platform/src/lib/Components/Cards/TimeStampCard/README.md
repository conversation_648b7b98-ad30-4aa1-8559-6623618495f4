# TimeStampCard Component

A React component for displaying individual time stamp entries for claims and jobs. This component follows the existing design patterns in the UI platform and provides a consistent way to display state changes with timestamps, modifiers, and optional reasons.

## Features

- Displays timestamp in a formatted date/time format
- Shows the user who made the change (modifier)
- Displays state changes with descriptions
- Optional reason field for additional context
- Configurable indentation levels
- Themed styling that matches the platform design system
- Responsive design with hover effects

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `dateTime` | `string` | Yes | - | The timestamp in ISO format |
| `modifier` | `string` | Yes | - | The user who made the change |
| `state` | `string` | Yes | - | The state that was changed |
| `stateDescription` | `string` | Yes | - | Description of the state change |
| `reason` | `string` | No | - | Optional reason for the change |
| `indentLevel` | `1 \| 2` | No | `1` | Indentation level for the card |

## Usage

```tsx
import { TimeStampCard } from '@your-org/ui-platform';

// Basic usage
<TimeStampCard
  dateTime="2024-01-15T10:30:00Z"
  modifier="John Doe"
  state="Status"
  stateDescription="Changed from Pending to Approved"
  reason="Claim approved after review"
/>

// Without reason
<TimeStampCard
  dateTime="2024-01-15T09:15:00Z"
  modifier="Admin User"
  state="Assigned"
  stateDescription="Assigned to Team A"
/>

// With custom indentation
<TimeStampCard
  dateTime="2024-01-15T16:20:00Z"
  modifier="System"
  state="Auto-Update"
  stateDescription="Automatic status update"
  reason="Scheduled maintenance"
  indentLevel={2}
/>
```

## Styling

The component uses styled-components and follows the platform's theming system. It automatically adapts to the current theme and provides:

- Consistent spacing and typography
- Hover effects for interactive elements
- Responsive design
- Accessible color contrast
- Glow line separator for visual separation

## Data Format

The component expects the `dateTime` prop to be in ISO 8601 format (e.g., "2024-01-15T10:30:00Z"). The component will format this for display using the `moment` library.

## Accessibility

- Proper semantic HTML structure
- ARIA labels for interactive elements
- Keyboard navigation support
- High contrast color schemes
- Screen reader friendly text structure

## Related Components

- `TimeStampList` - Container component for multiple time stamp cards
- `TimeStamps` - Fragment component for complete time stamps functionality 
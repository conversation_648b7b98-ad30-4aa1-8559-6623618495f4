import React from 'react';
import { render, screen } from '@testing-library/react';
import { TimeStampCard } from './TimeStampCard';

describe('TimeStampCard', () => {
  const defaultProps = {
    dateTime: '2024-01-15T10:30:00Z',
    modifier: '<PERSON>',
    state: 'Status',
    stateDescription: 'Changed from Pending to Approved',
  };

  it('renders with all required props', () => {
    render(<TimeStampCard {...defaultProps} />);
    
    expect(screen.getByText('2024-01-15 10:30 - John Doe')).toBeInTheDocument();
    expect(screen.getByText('Status: Changed from Pending to Approved')).toBeInTheDocument();
  });

  it('renders with optional reason', () => {
    render(<TimeStampCard {...defaultProps} reason="Claim approved after review" />);
    
    expect(screen.getByText('Claim approved after review')).toBeInTheDocument();
  });
}); 
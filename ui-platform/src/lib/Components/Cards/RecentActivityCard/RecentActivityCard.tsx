import React from 'react';
import styled from 'styled-components';
import moment from 'moment';

interface RecentActivityCardProps {
    claimnum: string;        // Could be claim_num
    applicant: string;  // Could be applicant
    access_date: string;     // Changed from date to access_date to match the moment usage
    onClick?: () => void; // Handler for the card click
  }
  
const RecentActivityCardContainer = styled(({ ...rest }) => <div {...rest}></div>)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  padding: 0.5rem;
  max-width: 320px;
  box-sizing: border-box;
  cursor: pointer;
//   font-size: ${(props) => props?.theme?.FontSize3}px;
  font-size: 0.7rem;
  margin-top: -0.3rem;
  color: ${(props) => props?.theme?.ColorsTypographyPrimary};
  border-radius: ${(props) => props?.theme?.RadiusXxs || '4px'};
  background: ${(props) => props?.theme?.ColorsBackgroundShell};
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background: ${(props) => props?.theme?.ColorsButtonColorActionPanelHover || '#f5f5f5'};
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: ${(props) => props?.theme?.RadiusXsg || '4px'};
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
`;

const ClaimNumContainer = styled(({ ...rest }) => <div {...rest}></div>)`
  grid-column: 1;
  grid-row: 1;
  font-weight: bold;
  color: ${(props) => props?.theme?.ColorsUtilityColorFocus};
`;

const ApplicantContainer = styled(({ ...rest }) => <div {...rest}></div>)`
  grid-column: 2;
  grid-row: 1;
  color: ${(props) => props?.theme?.ColorsTypographyPrimary};
`;

const ClaimAccessDate = styled(({ ...rest }) => <div {...rest}></div>)`
  grid-column: 1;
  grid-row: 2;
  color: ${(props) => props?.theme?.ColorsTypographySecondary || '#666'};
  font-size: 0.65rem;
`;

export const RecentActivityCard: React.FC<RecentActivityCardProps> = ({
  claimnum,
  applicant,
  access_date,
  onClick,
}) => {
  return (
    <RecentActivityCardContainer onClick={onClick}>
      <ClaimNumContainer>{claimnum}</ClaimNumContainer>
      <ApplicantContainer>{applicant}</ApplicantContainer>
      <ClaimAccessDate>
        {moment(access_date)?.calendar()}
      </ClaimAccessDate>
    </RecentActivityCardContainer>
  );
};
import type { Meta, StoryObj } from '@storybook/react';
import { RecentActivityCard } from './RecentActivityCard';

const meta: Meta<typeof RecentActivityCard> = {
  title: 'Components/Cards/RecentActivityCard',
  component: RecentActivityCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    claimnum: {
      control: 'text',
      description: 'The title of the activity (e.g., claim number)',
    },
    applicant: {
      control: 'text',
      description: 'The description of the activity (e.g., applicant name)',
    },
    access_date: {
      control: 'text',
      description: 'The access date of the activity (will be formatted with moment.js calendar)',
    },
    onClick: {
      action: 'clicked',
      description: 'Handler for card click events',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story with basic data
export const Default: Story = {
  args: {
    claimnum: 'CLAIM-2024-001',
    applicant: '<PERSON>',
    access_date: '2024-01-15T14:30:00Z',
  },
};

// Story with long text to test layout
export const LongText: Story = {
  args: {
    claimnum: 'CLAIM-2024-001234567890',
    applicant: 'Dr. <PERSON>-<PERSON>',
    access_date: '2024-01-15T14:30:00Z',
  },
};

// Story with recent activity (today)
export const RecentActivity: Story = {
  args: {
    claimnum: 'CLAIM-2024-003',
    applicant: 'Michael Brown',
    access_date: new Date().toISOString(),
  },
};

// Story with yesterday activity
export const YesterdayActivity: Story = {
  args: {
    claimnum: 'CLAIM-2024-004',
    applicant: 'Sarah Wilson',
    access_date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
  },
};

// Story with older activity (last week)
export const OlderActivity: Story = {
  args: {
    claimnum: 'CLAIM-2023-999',
    applicant: 'Lisa Davis',
    access_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
  },
};

// Multiple cards in a list
export const MultipleCards: Story = {
  render: () => (
    <div style={{ width: '400px', display: 'flex', flexDirection: 'column', gap: '8px' }}>
      <RecentActivityCard
        claimnum="CLAIM-2024-001"
        applicant="John Smith"
        access_date={new Date().toISOString()}
        onClick={() => console.log('Clicked on John Smith claim')}
      />
      <RecentActivityCard
        claimnum="CLAIM-2024-002"
        applicant="Sarah Wilson"
        access_date={new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()}
        onClick={() => console.log('Clicked on Sarah Wilson claim')}
      />
      <RecentActivityCard
        claimnum="CLAIM-2024-003"
        applicant="Michael Brown"
        access_date={new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()}
        onClick={() => console.log('Clicked on Michael Brown claim')}
      />
      <RecentActivityCard
        claimnum="CLAIM-2023-999"
        applicant="Lisa Davis"
        access_date={new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()}
        onClick={() => console.log('Clicked on Lisa Davis claim')}
      />
    </div>
  ),
};

// Interactive story with click handler
export const Interactive: Story = {
  args: {
    claimnum: 'CLAIM-2024-004',
    applicant: 'Robert Johnson',
    access_date: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
  },
  parameters: {
    docs: {
      description: {
        story: 'This story demonstrates the click functionality. Check the Actions tab to see click events.',
      },
    },
  },
}; 
import React from 'react';
import styled from 'styled-components';
import { JobFeeOptions } from '../../../Engine/helpers/BOQ-utils';

export interface InvoiceSummaryProps {
  subTotal: number;
  vat?: number;
  excessAmount?: number;
  jobFeeOptions?: JobFeeOptions
}

const InvoiceSummaryTotals = styled.div`
  position: relative;
  top: 42px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 183px;
  gap: 16px;
  text-align: right;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
`;

const Descriptors = styled.div`
  display: grid;
  grid-template-rows: repeat(auto, auto);
  gap: 0;
  justify-items: end;
  align-items: center;
`;

const Amounts = styled.div`
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  border-radius: 4px;
  box-sizing: border-box;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  padding: 8px;
  display: grid;
  grid-template-rows: repeat(auto, auto);
  gap: 16px;
  justify-items: center;
  align-items: center;
  text-align: center;
`;

const formatCurrency = (amount: number) => {
  return `R ${amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
};

/**
 * InvoiceSummary component to display a breakdown of subtotal, VAT, and optionally excess amounts.
 *
 * @param {InvoiceSummaryProps} props - The props for configuring the component
 * @returns {React.FC} The rendered invoice summary
 */
const InvoiceSummary: React.FC<InvoiceSummaryProps> = ({
  subTotal,
  vat,
  excessAmount,
  jobFeeOptions
}) => {
  // const vatAmount = (subTotal * vat) / 100;
  // const total = subTotal + vatAmount;
  // const balanceDue = total - (excessAmount || 0);

  const effectiveVat = vat ?? 15; // default to 15% if not specified
  const showVat = effectiveVat !== 0;

  const subTotalLessJobFee = subTotal - getJobFeeAmount();

  const vatAmount = (subTotalLessJobFee * effectiveVat) / 100;
  const total = subTotalLessJobFee + (showVat ? vatAmount : 0);
  const balanceDue = total - (excessAmount || 0);

  /**
   * Calculate the job fee amount based on the provided job fee options.
   * The fee is determined by applying a percentage to the subtotal,
   * and is then constrained within a minimum and maximum amount ranges.
   *
   * @returns {number} The calculated job fee amount
   */
  function getJobFeeAmount(): number {
    // Return 0 if no job fee options are provided
    if (!jobFeeOptions) return 0;

    const { min, max, percentage } = jobFeeOptions;

    // Calculate the fee by applying the percentage to the subtotal
    const jobFeeAmount = (subTotal * percentage) / 100;

    // Constrain the fee within the minimum and maximum limits
    if (jobFeeAmount > max) return max;
    if (jobFeeAmount < min) return min;

    return jobFeeAmount;
  }

  return (
    <InvoiceSummaryTotals>
      <Descriptors>
        {jobFeeOptions && <div>Less Fee Per Job</div>}
        <div>Subtotal</div>
        {/* <div>Add: VAT &#64; {vat}%</div> */}
        {showVat && <div>Add: VAT &#64; {effectiveVat}%</div>}
        {/* {excessAmount !== undefined && <div>Less Excess</div>} */}
        <div>Less Excess</div>
        {/* <div>{excessAmount !== undefined ? 'Balance Due' : 'Total Due'}</div> */}
        <div>Balance Due</div>
      </Descriptors>
      <Amounts>
        {jobFeeOptions && <div>{formatCurrency(getJobFeeAmount())}</div>}
        <div>{formatCurrency(subTotalLessJobFee)}</div>
        {/* <div>{formatCurrency(vatAmount) || 15}</div> */}
        {showVat && <div>{formatCurrency(vatAmount)}</div>}
        {/* {excessAmount !== undefined && (
          <div>{formatCurrency(excessAmount)}</div>
        )} */}
        <div>{formatCurrency(excessAmount || 0)}</div>
        <div>{formatCurrency(balanceDue)}</div>
      </Amounts>
    </InvoiceSummaryTotals>
  );
};

export default InvoiceSummary;

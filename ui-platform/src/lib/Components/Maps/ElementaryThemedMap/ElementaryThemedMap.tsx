import {
  <PERSON>complete,
  GoogleMap,
  LoadScript,
  <PERSON>er,
  DirectionsRenderer,
} from '@react-google-maps/api';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import styled, { css } from 'styled-components';
import Truck from '../../public/images/truck.png';

type MapComponentProps = {
  joblocation: {
    lat: number;
    lng: number;
  };
  theme: 'light' | 'dark';
  search?: boolean; // New prop for search functionality
  streetView?: boolean; // New prop for street view control
  size?: 'full' | 'small';
  route?: {
    origin: { lat: number; lng: number };
    destination: { lat: number; lng: number };
  };
};

const SearchWrapper = styled(({ mode, ...rest }) => <div {...rest} />)`
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1;
  width: 347px;
  max-width: 100%;

  input {
    width: 100%;
    border-radius: 5px;
    padding: 10px;
    border: 1px solid;
    ${(props) =>
      props.mode === 'light' &&
      css`
        color: ${(props) => props?.theme.ColorsTypographyPrimary};
        background: ${(props) => props?.theme.ColorsOverlaySurfaceOverlay};
        border-color: ${(props) => props?.theme.ColorsStrokesDefault};
      `}
    ${(props) =>
      props.mode === 'dark' &&
      css`
        color: ${(props) => props?.theme.ColorsTypographyPrimary};
        background: ${(props) => props?.theme.ColorsOverlaySurfaceOverlay};
        border-color: ${(props) => props?.theme.ColorsStrokesDefault};
      `}
  }
`;
/**
 * A reusable, themed, Google Map component with search and street view capabilities.
 *
 * @param {MapComponentProps} props - The component props.
 * @param {object} props.joblocation - The job location coordinates.
 * @param {string} props.theme - The theme of the map.
 * @param {boolean} [props.search=false] - Whether to display the search bar.
 * @param {boolean} [props.streetView=false] - Whether to display the street view.
 * @param {string} [props.size='full'] - The size of the map.
 * @return {JSX.Element} The job map component.
 */
export const ElementaryThemedMap: React.FC<MapComponentProps> = ({
  joblocation,
  theme,
  search = false,
  streetView = false,
  size,
  route,
  ...rest
}) => {
  const [lat, setLat] = useState(joblocation.lat);
  const [lng, setLng] = useState(joblocation.lng);
  const [mode, setMode] = useState(theme);
  const [address, setAddress] = useState('');
  const autocompleteRef = useRef<any>(null);
  const [directions, setDirections] =
    useState<google.maps.DirectionsResult | null>(null);

  const mapStyles = {
    light: [],
    dark: [
      {
        elementType: 'geometry',
        stylers: [{ color: '#212121' }],
      },
      {
        elementType: 'labels.text.fill',
        stylers: [{ color: '#cccccc' }],
      },
      {
        elementType: 'labels.text.stroke',
        stylers: [{ color: '#000000' }],
      },
      {
        featureType: 'administrative',
        elementType: 'geometry',
        stylers: [{ visibility: 'on' }],
      },
      {
        featureType: 'administrative.land_parcel',
        elementType: 'labels',
        stylers: [{ visibility: 'on' }],
      },
      {
        featureType: 'administrative.neighborhood',
        elementType: 'labels',
        stylers: [{ visibility: 'on' }],
      },
      {
        featureType: 'road',
        elementType: 'geometry',
        stylers: [{ color: '#cccccc' }],
      },
      {
        featureType: 'road',
        elementType: 'labels',
        stylers: [{ visibility: 'on' }],
      },
      // ... (other dark mode styles)
    ],
  };

  const mapSize = useMemo(() => {
    switch (size) {
      case 'full':
        return { width: '100%', height: '100%' };
      case 'small':
      default:
        return { width: '390px', height: '350px' };
    }
  }, [size]);

  const containerStyle = {
    width: '390px',
    height: '350px',
  };

  /**
   * Handles the event when the user selects a different place from the autocomplete suggestions.
   * It updates the latitude and longitude state variables with the selected place's coordinates.
   */
  const handlePlaceChanged = () => {
    const place = autocompleteRef.current.getPlace();
    if (place.geometry) {
      const newLat = place.geometry.location.lat();
      const newLng = place.geometry.location.lng();
      setLat(newLat);
      setLng(newLng);
    }
  };

  /**
   * Handles the event when the user presses the Enter key on the address input.
   * It uses the Google Maps Geocoder API to geocode the address and update the
   * latitude and longitude state variables with the geocoded coordinates.
   * If the geocoding is not successful, it shows an alert with the error message.
   * @param {React.KeyboardEvent<HTMLInputElement>} e The keyboard event
   */
  const handleKeyPress = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({ address: address }, (results, status) => {
        if (status === 'OK' && results && results[0].geometry) {
          const newLat = results[0].geometry.location.lat();
          const newLng = results[0].geometry.location.lng();
          setLat(newLat);
          setLng(newLng);
        } else {
          alert(
            'Geocode was not successful for the following reason: ' + status
          );
        }
      });
    }
  };

  useEffect(() => {
    setLat(joblocation.lat);
    setLng(joblocation.lng);
  }, [joblocation]);

  useEffect(() => {
    setMode(theme);
  }, [theme]);

  // New effect to calculate and display the route if route prop is provided
  useEffect(() => {
    if (route && window.google) {
      const directionsService = new window.google.maps.DirectionsService();
      directionsService.route(
        {
          origin: route.origin,
          destination: route.destination,
          travelMode: window.google.maps.TravelMode.DRIVING,
        },
        (result, status) => {
          if (status === window.google.maps.DirectionsStatus.OK) {
            setDirections(result);
          } else {
            console.error('Error fetching directions', result);
          }
        }
      );
    }
  }, [route]);

  return (
    <div style={{ position: 'relative', ...mapSize }}>
      <LoadScript
        googleMapsApiKey="AIzaSyA28JqiZDQ8_CYVbdLKsrA-l_E0iDkW0pw"
        libraries={['places']}
      >
        <GoogleMap
          mapContainerStyle={size ? mapSize : containerStyle}
          center={{ lat, lng }}
          zoom={6} // Suburb-level zoom setting
          options={{
            styles: mapStyles[mode],
            mapTypeControl: false,
            fullscreenControl: false,
            streetViewControl: streetView,
          }}
        >
          {route?.origin && (
            <Marker
              position={route.origin}
              icon={{
                url: Truck,
              }}
              label="A"
            />
          )}
          {route?.destination && (
            <Marker position={route.destination} label="B" />
          )}
          {/* {!route && (
            <Marker
              position={{ lat, lng }}
              label="B"
            />
          )} */}
          {directions && <DirectionsRenderer directions={directions} />}
        </GoogleMap>
        {/* {search && (
          <SearchWrapper mode={mode}>
            <Autocomplete
              onLoad={(ref) => (autocompleteRef.current = ref)}
              onPlaceChanged={handlePlaceChanged}
            >
              <input
                type="text"
                placeholder="Search Address"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                onKeyPress={handleKeyPress}
              />
            </Autocomplete>
          </SearchWrapper>
        )} */}
      </LoadScript>
    </div>
  );
};
export default ElementaryThemedMap;

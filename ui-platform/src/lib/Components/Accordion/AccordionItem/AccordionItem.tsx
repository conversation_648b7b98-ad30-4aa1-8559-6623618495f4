import { MouseEvent } from 'react';
import styled from 'styled-components';
import { AccordionItemProps } from '../../Models';

/**
 * Styled details component that serves as the container for an accordion item.
 * Implements custom styling for the accordion item's expanded and collapsed states,
 * including cursor styling, chevron rotation animation, and smooth sliding transitions.
 */
const Wrapper = styled(({ children, key, ...rest }) => (
  <details {...rest}>{children}</details>
))`
  && {
    cursor: pointer;
    border-radius: ${(props) => props.theme.RadiusXs};
    margin-bottom: ${(props) => props.theme.SpacingSm};
    overflow: hidden;

    /* Smooth transition for the summary element */
    summary {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      svg {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }

    /* Content container for smooth height animation */
    > div:not(summary) {
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      opacity: 0;
      max-height: 0;
      padding-top: 0;
      padding-bottom: 0;
    }

    &[open] {
      summary {
        border-color: ${(props) =>
          props.theme.ColorsButtonColorToolbarButtonsActivated};
        svg {
          transform: rotate(180deg) translateY(50%);
        }
      }

      /* Animate content when open */
      > div:not(summary) {
        opacity: 1;
        max-height: 1000px; /* Large enough value for content */
        padding-top: ${(props) => props.theme.SpacingSm};
        padding-bottom: ${(props) => props.theme.SpacingSm};
      }
    }

    /* Enhanced animation timing for different states */
    &:not([open]) > div:not(summary) {
      transition-delay: 0s;
    }

    &[open] > div:not(summary) {
      transition-delay: 0.1s;
    }
  }
`;

/**
 * A component representing an individual item within an Accordion.
 * This component uses the HTML5 details/summary elements for native accessibility support
 * and implements custom styling and behavior for the accordion pattern.
 *
 * @component
 * @param {Object} props - The component props
 * @param {boolean} [props.isOpen=false] - Controls whether the accordion item is expanded
 * @param {string} [props.key] - Unique identifier for the accordion item
 * @param {(e: MouseEvent<HTMLElement>) => void} [props.onClick] - Callback function triggered when the item is clicked
 * @param {string} [props.layout] - Layout variant for the accordion item
 * @param {string} [props.className] - Additional CSS classes to apply
 * @param {ReactNode} props.children - The content of the accordion item (should include AccordionHeading and AccordionContent)
 *
 * @example
 * ```tsx
 * <AccordionItem isOpen={true} onClick={handleClick}>
 *   <AccordionHeading>Section Title</AccordionHeading>
 *   <AccordionContent>Section Content</AccordionContent>
 * </AccordionItem>
 * ```
 */
export const AccordionItem = ({
  isOpen = false,
  onClick,
  layout,
  className,
  children,
  ...rest
}: AccordionItemProps) => {
  /**
   * Handles click events on the accordion heading (summary element).
   * Only responds to clicks on the summary, allowing content interactions.
   *
   * @param {MouseEvent<HTMLElement>} e - The click event object
   */
  const onAccordionClick = (e: MouseEvent<HTMLElement>) => {
    // Only handle clicks on the summary element (AccordionHeading)
    // This allows interactive content in AccordionContent to work properly
    if (e.target instanceof HTMLElement) {
      const summary = e.currentTarget.querySelector('summary');

      // Check if the click is on the summary element or any of its children
      const isClickOnSummary =
        summary && (e.target === summary || summary.contains(e.target));

      if (isClickOnSummary && onClick) {
        // Prevent the default details/summary behavior to avoid conflicts
        e.preventDefault();
        onClick(e);
      }
    }
  };

  return (
    <Wrapper open={isOpen} onClick={onAccordionClick} style={layout} {...rest}>
      {children[0]}
      {children[1]}
    </Wrapper>
  );
};

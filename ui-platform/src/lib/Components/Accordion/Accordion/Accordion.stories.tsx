import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { ComponentPropsWithoutRef, ReactElement } from 'react';
import { CSSProperties } from 'styled-components';
import {
  Accordion,
  AccordionContent,
  AccordionHeading,
  AccordionItem,
} from '../';
import { JobImageListItem } from '../../JobImage/JobImageListItem/JobImageListItem';
import { withItems } from '../../List/List';

const meta: Meta<typeof Accordion> = {
  title: 'Components/Accordion/Accordion',
  component: Accordion,
  parameters: {
    componentSubtitle:
      'A flexible and accessible accordion component for organizing content',
    layout: 'padded',
    docs: {
      description: {
        component: `
<div class="documentation-wrapper" style="max-width: 1200px; margin: 0 auto; padding: 20px; font-family: system-ui, -apple-system, sans-serif;">

<div style="background: linear-gradient(90deg, #2C5282, #2B6CB0); padding: 2px; border-radius: 8px; margin-bottom: 30px;">
  <div style="background: white; padding: 20px; border-radius: 6px;">
    <h1 style="color: #2C5282; margin-top: 0;">Accordion Component</h1>
    <p style="color: #4A5568; font-size: 1.1em; line-height: 1.5;">
      A powerful and flexible accordion component that helps organize content into collapsible sections, perfect for FAQs, navigation menus, and content organization.
    </p>
  </div>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
  <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🎯 Key Features</h3>
    <ul style="list-style-type: none; padding: 0; margin: 0;">
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Multiple or Single Expansion
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Customizable Styling
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Keyboard Navigation
      </li>
      <li style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        ✓ Nested Content Support
      </li>
    </ul>
  </div>
  
  <div style="background: #EDF2F7; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">⚡ Component Structure</h3>
    <ul style="list-style-type: none; padding: 0; margin: 0;">
      <li style="margin-bottom: 8px;">🔷 Accordion (Container)</li>
      <li style="margin-bottom: 8px;">🔷 AccordionItem</li>
      <li style="margin-bottom: 8px;">🔷 AccordionHeading</li>
      <li style="margin-bottom: 8px;">🔷 AccordionContent</li>
    </ul>
  </div>
</div>

<div style="background: #F7FAFC; padding: 24px; border-radius: 8px; margin-bottom: 30px;">
  <h2 style="color: #2C5282; margin-top: 0;">Integration Examples</h2>
  
  <div style="background: #2C5282; padding: 2px; border-radius: 6px; margin: 20px 0;">
    <div style="background: white; padding: 20px; border-radius: 4px;">
      <h3 style="color: #2C5282; margin-top: 0;">Basic Usage</h3>
      
\`\`\`tsx
import { Accordion, AccordionItem, AccordionHeading, AccordionContent } from '@4-sure/ui-platform';

function MyComponent() {
  return (
    <Accordion allowMultipleExpanded={true}>
      <AccordionItem>
        <AccordionHeading>Section 1</AccordionHeading>
        <AccordionContent>Content for section 1</AccordionContent>
      </AccordionItem>
      <AccordionItem>
        <AccordionHeading>Section 2</AccordionHeading>
        <AccordionContent>Content for section 2</AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
\`\`\`
    </div>
  </div>

  <div style="background: #2C5282; padding: 2px; border-radius: 6px; margin: 20px 0;">
    <div style="background: white; padding: 20px; border-radius: 4px;">
      <h3 style="color: #2C5282; margin-top: 0;">With Custom Styling</h3>
      
\`\`\`tsx
function CustomStyledAccordion() {
  return (
    <Accordion>
      <AccordionItem style={{ border: '1px solid #E2E8F0' }}>
        <AccordionHeading style={{ background: '#F7FAFC', padding: '1rem' }}>
          Custom Styled Section
        </AccordionHeading>
        <AccordionContent style={{ padding: '1rem' }}>
          Content with custom styling
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
\`\`\`
    </div>
  </div>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
  <div style="background: #F7FAFC; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🛠️ Props & Configuration</h3>
    
    <h4 style="color: #4A5568;">Accordion Props</h4>
    <ul style="color: #4A5568;">
      <li>allowMultipleExpanded (boolean)</li>
      <li>children (AccordionItem[])</li>
      <li>className (string)</li>
      <li>style (CSSProperties)</li>
    </ul>
    
    <h4 style="color: #4A5568;">AccordionItem Props</h4>
    <ul style="color: #4A5568;">
      <li>isOpen (boolean)</li>
      <li>onClick (function)</li>
      <li>children (node)</li>
    </ul>
  </div>
  
  <div style="background: #F7FAFC; padding: 20px; border-radius: 8px;">
    <h3 style="color: #2C5282; margin-top: 0;">🎨 Styling Options</h3>
    <ul style="color: #4A5568;">
      <li>Custom CSS via className</li>
      <li>Inline styles support</li>
      <li>Styled-components integration</li>
      <li>Theme-based styling</li>
    </ul>
  </div>
</div>

<div style="background: #F7FAFC; padding: 24px; border-radius: 8px; margin-bottom: 30px;">
  <h2 style="color: #2C5282; margin-top: 0;">Technical Details</h2>
  
  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
    <div>
      <h3 style="color: #2C5282;">💪 Performance</h3>
      <ul style="color: #4A5568;">
        <li>Optimized re-renders</li>
        <li>Lazy content loading</li>
        <li>Efficient state management</li>
      </ul>
    </div>
    
    <div>
      <h3 style="color: #2C5282;">♿ Accessibility</h3>
      <ul style="color: #4A5568;">
        <li>ARIA attributes</li>
        <li>Keyboard navigation</li>
        <li>Screen reader support</li>
      </ul>
    </div>
    
    <div>
      <h3 style="color: #2C5282;">🌐 Best Practices</h3>
      <ul style="color: #4A5568;">
        <li>Semantic HTML structure</li>
        <li>Consistent behavior</li>
        <li>Responsive design</li>
      </ul>
    </div>
  </div>
</div>

</div>
        `,
      },
      canvas: {
        sourceState: 'shown',
        layout: 'padded',
      },
      story: {
        inline: true,
        height: '300px',
      },
    },
  },
};

interface IItem {
  heading: string;
  content: ReactElement | string;
  isOpen?: boolean;
  onClick?: ComponentPropsWithoutRef<typeof AccordionItem>['onClick'];
  layout?: CSSProperties;
}

const items: IItem[] = [
  {
    heading: 'Photos or Documents',
    content: (
      <div>
        <JobImageListItem
          jobImageItems={[
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'image',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'image',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'icon',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'image',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'image',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'icon',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'image',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'image',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
            {
              src: 'https://picsum.photos/1336/1080',
              name: 'Decoder Serial Number',
              mediaType: 'icon',
              TeamMemberName: 'TeamMemberName',
              date: '2024/08/05',
              time: '11:69',
            },
          ]}
          usecase={'DocumentView'}
          mediaType={''}
        ></JobImageListItem>
      </div>
    ),
    isOpen: true,
    onClick: (e) => console.log(e),
  },
  {
    heading: 'Text and Stuff',
    content:
      '2 Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud 3 Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud',
    isOpen: false,
    onClick: (e) => console.log(e),
  },
  // {
  //   heading: 'Heading 3',
  //   content:
  //     '3 Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud ',
  //   onClick: (e) => console.log(e),
  // },
  // {
  //   heading: 'Heading 4',
  //   content:
  //     '4 Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud ',
  //   onClick: (e) => console.log(e),
  // },
];

const Item = ({ heading, content, isOpen, onClick, layout }: IItem) => (
  <AccordionItem
    isOpen={isOpen}
    onClick={onClick}
    layout={{ padding: '3px', ...layout }}
  >
    <AccordionHeading>{heading}</AccordionHeading>
    <AccordionContent>{content}</AccordionContent>
  </AccordionItem>
);

type Story = StoryObj<typeof Accordion>;

export const Overview: Story = {
  args: {
    children: withItems(Item)(items),
    allowMultipleExpanded: true,
  },
};

// Basic accordion with single expansion
export const SingleExpansion: Story = {
  args: {
    allowMultipleExpanded: false,
    children: [
      <AccordionItem key="1">
        <AccordionHeading>Section 1</AccordionHeading>
        <AccordionContent>
          <p>This accordion only allows one section to be open at a time.</p>
          <p>
            When you open another section, this one will automatically close.
          </p>
        </AccordionContent>
      </AccordionItem>,
      <AccordionItem key="2">
        <AccordionHeading>Section 2</AccordionHeading>
        <AccordionContent>
          <p>Click on this section to see the single expansion behavior.</p>
          <p>Notice how the previous section closes automatically.</p>
        </AccordionContent>
      </AccordionItem>,
      <AccordionItem key="3">
        <AccordionHeading>Section 3</AccordionHeading>
        <AccordionContent>
          <p>This is the third section demonstrating single expansion mode.</p>
          <p>Only one section can be open at any given time.</p>
        </AccordionContent>
      </AccordionItem>,
    ],
  },
};

// Multiple expansion accordion
export const MultipleExpansion: Story = {
  args: {
    allowMultipleExpanded: true,
    children: [
      <AccordionItem key="1">
        <AccordionHeading>Section 1</AccordionHeading>
        <AccordionContent>
          <p>
            This accordion allows multiple sections to be open simultaneously.
          </p>
          <p>You can expand as many sections as you want.</p>
        </AccordionContent>
      </AccordionItem>,
      <AccordionItem key="2">
        <AccordionHeading>Section 2</AccordionHeading>
        <AccordionContent>
          <p>Open this section while keeping others open.</p>
          <p>All sections can be expanded at the same time.</p>
        </AccordionContent>
      </AccordionItem>,
      <AccordionItem key="3">
        <AccordionHeading>Section 3</AccordionHeading>
        <AccordionContent>
          <p>This section can be open alongside the others.</p>
          <p>Perfect for comparing content across sections.</p>
        </AccordionContent>
      </AccordionItem>,
    ],
  },
};

// Accordion with interactive content
export const WithInteractiveContent: Story = {
  args: {
    allowMultipleExpanded: true,
    children: [
      <AccordionItem key="1">
        <AccordionHeading>Interactive Buttons</AccordionHeading>
        <AccordionContent>
          <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
            <button
              onClick={() => alert('Primary action triggered!')}
              style={{
                padding: '8px 16px',
                backgroundColor: '#2C5282',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Primary Action
            </button>
            <button
              onClick={() => alert('Secondary action triggered!')}
              style={{
                padding: '8px 16px',
                backgroundColor: '#E2E8F0',
                color: '#2C5282',
                border: '1px solid #CBD5E0',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Secondary Action
            </button>
            <button
              onClick={() => alert('Danger action triggered!')}
              style={{
                padding: '8px 16px',
                backgroundColor: '#E53E3E',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Danger Action
            </button>
          </div>
          <p style={{ marginTop: '16px', color: '#4A5568' }}>
            These buttons are interactive and won't trigger accordion collapse
            when clicked.
          </p>
        </AccordionContent>
      </AccordionItem>,
      <AccordionItem key="2">
        <AccordionHeading>Form Elements</AccordionHeading>
        <AccordionContent>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              alert('Form submitted!');
            }}
          >
            <div style={{ marginBottom: '16px' }}>
              <label
                style={{
                  display: 'block',
                  marginBottom: '4px',
                  fontWeight: 'bold',
                }}
              >
                Name:
              </label>
              <input
                type="text"
                placeholder="Enter your name"
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #CBD5E0',
                  borderRadius: '4px',
                }}
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <label
                style={{
                  display: 'block',
                  marginBottom: '4px',
                  fontWeight: 'bold',
                }}
              >
                Email:
              </label>
              <input
                type="email"
                placeholder="Enter your email"
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #CBD5E0',
                  borderRadius: '4px',
                }}
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <label
                style={{
                  display: 'block',
                  marginBottom: '4px',
                  fontWeight: 'bold',
                }}
              >
                Message:
              </label>
              <textarea
                placeholder="Enter your message"
                rows={3}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #CBD5E0',
                  borderRadius: '4px',
                  resize: 'vertical',
                }}
              />
            </div>
            <button
              type="submit"
              style={{
                padding: '8px 16px',
                backgroundColor: '#38A169',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Submit Form
            </button>
          </form>
        </AccordionContent>
      </AccordionItem>,
      <AccordionItem key="3">
        <AccordionHeading>Rich Content</AccordionHeading>
        <AccordionContent>
          <div style={{ display: 'grid', gap: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div
                style={{
                  width: '50px',
                  height: '50px',
                  backgroundColor: '#4299E1',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontWeight: 'bold',
                }}
              >
                1
              </div>
              <div>
                <h4 style={{ margin: '0 0 4px 0', color: '#2C5282' }}>
                  Step One
                </h4>
                <p style={{ margin: 0, color: '#4A5568' }}>
                  Complete the initial setup process
                </p>
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div
                style={{
                  width: '50px',
                  height: '50px',
                  backgroundColor: '#48BB78',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontWeight: 'bold',
                }}
              >
                2
              </div>
              <div>
                <h4 style={{ margin: '0 0 4px 0', color: '#2C5282' }}>
                  Step Two
                </h4>
                <p style={{ margin: 0, color: '#4A5568' }}>
                  Configure your preferences
                </p>
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div
                style={{
                  width: '50px',
                  height: '50px',
                  backgroundColor: '#ED8936',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontWeight: 'bold',
                }}
              >
                3
              </div>
              <div>
                <h4 style={{ margin: '0 0 4px 0', color: '#2C5282' }}>
                  Step Three
                </h4>
                <p style={{ margin: 0, color: '#4A5568' }}>Launch and enjoy!</p>
              </div>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>,
    ],
  },
};

export default meta;

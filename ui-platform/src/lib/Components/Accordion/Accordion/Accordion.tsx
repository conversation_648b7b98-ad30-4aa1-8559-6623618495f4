import {
  Children,
  MouseEvent,
  ReactElement,
  cloneElement,
  isValidElement,
  useEffect,
  useMemo,
  useState,
} from 'react';
import styled from 'styled-components';
import { AccordionItemProps, AccordionListProps } from '../../Models';

/**
 * StyledDiv component that serves as the container for the Accordion.
 * Implemented as a styled div that passes through all props except children.
 */
const StyledDiv = styled(({ children, ...rest }) => (
  <div {...rest}>{children}</div>
))``;

/**
 * A controlled accordion component that manages the expansion state of its child items.
 * This component implements the accordion pattern where items can be expanded/collapsed,
 * with optional support for multiple items being expanded simultaneously.
 *
 * @component
 * @param {Object} props - The component props
 * @param {boolean} [props.allowMultipleExpanded=true] - If true, multiple accordion items can be expanded simultaneously
 * @param {ReactElement<AccordionItemProps>[]} props.children - The accordion items to be rendered
 * @param {number[]} [props.defaultExpandedIndexes=[]] - Array of indexes that should be expanded by default
 * @param {(index: number, isOpen: boolean) => void} [props.onToggle] - Callback fired when an accordion item is toggled
 * @param {boolean} [props.collapsible=true] - If false, at least one accordion item must remain open
 * @param {boolean} [props.controlled=false] - If true, uses external state from child props instead of internal state
 * @param {string} [props.className] - Additional CSS class name
 * @param {Object} props.rest - Additional props to be spread to the root element
 *
 * @example
 * ```tsx
 * <Accordion
 *   allowMultipleExpanded={false}
 *   defaultExpandedIndexes={[0]}
 *   onToggle={(index, isOpen) => console.log(`Item ${index} is ${isOpen ? 'open' : 'closed'}`)}
 *   collapsible={false}
 * >
 *   <AccordionItem>
 *     <AccordionHeading>Section 1</AccordionHeading>
 *     <AccordionContent>Content 1</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 * ```
 */
export const Accordion = (props: AccordionListProps) => {
  const {
    allowMultipleExpanded = true,
    children,
    defaultExpandedIndexes = [],
    onToggle,
    collapsible = true,
    controlled = false,
    ...rest
  } = props;

  // Filter out null/undefined children and ensure we have valid elements
  const validChildren = useMemo(() => {
    return Children.toArray(children).filter(
      (child): child is ReactElement<AccordionItemProps> =>
        isValidElement(child) && child.props !== null
    );
  }, [children]);

  // State variable to track the expansion state of each accordion item.
  // Only initialize internal state when NOT in controlled mode
  const [isAccOpen, setIsAccOpen] = useState<boolean[]>(() => {
    if (controlled) {
      // In controlled mode, return empty array as internal state won't be used
      return [];
    }
    return validChildren.map(
      (child: ReactElement<AccordionItemProps>, index) => {
        // Priority: defaultExpandedIndexes > child.props.isOpen
        if (defaultExpandedIndexes.includes(index)) {
          return true;
        }
        return !!child.props.isOpen;
      }
    );
  });

  // Sync state when children change (only in uncontrolled mode)
  useEffect(() => {
    if (!controlled) {
      setIsAccOpen(
        validChildren.map((child: ReactElement<AccordionItemProps>, index) => {
          // Priority: defaultExpandedIndexes > child.props.isOpen
          if (defaultExpandedIndexes.includes(index)) {
            return true;
          }
          return !!child.props.isOpen;
        })
      );
    }
  }, [validChildren, defaultExpandedIndexes, controlled]);

  // Memoized function to render the accordion items with their current expansion state.
  const accordions = useMemo(() => {
    // Handler to toggle accordion item open/close (only used in uncontrolled mode)
    const handleToggle = (index: number) => {
      if (controlled) {
        // In controlled mode, don't use internal toggle logic
        return;
      }

      setIsAccOpen((prev) => {
        const currentlyOpen = prev[index];
        const newState = !currentlyOpen;

        // If collapsible is false and this is the only open item, don't allow closing
        if (
          !collapsible &&
          currentlyOpen &&
          prev.filter(Boolean).length === 1
        ) {
          return prev;
        }

        let newOpenState: boolean[];

        if (allowMultipleExpanded) {
          // Toggle only the clicked item
          newOpenState = prev.map((open, i) => (i === index ? newState : open));
        } else {
          // Only one open at a time: open clicked, close others
          newOpenState = prev.map((_, i) => (i === index ? newState : false));
        }

        // Call onToggle callback if provided
        if (onToggle) {
          onToggle(index, newState);
        }

        return newOpenState;
      });
    };

    return validChildren.map((child, index) => {
      if (controlled) {
        // In controlled mode, use the external state from child props
        return cloneElement(child, {
          ...child.props,
          // Use the external isOpen and onClick from child props
          isOpen: child.props.isOpen || false,
          onClick: child.props.onClick,
        });
      } else {
        // In uncontrolled mode, use internal state management
        return cloneElement(child, {
          ...child.props,
          isOpen: isAccOpen[index] || false,
          onClick: (e: MouseEvent<HTMLElement>) => {
            // Call original onClick if it exists
            if (child.props.onClick) {
              child.props.onClick(e);
            }
            // Handle our toggle logic
            // Note: AccordionItem now only calls onClick for summary clicks,
            // so this will only be triggered for heading clicks, not content clicks
            handleToggle(index);
          },
        });
      }
    });
  }, [
    isAccOpen,
    validChildren,
    allowMultipleExpanded,
    collapsible,
    onToggle,
    controlled,
  ]);

  return (
    <StyledDiv data-testid="accordion-list" {...rest}>
      {accordions}
    </StyledDiv>
  );
};

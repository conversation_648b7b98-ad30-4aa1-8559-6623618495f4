
import type { Meta, StoryObj } from '@storybook/react';

import { NotFound } from './NotFound';
import { Forbidden } from './Forbidden';
import { InternalServerError } from './InternalServerError';
import { Unauthorized } from './Unauthorized';
import { ErrorDisplay } from './ErrorDisplay';

const meta: Meta<typeof ErrorDisplay> = {
  title: 'Components/ErrorDisplay',
  component: ErrorDisplay,
  decorators: [(Story) => <Story />],
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof ErrorDisplay>;

export const Generic: Story = {
  args: {
    title: 'Something went wrong',
    message: 'An unexpected error occurred. Please try again.',
  },
};

export const PageNotFound: Story = {
  render: () => <NotFound navigate={() => {}} />,
};

export const ForbiddenError: Story = {
  render: () => <Forbidden navigate={() => {}} />,
};

export const ServerError: Story = {
  render: () => <InternalServerError />,
};

export const UnauthorizedError: Story = {
  render: () => <Unauthorized navigate={() => {}} />,
}; 
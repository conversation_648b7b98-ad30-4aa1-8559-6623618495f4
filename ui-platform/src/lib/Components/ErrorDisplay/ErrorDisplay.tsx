
import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { Heading } from '../Heading/Heading';
import { Paragraph } from '../Paragraph/Paragraph';
import { Background } from '../Background/Background';

interface ErrorDisplayProps {
  title: string;
  message: string;
  actions?: ReactNode;
  image?: ReactNode;
}

const MainContainer = styled.div`
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
`;

const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  padding: 2rem;
  z-index: 1;
  position: relative;
`;

const ContentWrapper = styled.div`
  max-width: 600px;
`;

const ImageWrapper = styled.div`
  margin-bottom: 2rem;
  max-width: 300px;
  width: 100%;
`;

const ActionsWrapper = styled.div`
  margin-top: 2rem;
`;

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  title,
  message,
  actions,
  image,
}) => {
  return (
    <MainContainer>
      <Background />
      <ErrorContainer>
        <ContentWrapper>
          {image && <ImageWrapper>{image}</ImageWrapper>}
          <Heading>{title}</Heading>
          <Paragraph>{message}</Paragraph>
          {actions && <ActionsWrapper>{actions}</ActionsWrapper>}
        </ContentWrapper>
      </ErrorContainer>
    </MainContainer>
  );
}; 
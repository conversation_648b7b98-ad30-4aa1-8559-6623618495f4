
import React from 'react';
import { ErrorDisplay } from './ErrorDisplay';
import { FormButton } from '../Buttons/FormButton/FormButton';

export const NotFound = ({navigate}: {navigate: (p: any) => void}) => {

  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <ErrorDisplay
      title="404 - Page Not Found"
      message="Sorry, the page you are looking for does not exist."
      actions={<FormButton onClick={handleGoHome}>Go to Homepage</FormButton>}
    />
  );
}; 
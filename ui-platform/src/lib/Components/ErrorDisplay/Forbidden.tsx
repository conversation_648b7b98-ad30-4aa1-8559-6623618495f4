
import React from 'react';
import { ErrorDisplay } from './ErrorDisplay';
import { FormButton } from '../Buttons/FormButton/FormButton';
import { useNavigate } from 'react-router-dom';

export const Forbidden = ({navigate}: {navigate: (p: any) => void}) => {

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <ErrorDisplay
      title="403 - Forbidden"
      message="Sorry, you do not have permission to access this page."
      actions={<FormButton onClick={handleGoBack}>Go Back</FormButton>}
    />
  );
}; 

import React from 'react';
import { ErrorDisplay } from './ErrorDisplay';
import { FormButton } from '../Buttons/FormButton/FormButton';

export const Unauthorized = ({navigate}: {navigate: (p: any) => void}) => {

  const handleLogin = () => {
    // This should ideally redirect to a login page.
    // The exact path might need to be configured.
    navigate('/login');
  };

  return (
    <ErrorDisplay
      title="401 - Unauthorized"
      message="Sorry, you need to be logged in to access this page."
      actions={<FormButton onClick={handleLogin}>Login</FormButton>}
    />
  );
}; 

import React from 'react';
import { ErrorDisplay } from './ErrorDisplay';
import { FormButton } from '../Buttons/FormButton/FormButton';

export const InternalServerError = () => {
  const handleReload = () => {
    window.location.reload();
  };

  return (
    <ErrorDisplay
      title="500 - Internal Server Error"
      message="Sorry, something went wrong on our end. Please try again later."
      actions={<FormButton onClick={handleReload}>Reload Page</FormButton>}
    />
  );
}; 
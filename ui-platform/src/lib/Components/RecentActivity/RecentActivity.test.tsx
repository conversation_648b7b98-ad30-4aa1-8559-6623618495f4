import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { RecentActivityList } from './RecentActivityList';
import { useRecentActivity } from '../../Hooks/useRecentActivity';

// Mock the hook
jest.mock('../../Hooks/useRecentActivity');

const mockUseRecentActivity = useRecentActivity as jest.MockedFunction<typeof useRecentActivity>;

describe('RecentActivityList', () => {
  const mockUserID = 123;

  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should display empty state when no recent activities', () => {
    mockUseRecentActivity.mockReturnValue({
      recentActivities: [],
      addRecentActivity: jest.fn(),
      removeRecentActivity: jest.fn(),
      clearRecentActivity: jest.fn(),
      getRecentActivities: jest.fn(),
      getRecentActivitiesByUser: jest.fn().mockReturnValue([]),
    });

    render(<RecentActivityList userID={mockUserID} />);
    
    expect(screen.getByText('No recent activities found. Click on claims to see them here.')).toBeInTheDocument();
  });

  it('should display recent activities when they exist', () => {
    const mockActivities = [
      {
        id: '1',
        userID: mockUserID,
        claim_num: 'CLM001',
        applicant: 'John Doe',
        access_date: '2024-01-01T10:00:00Z',
      },
      {
        id: '2',
        userID: mockUserID,
        claim_num: 'CLM002',
        applicant: 'Jane Smith',
        access_date: '2024-01-02T10:00:00Z',
      },
    ];

    mockUseRecentActivity.mockReturnValue({
      recentActivities: mockActivities,
      addRecentActivity: jest.fn(),
      removeRecentActivity: jest.fn(),
      clearRecentActivity: jest.fn(),
      getRecentActivities: jest.fn(),
      getRecentActivitiesByUser: jest.fn().mockReturnValue(mockActivities),
    });

    render(<RecentActivityList userID={mockUserID} />);
    
    expect(screen.getByText('CLM001')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('CLM002')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
  });

  it('should call onItemClick when a card is clicked', () => {
    const mockOnItemClick = jest.fn();
    const mockActivities = [
      {
        id: '1',
        userID: mockUserID,
        claim_num: 'CLM001',
        applicant: 'John Doe',
        access_date: '2024-01-01T10:00:00Z',
      },
    ];

    mockUseRecentActivity.mockReturnValue({
      recentActivities: mockActivities,
      addRecentActivity: jest.fn(),
      removeRecentActivity: jest.fn(),
      clearRecentActivity: jest.fn(),
      getRecentActivities: jest.fn(),
      getRecentActivitiesByUser: jest.fn().mockReturnValue(mockActivities),
    });

    render(<RecentActivityList userID={mockUserID} onItemClick={mockOnItemClick} />);
    
    fireEvent.click(screen.getByText('CLM001'));
    
    expect(mockOnItemClick).toHaveBeenCalledWith('CLM001', 'John Doe');
  });

  it('should handle multiple users separately', () => {
    const user1ID = 123;
    const user2ID = 456;

    const mockActivities = [
      {
        id: '1',
        userID: user1ID,
        claim_num: 'CLM001',
        applicant: 'User 1',
        access_date: '2024-01-01T10:00:00Z',
      },
      {
        id: '2',
        userID: user2ID,
        claim_num: 'CLM002',
        applicant: 'User 2',
        access_date: '2024-01-02T10:00:00Z',
      },
    ];

    mockUseRecentActivity.mockReturnValue({
      recentActivities: mockActivities,
      addRecentActivity: jest.fn(),
      removeRecentActivity: jest.fn(),
      clearRecentActivity: jest.fn(),
      getRecentActivities: jest.fn(),
      getRecentActivitiesByUser: jest.fn().mockImplementation((userID) => 
        mockActivities.filter(item => item.userID === userID)
      ),
    });

    const { rerender } = render(<RecentActivityList userID={user1ID} />);
    expect(screen.getByText('CLM001')).toBeInTheDocument();
    expect(screen.queryByText('CLM002')).not.toBeInTheDocument();

    rerender(<RecentActivityList userID={user2ID} />);
    expect(screen.getByText('CLM002')).toBeInTheDocument();
    expect(screen.queryByText('CLM001')).not.toBeInTheDocument();
  });
}); 
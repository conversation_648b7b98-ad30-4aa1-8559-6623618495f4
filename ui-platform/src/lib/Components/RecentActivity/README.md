# Recent Activity Feature

This feature replicates the IndexedDB-based recent activity functionality from the Angular app using Zustand store with localStorage persistence.

## Features

- ✅ User-specific storage (20 items per user)
- ✅ localStorage persistence across browser sessions
- ✅ Automatic oldest item removal when limit exceeded (matches Angular behavior)
- ✅ Automatic timestamp management
- ✅ Simple hook-based API for React components

## Key Differences from Original Implementation

This React implementation matches the Angular IndexedDB behavior:
- **User-specific**: Each user has their own 20-item limit
- **No duplicate prevention**: Unlike typical React patterns, this allows duplicates like the Angular version
- **Oldest item removal**: When a user reaches 20 items, the oldest is automatically removed
- **Field names**: Uses `claim_num` and `userID` to match the Angular structure

## Usage

### 1. Adding Recent Activity When Claim is Clicked

```typescript
import { useRecentActivity } from '@4-sure/ui-platform/lib/Hooks/useRecentActivity';

const ClaimsList = () => {
  const { addRecentActivity } = useRecentActivity();
  const currentUserID = 123; // Get from your auth context

  const handleClaimClick = (claim_num: string, applicant: string) => {
    // Add to recent activity (matches Angular IndexedDB behavior)
    addRecentActivity(currentUserID, claim_num, applicant, new Date().toISOString());
    
    // Your existing claim navigation logic
    navigateToClaim(claim_num);
  };

  return (
    <div>
      {claims.map((claim) => (
        <ClaimCard
          key={claim.id}
          claim={claim}
          onClick={() => handleClaimClick(claim.claim_num, claim.applicant?.surname)}
        />
      ))}
    </div>
  );
};
```

### 2. Displaying Recent Activity List

```typescript
import { RecentActivityList } from '@4-sure/ui-platform/lib/Components/RecentActivity/RecentActivityList';

const ActionPanelRecentActivity = () => {
  const currentUserID = 123; // Get from your auth context
  
  const handleRecentActivityClick = (claim_num: string, applicant: string) => {
    // Navigate to the clicked claim
    navigateToClaim(claim_num);
  };

  return (
    <RecentActivityList 
      userID={currentUserID}
      onItemClick={handleRecentActivityClick}
      showClearButton={true}
    />
  );
};
```

### 3. Using the Hook Directly

```typescript
import { useRecentActivity } from '@4-sure/ui-platform/lib/Hooks/useRecentActivity';

const MyComponent = () => {
  const currentUserID = 123; // Get from your auth context
  const { 
    getRecentActivitiesByUser, 
    addRecentActivity, 
    clearRecentActivity 
  } = useRecentActivity();

  const recentActivities = getRecentActivitiesByUser(currentUserID);

  const handleClaimClick = (claim_num: string, applicant: string) => {
    addRecentActivity(currentUserID, claim_num, applicant, new Date().toISOString());
  };

  return (
    <div>
      {recentActivities.map((item) => (
        <RecentActivityCard
          key={item.id}
          claimnum={item.claim_num} // Note: card component expects 'claimnum'
          applicant={item.applicant}
          access_date={item.access_date}
          onClick={() => handleClaimClick(item.claim_num, item.applicant)}
        />
      ))}
      <button onClick={clearRecentActivity}>Clear All</button>
    </div>
  );
};
```

## API Reference

### useRecentActivity Hook

```typescript
const {
  recentActivities,           // All activities (not user-specific)
  addRecentActivity,          // Function to add new activity
  removeRecentActivity,       // Function to remove specific activity
  clearRecentActivity,        // Function to clear all activities
  getRecentActivities,        // Function to get all activities
  getRecentActivitiesByUser,  // Function to get user-specific activities
} = useRecentActivity();
```

### Hook Methods

```typescript
// Add a recent activity item (matches Angular pattern)
addRecentActivity(userID: number, claim_num: string, applicant: string, access_date: string)

// Get all recent activities
getRecentActivities(): IRecentActivity[]

// Get recent activities for specific user
getRecentActivitiesByUser(userID: number): IRecentActivity[]

// Clear all recent activities
clearRecentActivity()

// Remove specific activity by ID
removeRecentActivity(id: string)
```

### Data Structure

```typescript
interface IRecentActivity {
  id?: string;           // Auto-generated unique identifier
  userID: number;        // User ID for user-specific storage
  claim_num: string;     // Claim number (matches Angular field name)
  applicant: string;     // Applicant name
  access_date: string;   // ISO timestamp
}
```

## Storage

- **Storage Key**: `biab-recentActivity`
- **Storage Type**: localStorage
- **Persistence**: Automatic across browser sessions
- **Version**: 1 (for future migrations)

## Behavior (Matches Angular IndexedDB)

1. **User-specific**: Each user has their own 20-item limit
2. **No duplicate prevention**: Allows multiple entries for the same claim (like Angular)
3. **Oldest item removal**: When a user reaches 20 items, the oldest is automatically removed
4. **Add to end**: New items are added to the list (not moved to front)
5. **Persistence**: Data is automatically saved to localStorage and restored on page load

## Integration Example

```typescript
// In your claims list component
const ClaimsList = () => {
  const currentUserID = useAuth().userID; // Get from your auth context
  const { addRecentActivity } = useRecentActivity();
  
  const handleClaimClick = (claim: Claim) => {
    // Add to recent activity (matches Angular pattern)
    addRecentActivity(
      currentUserID, 
      claim.claim_num || claim.mid, // Handle both field names
      claim.applicant?.surname || 'N/A',
      new Date().toISOString()
    );
    
    // Navigate to claim details
    navigate(`/claims/${claim.claim_num || claim.mid}`);
  };

  return (
    <div>
      {claims.map((claim) => (
        <ClaimCard
          key={claim.id}
          claim={claim}
          onClick={() => handleClaimClick(claim)}
        />
      ))}
    </div>
  );
};

// In your sidebar or navigation component
const Sidebar = () => {
  const currentUserID = useAuth().userID; // Get from your auth context
  
  return (
    <div>
      <h3>Recent Activity</h3>
      <RecentActivityList 
        userID={currentUserID}
        onItemClick={(claim_num, applicant) => {
          navigate(`/claims/${claim_num}`);
        }}
      />
    </div>
  );
};
```

## Migration from Angular IndexedDB

The key differences to note when migrating:

1. **Field mapping**: `claim_num` (Angular) → `claim_num` (React), `applicant.surname` → `applicant`
2. **User ID**: You'll need to provide the `userID` explicitly in React
3. **Storage**: localStorage instead of IndexedDB, but same persistence behavior
4. **No database queries**: Direct store access instead of Dexie queries
5. **Hook-based**: Use the `useRecentActivity` hook in your React components 
import React from 'react';
import styled from 'styled-components';
import { RecentActivityCard } from '../Cards/RecentActivityCard/RecentActivityCard';
import { useRecentActivity } from '../../Hooks/useRecentActivity';

const RecentActivityListContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 400px;
  overflow-y: auto;
  padding: 1rem;
`;

const EmptyState = styled.div`
  text-align: center;
  color: ${(props) => props?.theme?.ColorsTypographySecondary || '#666'};
  font-style: italic;
  padding: 2rem;
`;

const ClearButton = styled.button`
  align-self: flex-end;
  background: ${(props) => props?.theme?.ColorsButtonColorActionPanelHover || '#007bff'};
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: ${(props) => props?.theme?.RadiusXxs || '4px'};
  cursor: pointer;
  font-size: 0.8rem;
  margin-bottom: 1rem;
  
  &:hover {
    background: ${(props) => props?.theme?.ColorsButtonColorActionPanelHover || '#0056b3'};
  }
`;

interface RecentActivityListProps {
  userID: number; // Required userID for user-specific filtering
  onItemClick?: (claim_num: string, applicant: string) => void;
  showClearButton?: boolean;
}

export const RecentActivityList: React.FC<RecentActivityListProps> = ({
  userID,
  onItemClick,
  showClearButton = true,
}) => {
  const { recentActivities, clearRecentActivity } = useRecentActivity();
  
  // Filter activities for the current user and sort by date
  const userActivities = recentActivities
    .filter(item => item.userID === userID)
    .sort((a, b) => new Date(b.access_date).getTime() - new Date(a.access_date).getTime());

  // Add debugging logs
  console.log('RecentActivityList render:', {
    userID,
    allItems: recentActivities,
    userActivities: userActivities,
    userActivitiesLength: userActivities.length
  });

  const handleItemClick = (item: any) => {
    if (onItemClick) {
      onItemClick(item.claim_num, item.applicant);
    }
  };

  const handleClearAll = () => {
    if (window.confirm('Are you sure you want to clear all recent activities?')) {
      clearRecentActivity();
    }
  };

  if (userActivities.length === 0) {
    console.log('RecentActivityList: No activities found, showing empty state');
    return (
      <RecentActivityListContainer>
        <EmptyState>
          No recent activities found. Click on claims to see them here.
        </EmptyState>
      </RecentActivityListContainer>
    );
  }

  console.log('RecentActivityList: Rendering activities:', userActivities);

  return (
    <RecentActivityListContainer>
      {showClearButton && userActivities.length > 0 && (
        <ClearButton onClick={handleClearAll}>
          Clear All
        </ClearButton>
      )}
      
      {userActivities.map((item) => (
        <RecentActivityCard
          key={item.id}
          claimnum={item.claim_num} // Map claim_num to claimnum for the card component
          applicant={item.applicant}
          access_date={item.access_date}
          onClick={() => handleItemClick(item)}
        />
      ))}
    </RecentActivityListContainer>
  );
}; 
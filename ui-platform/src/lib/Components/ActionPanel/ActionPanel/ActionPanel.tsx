import styled from 'styled-components';
import { ActionPanelConfig } from '../../../Engine/models/action-panel.config';

import { ActionPanelRenderer } from '../../../Engine/components/ActionPanelRenderer';
import { ActionConfig } from '../../../Engine/models/action.config';
import { ActionPanelContainer } from '../ActionPanelContainer/ActionPanelContainer';
import { ActionPanelOverlay } from '../ActionPanelOverlay/ActionPanelOverlay';
import { UserPanelHeader } from '../UserPanelHeader/UserPanelHeader';
import { useActionPanel } from '../hooks/useActionPanel';
import React from 'react';

/**
 * Hidden container for the WorkflowActionPanelSearch component
 * This ensures the search component is always mounted to handle search triggers
 */
const HiddenSearchContainer = styled.div`
  position: absolute;
  top: -9999px;
  left: -9999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
  pointer-events: none;
`;

/**
 * Props interface for the ActionPanel component
 * @interface ActionPanelProps
 */
export type ActionPanelProps = {
  /** Array of configuration objects that define the panel's content and behavior */
  configs: ActionPanelConfig[];
  /** Optional identifier for the currently active view */
  activeView?: string;
  /** Map of component names to their implementations */
  componentMap: any;
  /** Optional submit handler function */
  submit?: any;
  /** Optional data fetching function */
  fetcher?: any;
  /** Optional object containing client-specific data */
  clientDataObject?: { [id: string]: any };
  callClientAction: (config: ActionConfig) => void;
  /** Props for the WorkflowActionPanelSearch component */
  searchProps?: {
    searchUrl: string;
    token: string;
    tokenPrefix: string;
    onSearchTrigger?: (searchTerm: string) => void;
  };
};

/**
 * ActionPanel is a dynamic side panel component that can display various types of content
 * based on configuration. It supports multiple views that can be toggled through a control
 * panel, with smooth transitions between different content states.
 *
 * Features:
 * - Configurable content views through ActionPanelConfig objects
 * - Smooth fade transitions between different views
 * - Integrated overlay system for content display
 * - Customizable header with user panel integration
 * - Component mapping system for dynamic content rendering
 * - Always-mounted search component for handling search triggers
 *
 * @component
 * @param {ActionPanelProps} props - Component properties
 * @param {ActionPanelConfig[]} props.configs - Array of panel configurations
 * @param {string} [props.activeView] - Currently active view identifier
 * @param {Object} props.componentMap - Map of component names to implementations
 * @param {Function} [props.submit] - Optional submit handler
 * @param {Function} [props.fetcher] - Optional data fetching function
 * @param {Object} [props.clientDataObject] - Optional client-specific data
 * @param {Object} [props.searchProps] - Props for the WorkflowActionPanelSearch component
 *
 * @example
 * ```tsx
 * const configs = [{
 *   id: 'panel1',
 *   title: 'User Profile',
 *   icon: 'user',
 *   component: 'UserProfileComponent',
 *   actionLevel: 'topControls'
 * }];
 *
 * const componentMap = {
 *   UserProfileComponent: UserProfile
 * };
 *
 * <ActionPanel
 *   configs={configs}
 *   componentMap={componentMap}
 *   activeView="panel1"
 *   clientDataObject={{ userId: '123' }}
 *   searchProps={{
 *     searchUrl: '/api/search',
 *     token: 'auth-token',
 *     tokenPrefix: 'Bearer'
 *   }}
 * />
 * ```
 */
export function ActionPanel(props: ActionPanelProps) {
  const {
    isOverlayVisible,
    setIsOverlayVisible,
    activeConfig,
    setActiveConfig,
  } = useActionPanel(props?.configs, props?.activeView);

  // Get the WorkflowActionPanelSearch component from componentMap
  const WorkflowActionPanelSearch = props.componentMap?.WorkflowActionPanelSearch;

  return (
    <>
      <ActionPanelContainer
        configs={props.configs}
        setIsOverlayVisible={setIsOverlayVisible}
        isOverlayVisible={isOverlayVisible}
        activeConfig={activeConfig}
        setActiveConfig={setActiveConfig}
      />

      {/* Always render WorkflowActionPanelSearch if available and searchProps provided */}
      {WorkflowActionPanelSearch && props.searchProps && (
        <HiddenSearchContainer>
          <WorkflowActionPanelSearch {...props.searchProps} />
        </HiddenSearchContainer>
      )}

      <ActionPanelOverlay visible={isOverlayVisible}>
        <>
          {activeConfig?.title && (
            <UserPanelHeader title={activeConfig?.title} />
          )}
          {activeConfig && (
            <ActionPanelRenderer
              callClientAction={props.callClientAction}
              actionPanelConfig={activeConfig}
              componentMap={props.componentMap}
              {...{ submit: props?.submit, fetcher: props?.fetcher }}
            />
          )}
        </>
      </ActionPanelOverlay>
    </>
  );
}

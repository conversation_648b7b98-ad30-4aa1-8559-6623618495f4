import { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { DragAndDrop, DragAndDropProps } from './DragAndDrop';

export default {
  title: 'Components/DragAndDrop',
  component: DragAndDrop,
  argTypes: {
    uploadMode: {
      control: { type: 'select' },
      options: ['binary', 'base64'],
      description: 'Upload mode - binary (FormData) or base64 (JSON)',
    },
    fileTypesAllowed: {
      control: { type: 'object' },
      description: 'Array of allowed file types',
    },
    fileSizeLimit: {
      control: { type: 'number' },
      description: 'Maximum file size in bytes',
    },
    toggleButton: {
      control: { type: 'boolean' },
      description: 'Whether to show the browse button',
    },
    disabled: {
      control: { type: 'boolean' },
      description: 'Whether the component is disabled',
    },
    iconLeft: {
      control: { type: 'text' },
      description: 'Icon to display on the left',
    },
    iconRight: {
      control: { type: 'text' },
      description: 'Icon to display on the right',
    },
  },
} as Meta<DragAndDropProps>;

type Story = StoryObj<DragAndDropProps>;

export const Overview: Story = {
  args: {
    name: 'document',
    label: 'Document Upload',
    filename: 'Drag & drop here',
    uploadMode: 'binary',
    fileTypesAllowed: ['pdf'],
    fileSizeLimit: 5242880,
    toggleButton: true,
    instructions: 'Upload a PDF file (max 5MB)',
  },
};

export const BinaryUpload: Story = {
  args: {
    name: 'binaryUpload',
    label: 'Binary Upload (Default)',
    onDrop: (acceptedFiles, fileRejections, evt) => {
      console.log('Binary upload - accepted files:', acceptedFiles);
      console.log('Binary upload - rejected files:', fileRejections);
    },
    uploadMode: 'binary',
    purpose: 'document',
    fileTypesAllowed: ['pdf', 'doc'],
    fileSizeLimit: 5242880, // 5MB
    toggleButton: true,
    iconLeft: 'file-07',
    instructions: 'Drag and drop a PDF or DOC file (max 5MB) - uses FormData',
  },
};

export const Base64Upload: Story = {
  args: {
    name: 'base64Upload',
    label: 'Base64 Upload',
    onDrop: (acceptedFiles, fileRejections, evt) => {
      console.log('Base64 upload - onDrop called but handled by onBase64Upload');
    },
    uploadMode: 'base64',
    jobId: '12345',
    purpose: 'receipt',
    onBase64Upload: (data) => {
      console.log('Base64 upload data:', {
        data: data.data.substring(0, 50) + '...', // Show first 50 chars
        filename: data.filename,
        job_id: data.job_id,
        purpose: data.purpose,
      });
    },
    fileTypesAllowed: ['pdf', 'image'],
    fileSizeLimit: 10485760, // 10MB
    toggleButton: true,
    iconLeft: 'file-07',
    instructions: 'Drag and drop a PDF or image file (max 10MB) - converts to base64',
  },
};

export const ImageUpload: Story = {
  args: {
    name: 'imageUpload',
    label: 'Image Upload (Base64)',
    onDrop: (acceptedFiles, fileRejections, evt) => {
      console.log('Image upload - onDrop called but handled by onBase64Upload');
    },
    uploadMode: 'base64',
    jobId: '67890',
    purpose: 'profile-picture',
    onBase64Upload: (data) => {
      console.log('Image upload data:', {
        filename: data.filename,
        job_id: data.job_id,
        purpose: data.purpose,
        dataLength: data.data.length,
      });
    },
    fileTypesAllowed: ['image'],
    fileSizeLimit: 2097152, // 2MB
    toggleButton: true,
    iconLeft: 'file-07',
    instructions: 'Upload profile picture (JPG, PNG, max 2MB)',
  },
};

export const InvoiceUpload: Story = {
  args: {
    name: 'invoiceUpload',
    label: 'Invoice Upload (Base64)',
    onDrop: (acceptedFiles, fileRejections, evt) => {
      console.log('Invoice upload - onDrop called but handled by onBase64Upload');
    },
    uploadMode: 'base64',
    jobId: '99999',
    purpose: 'invoice',
    onBase64Upload: (data) => {
      // Simulate API call with additional metadata
      const payload = {
        ...data,
        metadata: {
          uploadedAt: new Date().toISOString(),
          category: 'invoice',
          priority: 'high',
        },
      };
      console.log('Invoice upload payload:', payload);
    },
    fileTypesAllowed: ['pdf'],
    fileSizeLimit: 5242880, // 5MB
    toggleButton: true,
    iconLeft: 'file-07',
    instructions: 'Upload invoice PDF files only (max 5MB)',
  },
};

export const Disabled: Story = {
  args: {
    name: 'disabledUpload',
    label: 'Disabled Upload',
    onDrop: (acceptedFiles, fileRejections, evt) => {
      console.log('Disabled upload - should not be called');
    },
    uploadMode: 'binary',
    purpose: 'document',
    fileTypesAllowed: ['pdf'],
    fileSizeLimit: 5242880,
    toggleButton: true,
    iconLeft: 'file-07',
    instructions: 'This upload is disabled',
    disabled: true,
  },
};

export const NoButton: Story = {
  args: {
    name: 'noButtonUpload',
    label: 'Upload Without Button',
    onDrop: (acceptedFiles, fileRejections, evt) => {
      console.log('No button upload - accepted files:', acceptedFiles);
    },
    uploadMode: 'binary',
    purpose: 'document',
    fileTypesAllowed: ['pdf'],
    fileSizeLimit: 5242880,
    toggleButton: false,
    iconLeft: 'file-07',
    instructions: 'Drag and drop only - no browse button',
  },
};

export const LargeFileLimit: Story = {
  args: {
    name: 'largeFileUpload',
    label: 'Large File Upload (Base64)',
    onDrop: (acceptedFiles, fileRejections, evt) => {
      console.log('Large file upload - onDrop called but handled by onBase64Upload');
    },
    uploadMode: 'base64',
    jobId: 'large-file-001',
    purpose: 'video-documentation',
    onBase64Upload: (data) => {
      console.log('Large file upload data:', {
        filename: data.filename,
        job_id: data.job_id,
        purpose: data.purpose,
        dataSize: `${(data.data.length * 0.75 / 1024 / 1024).toFixed(2)} MB`, // Approximate size
      });
    },
    fileTypesAllowed: ['pdf', 'image'],
    fileSizeLimit: 52428800, // 50MB
    toggleButton: true,
    iconLeft: 'file-07',
    instructions: 'Upload large files (PDF or images, max 50MB)',
  },
};

export const ErrorState: Story = {
  args: {
    name: 'errorUpload',
    label: 'Upload with Error',
    onDrop: (acceptedFiles, fileRejections, evt) => {
      console.log('Error upload - accepted files:', acceptedFiles);
      console.log('Error upload - rejected files:', fileRejections);
    },
    uploadMode: 'binary',
    purpose: 'document',
    fileTypesAllowed: ['pdf'],
    fileSizeLimit: 5242880,
    toggleButton: true,
    iconLeft: 'file-07',
    instructions: 'This upload has an error state',
    error: {
      type: 'required',
      message: 'Please upload a file',
    },
  },
};

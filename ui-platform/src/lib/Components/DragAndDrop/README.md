# DragAndDrop Component

The DragAndDrop component now supports both binary and base64 file uploads with configurable parameters.

## Features

- **Binary Upload Mode**: Traditional file upload with FormData
- **Base64 Upload Mode**: File conversion to base64 with custom parameters
- **Configurable Parameters**: Support for job_id, purpose, and other custom fields
- **File Validation**: File type and size restrictions
- **Error Handling**: Comprehensive error handling for both modes

## Usage

### Binary Upload (Default)

```tsx
<DragAndDrop
  name="fileUpload"
  onDrop={(acceptedFiles, fileRejections, evt) => {
    const form = new FormData();
    form.append('file', acceptedFiles[0]);
    form.append('purpose', 'document');
    form.append('url', '/api/upload');
    form.append('list', 'False');
    
    fetcher.submit(form, {
      method: 'post',
      action: '/api/upload',
      encType: 'multipart/form-data',
    });
  }}
  uploadMode="binary"
  purpose="document"
  fileTypesAllowed={['pdf', 'doc', 'docx']}
  fileSizeLimit={5242880} // 5MB
/>
```

### Base64 Upload

```tsx
<DragAndDrop
  name="fileUpload"
  uploadMode="base64"
  jobId="12345"
  purpose="receipt"
  onBase64Upload={(data) => {
    // data contains: { data: string, filename: string, job_id: string, purpose: string }
    console.log('Base64 data:', data.data);
    console.log('Filename:', data.filename);
    console.log('Job ID:', data.job_id);
    console.log('Purpose:', data.purpose);
    
    // Send to API with the correct payload structure
    fetch('/api/upload-base64', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
  }}
  fileTypesAllowed={['pdf', 'jpg', 'png']}
  fileSizeLimit={10485760} // 10MB
/>
```

## API Payload Structure

### Binary Upload
```
------WebKitFormBoundaryByOOkekI3OplKY91
Content-Disposition: form-data; name="file"; filename="document.pdf"
Content-Type: application/pdf

[Binary file data]
```

### Base64 Upload
```json
{
  "data": "JVBERi0xLjQKJcOkw7zDtsO...",
  "filename": "document.pdf",
  "job_id": "12345",
  "purpose": "receipt"
}
```

## Configuration Options

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `uploadMode` | `'binary' \| 'base64'` | `'binary'` | Upload mode selection |
| `jobId` | `string \| number` | - | Job ID for base64 uploads |
| `purpose` | `string` | - | Purpose for base64 uploads |
| `onBase64Upload` | `function` | - | Callback for base64 uploads |
| `fileTypesAllowed` | `Files[]` | `['pdf']` | Allowed file types |
| `fileSizeLimit` | `number` | `5242880` | Maximum file size in bytes |

## Form Builder Integration

When using with the form builder, configure the control like this:

```tsx
const dragAndDropConfig: DragAndDropControlConfig = {
  type: 'drag-and-drop',
  name: 'documentUpload',
  uploadMode: 'base64',
  jobId: '12345',
  purpose: 'receipt',
  onBase64Upload: (data) => {
    // Handle base64 upload
    console.log('Upload data:', data);
  },
  fileTypesAllowed: ['pdf', 'jpg', 'png'],
  fileSizeLimit: 10485760,
  url: '/api/upload',
  action: '/api/upload',
  purpose: 'receipt',
  // ... other config options
};
```

## Error Handling

The component handles various error scenarios:

- **File Type Validation**: Rejects files not in `fileTypesAllowed`
- **File Size Validation**: Rejects files larger than `fileSizeLimit`
- **Base64 Conversion Errors**: Handles FileReader errors gracefully
- **Network Errors**: Provides error feedback for upload failures

## Migration Guide

### From Binary to Base64

1. Add `uploadMode="base64"` to your DragAndDrop component
2. Provide `jobId` and `purpose` props
3. Implement `onBase64Upload` callback
4. Update your API endpoint to handle the new payload structure

### Example Migration

**Before (Binary):**
```tsx
<DragAndDrop
  onDrop={(files) => {
    const form = new FormData();
    form.append('file', files[0]);
    form.append('purpose', 'receipt');
    form.append('list', 'False');
    // Submit form...
  }}
/>
```

**After (Base64):**
```tsx
<DragAndDrop
  uploadMode="base64"
  jobId="12345"
  purpose="receipt"
  onBase64Upload={(data) => {
    // Submit JSON payload with base64 data
    fetch('/api/upload', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  }}
/>
``` 
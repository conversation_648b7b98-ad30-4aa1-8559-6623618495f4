import Keycloak from 'keycloak-js';
import React, { useMemo, useState } from 'react';
import styled, { useTheme } from 'styled-components';
import { ActionConfig } from '../../../Engine/models/action.config';
import { useFilteringEngineStore } from '../../../Engine/hooks/useFilteringEngineStore';
import { useWorkflowViewStore } from '../../../Engine/hooks/useWorkflowViewStore';
import { ToolbarConfig } from '../../../Engine/models/toolbar.config';
import { IconButton } from '../../Buttons/IconButton/IconButton';
import { ToolbarButton } from '../../Buttons/ToolbarButton/ToolbarButton';
import { IconTypes } from '../../Icons';
import { ProfileDropdownMenu } from '../../Menu';
import { ToolBarNavMenu } from '../ToolBarNavMenu';
import { TextButton } from '../../Buttons/TextButton/TextButton';
import { Link } from 'react-router-dom';

const ToolbarWithIconButtonContainer = styled.div`
  width: 100%;
  height: 53px;
  position: relative;
  background-color: ${(props: any) => props?.theme.ColorsStrokesInverse};
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: space-between;
  /* padding: 0px 16px; */
  box-sizing: border-box;
  text-align: left;
  font-size: ${(props: any) => props.theme.FontSize1}px;
  color: ${(props: any) => props?.theme.ColorsTypographySecondary};
  font-family: ${(props: any) => props.theme.FontFamiliesInter};
  grid-area: Toolbar;
`;

const TitleContainer = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: ${(props: any) => props.theme.FontSize2}px;
  font-weight: 600;
  color: ${(props: any) => props?.theme.ColorsTypographyPrimary};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 40%;
`;

const IconButtonContainer = styled.div`
  // margin-left: auto;
  display: flex;
  align-items: flex-end;
  gap: ${(props: any) => props?.theme.GapLg};
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
`;

const PingButton = styled(IconButton)`
  background-color: ${(props: any) => props?.theme.ColorsTabsToolbarPrimary};
  border: 1px solid ${(props: any) => props?.theme.ColorsStrokesDefault};
  align-self: flex-end;

  &:hover {
    background-color: ${(props: any) => props?.theme.ColorsTabsToolbarHover};
  }

  &:disabled {
    border: 1px solid ${(props: any) => props?.theme.ColorsStrokesGrey};
    background-color: transparent;
  }

  &:active {
    background-color: ${(props: any) => props?.theme.ColorsTabsToolbarActivated};
    box-shadow: 0px 0px 5.2px 0px #fbfeff;
  }
`;

const StyledProfileDropdownMenu = styled(ProfileDropdownMenu)`
  margin: 0px 10px;
`;

const RightsideTabsContainer = styled.div`
  margin-right: 20px;
  margin-left: auto;
  display: flex;
  align-items: flex-end;
`;

interface Props {
  activeModuleName: string;
  moduleTabs: {
    name: string;
    path: string;
    alert?: boolean;
    alertIcon?: { type: IconTypes; color?: 'string' };
  }[];
  rightsideTabs: { name: string; path: string }[];
  toolbarConfig: ToolbarConfig;
  keycloak?: Keycloak;
  callClientAction: (config: ActionConfig) => void;
}

/**
 * The Toolbar component displays a toolbar with module tabs and menu items.
 *
 * @param {Props} props - The props for the component.
 * @param {string} props.activeModuleName - The name of the active module.
 * @param {{ name: string; path: string }[]} props.moduleTabs - The list of tabs for the module.
 * @param {{ name: string; path: string }[]} [props.rightsideTabs = []] - The list of tabs for the right side of the toolbar.
 * @param {ToolbarConfig} props.toolbarConfig - The configuration for the toolbar.
 * @param {Keycloak} [props.keycloak] - The Keycloak instance for authentication.
 * @returns {JSX.Element} The rendered component.
 */
export const Toolbar = ({
  // toolbarTabsConfig: toolbarTabs,
  activeModuleName,
  moduleTabs,
  rightsideTabs = [],
  toolbarConfig,
  keycloak,
  callClientAction,
}: Props) => {
  const {
    email,
    username,
    menuItems,
    // optionsHandler,
    // avatarHandler,
    profileDisabled = false,
    pingDisabled = false,
    // pingHandler,
    buttonText,
    showNeedHelpButton,
    displaySort,
    displayWorkflowViewSwitcher,
    toggleWorkflowView,
    buttonOnClick,
    modulesList,
    image,
    alertIndicator,
    alertIndicatorIcon,
  } = toolbarConfig;

  const profilePic = useMemo(() => image, [image]);
  const alert = useMemo(() => alertIndicator, [alertIndicator]);
  const alertIcon = useMemo(() => alertIndicatorIcon, [alertIndicatorIcon]);
  const setToggleSort = useFilteringEngineStore(state => state.setToggleSort);
  const { toggleView } = useWorkflowViewStore();

  const iconColor = useTheme().ColorsStrokesDefault;

  const onMenuItemClick = async (item: any) => {
    if (typeof item.onClick !== 'function') {
      console.log({ onCLick: item.onClick });
      for (const cf of item.onClick || []) {
        await callClientAction(cf);
      }
    }
  };
  return (
    <ToolbarWithIconButtonContainer>
      <div>
        <ToolBarNavMenu
          {...{
            moduleTabs,
            modulesList,
            activeModuleName,
            alertIndicator: alert,
            alertIndicatorIcon: alertIcon,
          }}
        />
      </div>
      {toolbarConfig.title && <TitleContainer>{toolbarConfig.title}</TitleContainer>}
      <RightsideTabsContainer>
        <ToolBarNavMenu position="right" moduleTabs={rightsideTabs} />
      </RightsideTabsContainer>
      <IconButtonContainer>
        {pingDisabled && (
          <PingButton
            icon="users-plus"
            disabled={pingDisabled}
            // onClick={pingHandler}
          />
        )}
         {!!showNeedHelpButton && (
          <Link 
          style={{
            textDecoration: 'none',
            color: 'inherit',
            marginBottom: '5px'
          }}
          to="https://4-sure.atlassian.net/servicedesk/customer/portal/15" 
          target='_blank'>
          <TextButton
            btnValue={'Need help?'}
            actiontype='attention'
          ></TextButton>
          </Link>
        )}
        {!!buttonText && (
          <ToolbarButton
            buttonText={buttonText}
            onClick={buttonOnClick}
            callClientAction={callClientAction}
          ></ToolbarButton>
        )}
        {displaySort && window.location.pathname.includes('workflow') && (
           <IconButton
           icon="switch-vertical"
           style={{
            marginTop: '20px'
           }}
           onClick={(ev) => {
             ev.stopPropagation();
             setToggleSort();
           }}
         />
        )}
        {displayWorkflowViewSwitcher && window.location.pathname.includes('workflow') && ( <IconButton
           icon="eye-off"
           style={{
            marginTop: '20px'
           }}
           onClick={(ev) => {
             ev.stopPropagation();
             toggleView();
           }}
         />)}
        <StyledProfileDropdownMenu
          items={menuItems ?? []}
          size="small"
          orientation="right"
          onMenuItemClick={onMenuItemClick}
          disabled={profileDisabled}
          {...{
            email,
            username,
            image: profilePic,
          }}
        />
      </IconButtonContainer>
    </ToolbarWithIconButtonContainer>
  );
};

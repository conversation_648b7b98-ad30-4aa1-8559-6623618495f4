import * as React from 'react';
import { FC, useCallback, useEffect, useRef, useState } from 'react';

interface Props {
  invoiceData: {
    data: string;
    fileName: string;
    contentType?: string;
  };
}

type DownloadStatus = 'idle' | 'downloading' | 'completed' | 'error';

const base64ToBlob = (
  base64: string,
  contentType = 'application/pdf',
  sliceSize = 512
) => {
  console.log('[AutoFileDownload] Converting base64 to blob:', {
    dataLength: base64.length,
    contentType,
    sliceSize,
  });

  try {
    const byteCharacters = atob(base64);
    console.log(
      '[AutoFileDownload] Base64 decoded successfully, byte length:',
      byteCharacters.length
    );

    const byteArrays: Uint8Array[] = [];
    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);
      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
    const blob = new Blob(byteArrays, { type: contentType });
    console.log('[AutoFileDownload] Blob created successfully:', {
      size: blob.size,
      type: blob.type,
    });
    return blob;
  } catch (error) {
    console.error('[AutoFileDownload] Error converting base64 to blob:', error);
    throw new Error('Invalid base64 data provided');
  }
};

const createDownloadLink = (
  base64: string,
  contentType = 'application/pdf'
) => {
  console.log(
    '[AutoFileDownload] Creating download link with content type:',
    contentType
  );
  const blob = base64ToBlob(base64, contentType);
  const url = URL.createObjectURL(blob);
  console.log('[AutoFileDownload] Download URL created:', url);
  return url;
};

/**
 * AutoFileDownload component
 *
 * A component that automatically downloads a file when the component is mounted.
 * Uses state-based approach to handle timing and prevent race conditions.
 *
 * @param {Props} invoiceData - The data to be downloaded
 * @returns {React.FC} AutoFileDownload component
 */
export const AutoFileDownloadComponent: FC<Props> = ({ invoiceData }) => {
  const linkRef = useRef<HTMLAnchorElement | null>(null);
  const downloadUrlRef = useRef<string | null>(null);
  const [downloadStatus, setDownloadStatus] = useState<DownloadStatus>('idle');
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isComponentReady, setIsComponentReady] = useState(false);
  const isDownloadingRef = useRef(false);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);

  console.log('[AutoFileDownload] Component rendered with data:', {
    hasData: !!invoiceData.data,
    dataLength: invoiceData.data?.length || 0,
    fileName: invoiceData.fileName,
    contentType: invoiceData.contentType || 'application/pdf',
    downloadStatus,
    isComponentReady,
  });

  // Mark component as ready after initial render
  useEffect(() => {
    setIsComponentReady(true);
  }, []);

  // Cleanup function to revoke URLs
  const cleanupDownloadUrl = useCallback(() => {
    if (downloadUrlRef.current) {
      console.log('[AutoFileDownload] Cleaning up download URL');
      URL.revokeObjectURL(downloadUrlRef.current);
      downloadUrlRef.current = null;
    }
  }, []);

  // Handle download process
  const handleDownload = useCallback(async () => {
    // Prevent multiple concurrent downloads
    if (isDownloadingRef.current) {
      console.log('[AutoFileDownload] Download already in progress, skipping');
      return;
    }

    if (!invoiceData.data) {
      console.warn('[AutoFileDownload] No data provided, skipping download');
      setError('No data provided');
      setDownloadStatus('error');
      return;
    }

    if (!linkRef.current) {
      console.warn(
        '[AutoFileDownload] Link ref not available, scheduling retry...'
      );

      // Clear any existing retry timeout
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }

      retryTimeoutRef.current = setTimeout(() => {
        if (isMountedRef.current) {
          if (linkRef.current) {
            handleDownload();
          } else {
            setError('Download element not available after retries');
            setDownloadStatus('error');
          }
        }
      }, 100);
      return;
    }

    console.log('[AutoFileDownload] Starting download process');
    isDownloadingRef.current = true;
    setDownloadStatus('downloading');
    setError(null);

    try {
      const contentType = invoiceData.contentType || 'application/pdf';
      console.log('[AutoFileDownload] Using content type:', contentType);

      const href = createDownloadLink(invoiceData.data, contentType);
      downloadUrlRef.current = href;
      setDownloadUrl(href);

      const linkEl = linkRef.current;

      console.log('[AutoFileDownload] Setting up download link:', {
        href: href.substring(0, 50) + '...',
        fileName: invoiceData.fileName,
      });

      linkEl.href = href;
      linkEl.download = invoiceData.fileName;

      console.log('[AutoFileDownload] Triggering download click');
      linkEl.click();

      console.log('[AutoFileDownload] Download triggered');
      setDownloadStatus('completed');

      // Clean up the URL after a delay to ensure download starts
      setTimeout(() => {
        if (isMountedRef.current) {
          cleanupDownloadUrl();
        }
      }, 1000);
    } catch (error) {
      console.error('[AutoFileDownload] Error creating download link:', error);
      if (isMountedRef.current) {
        setError(
          error instanceof Error ? error.message : 'Unknown error occurred'
        );
        setDownloadStatus('error');
      }
      cleanupDownloadUrl();
    } finally {
      isDownloadingRef.current = false;
    }
  }, [invoiceData, cleanupDownloadUrl]);

  // Trigger download when component is ready and has data
  useEffect(() => {
    if (isComponentReady && invoiceData.data) {
      handleDownload();
    }
  }, [isComponentReady, invoiceData, handleDownload]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      cleanupDownloadUrl();
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, [cleanupDownloadUrl]);

  // Get status message for display
  const getStatusMessage = () => {
    switch (downloadStatus) {
      case 'idle':
        return 'Preparing download...';
      case 'downloading':
        return 'Downloading...';
      case 'completed':
        return 'Download completed';
      case 'error':
        return `Download failed: ${error || 'Unknown error'}`;
      default:
        return '';
    }
  };

  return (
    <div className="auto-file-download">
      <a
        ref={linkRef}
        href={downloadUrl || undefined}
        download={invoiceData.fileName}
        className={`download-link ${downloadStatus}`}
        style={{
          display: 'inline-block',
          padding: '8px 16px',
          textDecoration: 'none',
          borderRadius: '4px',
          cursor: downloadStatus === 'downloading' ? 'wait' : 'pointer',
          opacity: downloadStatus === 'downloading' ? 0.7 : 1,
          backgroundColor: downloadStatus === 'error' ? '#fee' : '#f0f0f0',
          color: downloadStatus === 'error' ? '#c00' : '#333',
          border:
            downloadStatus === 'error' ? '1px solid #fcc' : '1px solid #ccc',
        }}
      >
        {getStatusMessage()}
      </a>

      {downloadStatus === 'error' && (
        <div
          className="download-error"
          style={{
            marginTop: '8px',
            fontSize: '12px',
            color: '#c00',
          }}
        >
          {error}
        </div>
      )}
    </div>
  );
};

export default AutoFileDownloadComponent;

import React, { useState } from 'react';
import { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ViewSwitcher, ViewType, ListView } from './ViewSwitcher';
import { JobCardListWithNormalSize } from '../../../Fragments/JobCardListWithNormalSize/JobCardListWithNormalSize';

const mockJobs = [
  {
    id: 1,
    skill: 'Plumbing',
    sp: '<PERSON> Team leader',
    date: '2024-04-04',
    time: 'Between 10:00 and 14:00',
    state: 0,
    stateTextDislplay: '0: Job Created',
    description: 'Invoice the allowable fees',
    sla: '2d 01h 37m',
    claim: {
      applicant: {
        first_name: '<PERSON><PERSON>',
        surname: '<PERSON><PERSON><PERSON><PERSON>',
      },
    },
  },
  {
    id: 2,
    skill: 'Roofing',
    sp: '<PERSON> Team leader',
    date: '2024-04-04',
    time: 'Between 10:00 and 14:00',
    state: 6,
    stateTextDislplay: '0: Job Created',
    description: 'Appoint a team leader',
    sla: '2d 01h 37m',
    customer: '<PERSON>',
    claim: {
      applicant: {
        first_name: '<PERSON><PERSON>',
        surname: '<PERSON><PERSON><PERSON>',
      },
    },
  },
  {
    id: 3,
    skill: '<PERSON>oofing',
    sp: '<PERSON> Botha Team leader',
    date: '2024-04-04',
    time: 'Between 10:00 and 14:00',
    state: 5,
    stateTextDislplay: '0: Job Created',
    description: 'Invoice for a job',
    sla: '2d 01h 37m',
    customer: '<PERSON>fiso Mhlangu',
    claim: {
      applicant: {
        first_name: 'Warrant',
        surname: 'Shondlani',
      },
    },
  },
];

const LinkRouter = ({ children }: { children: React.ReactNode }) => <>{children}</>;
const staffMember = {} as any;
const allInfo = {};
const callClientAction = () => {};
const getMenuItems = () => [];

const meta: Meta<typeof ViewSwitcher> = {
  title: 'Components/Filter/ChangeView/ViewSwitcher',
  component: ViewSwitcher,
};

export default meta;

type Story = StoryObj<typeof ViewSwitcher>;

export const Default: Story = {
  render: () => {
    const [view, setView] = useState<ViewType>('detailed');
    return (
      <>
        <ViewSwitcher initialView={view} onViewChange={setView} buttonStyle={{ alignSelf: 'flex-start' }} />
        {view === 'detailed' ? (
          <JobCardListWithNormalSize
            jobs={mockJobs}
            LinkRouter={LinkRouter}
            staffMember={staffMember}
            allInfo={allInfo}
            callClientAction={callClientAction}
            getMenuItems={getMenuItems}
            scrollable={false}
          />
        ) : (
          <ListView jobs={mockJobs} />
        )}
      </>
    );
  },
};

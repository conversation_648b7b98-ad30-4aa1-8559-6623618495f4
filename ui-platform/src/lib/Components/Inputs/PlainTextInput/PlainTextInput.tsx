import React, {
  ChangeEvent,
  FC,
  HTMLInputTypeAttribute,
  useState,
} from 'react';
import {
  Control,
  Controller,
  FieldError,
  RegisterOptions,
} from 'react-hook-form';
import styled, { css } from 'styled-components';

interface PlainTextInputProps {
  placeholder?: string;
  icon?: React.ReactNode;
  position?: 'left' | 'right' | 'none';
  error?: FieldError | undefined | null;
  type?: HTMLInputTypeAttribute | undefined;
  rules?: RegisterOptions;
  name: string;
  control?: Control;
  underline?: boolean;
  label?: string;
  instructions?: string;
  value?: string;
  disabled?: boolean;
  className?: string;
  state?: 'default' | 'display-only';
  onFocus?: (ev: any) => any;
}

const Container = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  width: 100%;
  gap: ${(props) => props.theme.SpacingXs};
`;

const Label = styled.label`
  margin-bottom: ${(props) => props.theme.SpacingXs};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
`;

const StyledPlainInputTextWrapper = styled.div<{
  error?: any;
  hasIconLeft: boolean;
  state?: 'default' | 'display-only';
}>`
  display: grid;
  grid-template-columns: ${(props) =>
    props.hasIconLeft ? 'auto 1fr' : '1fr auto'};
  /* width: 100%; */
  height: auto;
  padding: ${(props) => props.theme.SpacingSm} ${(props) => props.theme.GapSm};
  align-items: center;
  gap: ${(props) => props.theme.GapSm};
  border-radius: ${(props) => props.theme.RadiusXs};
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  background: ${(props) => props.theme.ColorsInputsPrimary};

  ${({ error, theme }) =>
    error &&
    css`
      border-color: red;
    `}

  ${({ state }) =>
    state === 'display-only' &&
    css`
      border: 1px solid ${(props) => props.theme.ColorsControllersDefault};
      background: ${(props) => props.theme.ColorsInputsNonEditable};
    `}

  &:focus-within {
    border-color: ${(props) => props.theme.ColorsStrokesFocus};
    background: ${(props) => props.theme.ColorsInputsPrimary};
  }
`;

const StyledPlainInputText = styled.input`
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  font-style: normal;
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  line-height: normal;
  width: 100%;
  outline: none;
  border: none;
  background: inherit;

  &::placeholder {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
  }

  &:focus {
    background-color: inherit;
  }
`;

const IconWrapper = styled.div`
  display: grid;
  place-items: center;
`;

const Instruction = styled.div<{ error?: boolean }>`
  margin-top: ${(props) => props.theme.SpacingXs};
  color: ${({ error, theme }) =>
    error ? theme.ColorsUtilityColorError : theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize2}px;
`;

const StyledInstuctionContainer = styled.div`
  margin: unset;
`;

/**
 * A plain text input component that can be used to input text. It supports
 * displaying a label, instructions, and an error message. It also supports
 * displaying an icon to the left or right of the input field.
 *
 * @prop {string} name The name of the input field.
 * @prop {React.ReactNode} label The label of the input field.
 * @prop {React.ReactNode} instructions The instructions of the input field.
 * @prop {string} placeholder The placeholder of the input field.
 * @prop {React.ReactNode} icon The icon of the input field.
 * @prop {'left'|'right'|'none'} position The position of the icon. If 'left',
 * it will be placed to the left of the input field. If 'right', it will be
 * placed to the right of the input field. If 'none', it will not be displayed.
 * @prop {boolean} disabled Whether the input field is disabled.
 * @prop {boolean} error Whether the input field has an error.
 * @prop {React.ReactNode} state The state of the input field. If 'default', it
 * will be displayed as a default input field. If 'display-only', it will be
 * displayed as a read-only input field.
 * @prop {React.FocusEventHandler<HTMLInputElement>} onFocus A function that
 * will be called when the input field is focused.
 */
export const PlainTextInput = ({
  placeholder,
  type = 'text',
  icon,
  position = 'none',
  error,
  label,
  name,
  rules,
  control,
  className,
  disabled,
  instructions,
  state = 'default',
  onFocus,
  value,
}: PlainTextInputProps) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      disabled={disabled}
      render={({
        field: { onChange, value, name: nm, disabled: d = disabled, onBlur },
        fieldState: { error: fieldError = error },
        formState,
      }) => (
        <Container className={className}>
          {label && <Label>{label}</Label>}
          <StyledPlainInputTextWrapper
            error={error}
            hasIconLeft={position === 'left'}
            state={state}
          >
            {position === 'left' && <IconWrapper>{icon}</IconWrapper>}
            <StyledPlainInputText
              type={type}
              name={nm}
              disabled={d}
              placeholder={placeholder}
              value={value || ''}
              onChange={onChange}
              onBlur={onBlur}
              onFocus={onFocus}
              autoComplete="off"
            />
            {position === 'right' && <IconWrapper>{icon}</IconWrapper>}
          </StyledPlainInputTextWrapper>
          {fieldError ? (
            <Instruction error={!!fieldError?.message}>
              {fieldError?.message}
            </Instruction>
          ) : (
            <StyledInstuctionContainer>
              {instructions && <Instruction>{instructions}</Instruction>}
            </StyledInstuctionContainer>
          )}
        </Container>
      )}
    />
  );
};

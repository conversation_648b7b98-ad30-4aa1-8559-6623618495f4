import { useCallback } from 'react';
import useRecentActivityStore from '../stores/RecentActivityStore';
import { IRecentActivity } from '../models/IRecentActivity';

export const useRecentActivity = () => {
  // Subscribe to the store state to ensure re-renders
  const items = useRecentActivityStore((state) => state.items);
  const addItem = useRecentActivityStore((state) => state.addItem);
  const removeItem = useRecentActivityStore((state) => state.removeItem);
  const clearAll = useRecentActivityStore((state) => state.clearAll);
  const getItems = useRecentActivityStore((state) => state.getItems);
  const getItemsByUser = useRecentActivityStore((state) => state.getItemsByUser);

  console.log('useRecentActivity hook: items from store:', items);

  const addRecentActivity = useCallback((userID: number, claim_num: string, applicant: string, access_date: string) => {
    console.log('addRecentActivity', userID, claim_num, applicant, access_date);
    addItem({
      userID,
      claim_num,
      applicant,
      access_date,
    });
  }, [addItem]);

  const removeRecentActivity = useCallback((id: string) => {
    removeItem(id);
  }, [removeItem]);

  const clearRecentActivity = useCallback(() => {
    clearAll();
  }, [clearAll]);

  const getRecentActivities = useCallback((): IRecentActivity[] => {
    return getItems();
  }, [getItems]);

  const getRecentActivitiesByUser = useCallback((userID: number): IRecentActivity[] => {
    return getItemsByUser(userID);
  }, [getItemsByUser]);

  return {
    recentActivities: items,
    addRecentActivity,
    removeRecentActivity,
    clearRecentActivity,
    getRecentActivities,
    getRecentActivitiesByUser,
  };
}; 
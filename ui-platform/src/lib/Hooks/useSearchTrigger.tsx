import { create } from 'zustand';
import { useCallback } from 'react';

interface SearchTriggerStore {
  triggerSearch: (searchTerm: string) => void;
  onSearchTrigger: ((searchTerm: string) => void) | null;
  setOnSearchTrigger: (callback: (searchTerm: string) => void) => void;
  clearOnSearchTrigger: () => void;
}

const useSearchTriggerStore = create<SearchTriggerStore>((set, get) => ({
  triggerSearch: (searchTerm: string) => {
    console.log('[useSearchTrigger] triggerSearch called with:', searchTerm);
    const { onSearchTrigger } = get();
    console.log('[useSearchTrigger] Current onSearchTrigger callback:', onSearchTrigger ? 'exists' : 'null');
    
    if (onSearchTrigger) {
      console.log('[useSearchTrigger] Executing onSearchTrigger callback');
      try {
        onSearchTrigger(searchTerm);
        console.log('[useSearchTrigger] onSearchTrigger callback executed successfully');
      } catch (error) {
        console.error('[useSearchTrigger] Error executing onSearchTrigger callback:', error);
      }
    } else {
      console.warn('[useSearchTrigger] No onSearchTrigger callback registered - search will not be performed');
    }
  },
  onSearchTrigger: null,
  setOnSearchTrigger: (callback: (searchTerm: string) => void) => {
    console.log('[useSearchTrigger] setOnSearchTrigger called with callback: function');
    set({ onSearchTrigger: callback });
    console.log('[useSearchTrigger] onSearchTrigger callback registered successfully');
  },
  clearOnSearchTrigger: () => {
    console.log('[useSearchTrigger] clearOnSearchTrigger called');
    set({ onSearchTrigger: null });
    console.log('[useSearchTrigger] onSearchTrigger callback cleared');
  },
}));

export const useSearchTrigger = () => {
  console.log('[useSearchTrigger] useSearchTrigger hook initialized');
  
  const { triggerSearch, setOnSearchTrigger, clearOnSearchTrigger } = useSearchTriggerStore();

  const registerSearchTrigger = useCallback((callback: (searchTerm: string) => void) => {
    console.log('[useSearchTrigger] registerSearchTrigger called with callback: function');
    
    setOnSearchTrigger(callback);
    
    // Return cleanup function
    return () => {
      console.log('[useSearchTrigger] Cleanup function called - clearing search trigger');
      clearOnSearchTrigger();
    };
  }, [setOnSearchTrigger, clearOnSearchTrigger]);

  console.log('[useSearchTrigger] Returning hook methods');
  return {
    triggerSearch,
    registerSearchTrigger,
  };
}; 
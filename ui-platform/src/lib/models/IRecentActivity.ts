export interface IRecentActivity {
    id?: string; // Auto-generated unique identifier
    userID: number; // User ID for user-specific storage
    claim_num: string; // Changed from claimnum to match Angular
    applicant: string;
    access_date: string;
}

export interface IRecentActivityStore {
    items: IRecentActivity[];
    addItem: (item: Omit<IRecentActivity, 'id'>) => void;
    removeItem: (id: string) => void;
    clearAll: () => void;
    getItems: () => IRecentActivity[];
    getItemsByUser: (userID: number) => IRecentActivity[];
} 
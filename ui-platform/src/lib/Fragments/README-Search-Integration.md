# Search Integration Between Action Panel Components

This document explains how to set up automatic search triggering between the `WorkflowActionPanelSearch` and `ActionPanelRecentActivity` components.

## Overview

When a user clicks on an item in the recent activity list, it automatically triggers a search in the `WorkflowActionPanelSearch` component using the claim number as the search term. This provides a seamless user experience for quickly finding and viewing specific claims.

## How It Works

The integration uses a shared Zustand store (`useSearchTrigger`) to communicate between components:

1. `WorkflowActionPanelSearch` registers itself as a search handler
2. `ActionPanelRecentActivity` triggers searches when items are clicked
3. The search is performed automatically with the claim number as the search term

## Setup Instructions

### 1. Include Components in Action Panel Configuration

Add both components to your action panel configuration:

```typescript
import { ActionPanelConfig } from '@4-sure/ui-platform';

const actionPanelConfig: ActionPanelConfig[] = [
  {
    icon: 'search-sm',
    title: 'Search & Recent Activity',
    layout: {},
    onEnter: [],
    onLeave: [],
    fragments: [
      // Search Panel Fragment
      {
        component: 'WorkflowActionPanelSearch',
        props: {
          searchUrl: '{VITE_API_BASE_URL}/api/v1/search/claims',
          token: '{auth.token}',
          tokenPrefix: 'Bearer',
        },
        layout: {
          marginBottom: '20px',
        },
      },
      // Recent Activity Fragment
      {
        component: 'ActionPanelRecentActivity',
        props: {
          userID: '{auth.userID}',
          showClearButton: true,
        },
        layout: {
          marginTop: '10px',
        },
      },
    ],
    actionLevel: 'bottomControls',
  },
];
```

### 2. Add Components to Component Map

Ensure both components are included in your component map:

```typescript
import { WorkflowActionPanelSearch } from '@4-sure/ui-platform';
import { ActionPanelRecentActivity } from '@4-sure/ui-platform';

const componentMap = {
  WorkflowActionPanelSearch: WorkflowActionPanelSearch,
  ActionPanelRecentActivity: ActionPanelRecentActivity,
  // ... other components
};
```

### 3. Alternative: Separate Action Panels

You can also use the components in separate action panels - the integration will still work:

```typescript
const separateActionPanelsConfig: ActionPanelConfig[] = [
  {
    icon: 'search-sm',
    title: 'Search Claims',
    layout: {},
    onEnter: [],
    onLeave: [],
    fragments: [
      {
        component: 'WorkflowActionPanelSearch',
        props: {
          searchUrl: '{VITE_API_BASE_URL}/api/v1/search/claims',
          token: '{auth.token}',
          tokenPrefix: 'Bearer',
        },
        layout: {},
      },
    ],
    actionLevel: 'topControls',
  },
  {
    icon: 'alarm-clock',
    title: 'Recent Activity',
    layout: {},
    onEnter: [],
    onLeave: [],
    fragments: [
      {
        component: 'ActionPanelRecentActivity',
        props: {
          userID: '{auth.userID}',
          showClearButton: true,
        },
        layout: {},
      },
    ],
    actionLevel: 'bottomControls',
  },
];
```

## User Experience

1. User sees recent activity items in the action panel
2. User clicks on a recent activity item (e.g., claim number "CLM-12345")
3. The search panel automatically:
   - Sets the search term to "CLM-12345"
   - Performs the search
   - Displays the search results
4. User can see the specific claim details in the main content area

## Technical Details

### Components Involved

- **`WorkflowActionPanelSearch`**: Handles search functionality and displays search results
- **`ActionPanelRecentActivity`**: Displays recent activity list and triggers searches
- **`useSearchTrigger`**: Shared hook that manages communication between components

### Search Trigger Flow

1. `ActionPanelRecentActivity` calls `triggerSearch(claim_num)` when an item is clicked
2. `useSearchTrigger` store notifies the registered search handler
3. `WorkflowActionPanelSearch` receives the search term and performs the search
4. Search results are displayed and filtered in the main content area

### Customization

You can customize the search behavior by:

- Modifying the search term format in `ActionPanelRecentActivity`
- Adding additional search parameters in `WorkflowActionPanelSearch`
- Implementing custom search logic in the `performSearch` function

## Example Usage

See the example configuration files:
- `example-action-panel-config.ts` - Complete configuration examples
- `WorkflowActionPanelSearch.stories.tsx` - Storybook examples
- `ActionPanelRecentActivity.stories.tsx` - Storybook examples

## Troubleshooting

### Search Not Triggering

1. Ensure both components are included in the same action panel or different panels
2. Check that the `useSearchTrigger` hook is properly imported
3. Verify that the components are correctly registered in the component map

### Search Results Not Displaying

1. Check the search URL and authentication configuration
2. Verify that the search API returns the expected format
3. Ensure the filtering engine is properly configured

### Recent Activity Not Showing

1. Verify that the `userID` prop is correctly set
2. Check that recent activity data is available in the store
3. Ensure the `useRecentActivity` hook is working properly 
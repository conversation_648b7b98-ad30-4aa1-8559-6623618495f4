import { descend, sort } from 'ramda';
import { useMemo } from 'react';
import { IconTypes } from '../../Components';
import { addObjectProperty } from '../../Utilities/addObjectProperty';

// Improved TypeScript interfaces
export interface DocumentFile {
  id: string | number;
  purpose?: string;
  filename?: string;
  created?: string;
  thumbnail?: string | null;
  mediaType?: string;
  job?: number;
  claim?: number;
  on_maven?: boolean;
  token?: string;
  isBase64?: boolean;
  [key: string]: any; // Allow additional properties for flexibility
}

interface UseDocumentViewFilesProps<T extends DocumentFile> {
  documents: T[];
  dataKey: string;
}

export interface DocumentCategory {
  heading: string;
  match: (doc: DocumentFile) => boolean;
  icon?: IconTypes;
  defaultOpen?: boolean;
}

export const useDocumentViewFiles = <T extends DocumentFile>({
  documents,
  dataKey,
}: UseDocumentViewFilesProps<T>) => {
  const files = useMemo(() => documents, [documents]);
  let quotations: DocumentFile[],
    latestQuotation: DocumentFile,
    photos: DocumentFile[],
    reports: DocumentFile[],
    agreement_of_loss_letters: DocumentFile[],
    latestReport: DocumentFile,
    invoices: DocumentFile[],
    latestInvoice: DocumentFile;
  const latestDocuments: DocumentFile[] = [];

  if (dataKey === 'files') {
    const purposeLimitTo = (purpose: string) => (file: T) =>
      file.purpose?.toLowerCase()?.includes(purpose);
    const fileTypeLimitTo = (type: string) => (file: T) =>
      file.filename?.toLowerCase()?.includes(type);
    // quotations
    quotations = [
      ...files.filter(purposeLimitTo('quotation')),
      ...files.filter(purposeLimitTo('quote')),
    ].map((quote: DocumentFile) => {
      let quoWithExtension: DocumentFile;
      if (quote.filename && !quote.filename.includes('.pdf')) {
        quoWithExtension = addObjectProperty(
          'filename',
          quote.filename + '.pdf',
          quote
        );
        return quoWithExtension;
      }
      return quote;
    }) as DocumentFile[];
    // latest quotation
    latestQuotation = sort(descend((f: any) => new Date(f.created)))(
      quotations
    )[0];

    // Report
    // newReport = (files as any[]).reduce((acc, file) => {
    // 	if (purposeLimitTo('loss_adjuster_team_lead_report')) {
    // 		if (!file.filename.includes('.pdf')) {
    // 			return [...acc, addObjectProperty('filename', file.filename + '.pdf', file)];
    // 		}
    // 		return [...acc, file];
    // 	}
    // 	return [...acc, file];
    // }, []);

    // Invoices
    invoices = files.filter(purposeLimitTo('invoice')).map((inv) => {
      let invWithExtension: DocumentFile;
      if (inv.filename && !inv.filename.includes('.pdf')) {
        invWithExtension = addObjectProperty(
          'filename',
          inv.filename + '.pdf',
          inv
        );
        return invWithExtension;
      }
      return inv;
    });
    latestInvoice = sort(descend((f: any) => new Date(f.created)))(invoices)[0];

    // photos
    photos = [
      ...files.filter(fileTypeLimitTo('.jpg')),
      ...files.filter(fileTypeLimitTo('.png')),
    ];

    //check for reports with no pdf in name
    const extraRep = files.filter(purposeLimitTo('report')).map((rep) => {
      let repWithExtension = {};
      if (rep.filename && !rep.filename.includes('.pdf')) {
        repWithExtension = addObjectProperty(
          'filename',
          rep.filename + '.pdf',
          rep
        );
        return repWithExtension;
      }
      return rep;
    });
    // console.log({ extraRep });
    // reports
    // reports = files.filter(
    // 	file => file.filename?.toLowerCase()?.includes('.pdf') && !(file.purpose?.toLowerCase()?.includes('quot') || file.purpose.toLowerCase().includes('invoice'))
    // );

    // Removed Invoice from the report ::: 'invoice',
    reports = files
      .filter(
        (file) =>
          ['report', 'coc_certificate', 'repudiation_letter'].some(
            (purpose) =>
              file?.purpose && file.purpose.toLowerCase().includes(purpose)
          ) && !file.purpose?.toLowerCase()?.includes('quot')
      )
      .map((file) =>
        file?.filename && !file.filename.includes('.')
          ? { ...file, filename: `${file.filename}.pdf` }
          : file
      );

    // Get and format AOL filenames
    agreement_of_loss_letters = files
      .filter(purposeLimitTo('agreement of loss'))
      .map((aol: any) => {
        let aolWithExtension = {};
        if (!aol.filename.includes('.pdf')) {
          aolWithExtension = addObjectProperty(
            'filename',
            aol.filename + '.pdf',
            aol
          );
          return aolWithExtension;
        }
        return aol;
      });

    // Get and format AOL filenames
    // loss_adjuster_team_lead_report = files.filter(purposeLimitTo('report')).map((aol: any) => {
    // 	let aolWithExtension = {};
    // 	if (!aol.filename.includes('.pdf')) {
    // 		aolWithExtension = addObjectProperty('filename', aol.filename + '.pdf', aol);
    // 		return aolWithExtension;
    // 	}
    // 	return aol;
    // });

    // const latest_aol_letter = sort(descend(f => new Date(f.created)))(agreement_of_loss_letters)[0];

    // Including AOL letters to be included in reports displayed
    reports = [...reports, ...agreement_of_loss_letters];

    // latest report
    latestReport = sort(descend((f: any) => new Date(f.created)))(reports)[0];

    // Latest report and quote
    if (latestReport) {
      latestDocuments.push(latestReport);
    }
    if (latestInvoice) {
      latestDocuments.push(latestInvoice);
    }
    if (latestQuotation) {
      latestDocuments.push(latestQuotation);
    }
    // console.log('latest report ', latestReport);
    return {
      files,
      quotations,
      latestQuotation,
      reports,
      latestReport,
      invoices,
      latestInvoice,
      latestDocuments,
      photos,
    };
  }
  if (dataKey === 'photos') {
    const fileTypeLimitTo = (type: string) => (file: T) =>
      file.filename?.toLowerCase()?.includes(type);
    photos = [
      ...files.filter(fileTypeLimitTo('.jpg')),
      ...files.filter(fileTypeLimitTo('.png')),
    ];
    return {
      photos,
    };
  }

  return {
    [dataKey]: documents,
  };
};

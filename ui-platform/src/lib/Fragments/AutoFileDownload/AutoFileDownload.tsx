import React from 'react';
import { AutoFileDownloadComponent } from '../../Components/AutoFileDownload/AutoFileDownload';

interface AutoFileDownloadProps {
  invoiceData: {
    data: string;
    fileName: string;
    contentType?: string;
  };
}

/**
 * AutoFileDownload Fragment
 *
 * A fragment that provides automatic file download functionality.
 * Wraps the AutoFileDownload component with fragment-specific logic.
 *
 * @param {AutoFileDownloadProps} props - The fragment props
 * @returns {JSX.Element} The AutoFileDownload fragment
 */
export function AutoFileDownload({
  invoiceData,
}: AutoFileDownloadProps): JSX.Element {
  return <AutoFileDownloadComponent invoiceData={invoiceData} />;
}

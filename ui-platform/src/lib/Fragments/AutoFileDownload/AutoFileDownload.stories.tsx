import { Meta, StoryObj } from '@storybook/react';
import { AutoFileDownload } from './AutoFileDownload';

// Sample base64 data for testing (a simple PDF-like string)
const sampleBase64Data = 'JVBERi0xLjQKJcOkw7zDtsO8DQoxIDAgb2JqDQo8PA0KL1R5cGUgL0NhdGFsb2cNCi9QYWdlcyAyIDAgUg0KPj4NCmVuZG9iag0KMiAwIG9iag0KPDwNCi9UeXBlIC9QYWdlcw0KL0tpZHMgWzMgMCBSXQ0KL0NvdW50IDENCi9NZWRpYUJveCBbMCAwIDYxMiA3OTJdDQo+Pg0KZW5kb2JqDQozIDAgb2JqDQo8PA0KL1R5cGUgL1BhZ2UNCj4+DQplbmRvYmoNCnhyZWYNCjAgNA0KMDAwMDAwMDAwMCA2NTUzNSBmDQowMDAwMDAwMDEwIDAwMDAwIG4NCjAwMDAwMDAwNzkgMDAwMDAgbg0KMDAwMDAwMDE3MyAwMDAwMCBuDQp0cmFpbGVyDQo8PA0KL1NpemUgNA0KL1Jvb3QgMSAwIFINCj4+DQpzdGFydHhyZWYNCjIzNA0KJSVFT0Y=';

const meta: Meta<typeof AutoFileDownload> = {
  title: 'Fragments/AutoFileDownload',
  component: AutoFileDownload,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
AutoFileDownload Fragment - A fragment that provides automatic file download functionality.

## Features
- Automatic file download on fragment mount
- Base64 to blob conversion
- Configurable content types
- Error handling and logging
- Memory leak prevention
- State-based approach to prevent race conditions

## Usage
The fragment automatically triggers a download when mounted. It converts base64 data to a blob and creates a download link.

## Props
- \`invoiceData.data\`: Base64 encoded file data
- \`invoiceData.fileName\`: Name for the downloaded file
- \`invoiceData.contentType\`: MIME type of the file (optional, defaults to 'application/pdf')

## Fragment vs Component
This fragment wraps the AutoFileDownload component and can be extended with fragment-specific logic, 
such as additional validation, error handling, or integration with other fragments.
        `,
      },
    },
  },
  argTypes: {
    invoiceData: {
      description: 'Object containing the file data and metadata',
      control: { type: 'object' },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof AutoFileDownload>;

// Default story with PDF data
export const Default: Story = {
  args: {
    invoiceData: {
      data: sampleBase64Data,
      fileName: 'sample-invoice.pdf',
      contentType: 'application/pdf',
    },
  },
};

// Story with different file type
export const TextFile: Story = {
  args: {
    invoiceData: {
      data: btoa('This is a sample text file content for testing purposes.'),
      fileName: 'sample-document.txt',
      contentType: 'text/plain',
    },
  },
};

// Story with image data
export const ImageFile: Story = {
  args: {
    invoiceData: {
      data: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', // 1x1 transparent PNG
      fileName: 'sample-image.png',
      contentType: 'image/png',
    },
  },
};

// Story with Excel file
export const ExcelFile: Story = {
  args: {
    invoiceData: {
      data: sampleBase64Data, // Using same data for demo
      fileName: 'financial-report.xlsx',
      contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    },
  },
};

// Story with long filename
export const LongFileName: Story = {
  args: {
    invoiceData: {
      data: sampleBase64Data,
      fileName: 'very-long-filename-that-might-cause-issues-in-some-systems-and-needs-to-be-handled-properly.pdf',
      contentType: 'application/pdf',
    },
  },
};

// Story with special characters in filename
export const SpecialCharacters: Story = {
  args: {
    invoiceData: {
      data: sampleBase64Data,
      fileName: 'invoice-2024-01-15 (final) - copy.pdf',
      contentType: 'application/pdf',
    },
  },
};

// Story with empty data (error case)
export const EmptyData: Story = {
  args: {
    invoiceData: {
      data: '',
      fileName: 'empty-file.pdf',
      contentType: 'application/pdf',
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'This story demonstrates how the fragment handles empty data. Check the browser console for warning messages.',
      },
    },
  },
};

// Story with invalid base64 data
export const InvalidData: Story = {
  args: {
    invoiceData: {
      data: 'invalid-base64-data!@#$%',
      fileName: 'invalid-file.pdf',
      contentType: 'application/pdf',
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'This story demonstrates how the fragment handles invalid base64 data. Check the browser console for error messages.',
      },
    },
  },
};

// Interactive story with controls
export const Interactive: Story = {
  args: {
    invoiceData: {
      data: sampleBase64Data,
      fileName: 'interactive-test.pdf',
      contentType: 'application/pdf',
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'Use the controls below to modify the fragment props and see how it behaves. Try changing the fileName, contentType, or data.',
      },
    },
  },
};

// Story for different content types
export const ContentTypes: Story = {
  args: {
    invoiceData: {
      data: sampleBase64Data,
      fileName: 'document.pdf',
      contentType: 'application/pdf',
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'This story demonstrates different content types. Try changing the contentType in the controls to see how different file types are handled.',
      },
    },
  },
};

// Story for testing edge cases
export const EdgeCases: Story = {
  args: {
    invoiceData: {
      data: sampleBase64Data,
      fileName: 'test-file.pdf',
      contentType: 'application/pdf',
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'This story is for testing edge cases like long filenames, special characters, and various content types.',
      },
    },
  },
};

// Story demonstrating the working download functionality
export const WorkingDownload: Story = {
  args: {
    invoiceData: {
      data: sampleBase64Data,
      fileName: 'working-download-demo.pdf',
      contentType: 'application/pdf',
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'This story demonstrates the working download functionality with the state-based approach that fixes the race condition.',
      },
    },
  },
}; 
import type { Meta, StoryObj } from '@storybook/react';
import { ActionPanelRecentActivity } from './ActionPanelRecentActivity';

const meta: Meta<typeof ActionPanelRecentActivity> = {
  component: ActionPanelRecentActivity,
  title: 'Fragments/ActionPanelRecentActivity',
  parameters: {
    layout: 'padded',
  },
  args: {
    userID: 1,
    showClearButton: true,
  },
};

export default meta;
type Story = StoryObj<typeof ActionPanelRecentActivity>;

export const Default: Story = {
  args: {
    userID: 1,
    onItemClick: (claim_num: string, applicant: string) => {
      console.log('Recent activity item clicked:', { claim_num, applicant });
    },
  },
};

export const WithoutClearButton: Story = {
  args: {
    userID: 1,
    showClearButton: false,
  },
}; 
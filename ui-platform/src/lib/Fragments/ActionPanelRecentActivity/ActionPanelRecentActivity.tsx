import React from 'react';
import { RecentActivityList } from '../../Components/RecentActivity/RecentActivityList';
import { useRecentActivity } from '../../Hooks/useRecentActivity';
import { useSearchTrigger } from '../../Hooks/useSearchTrigger';

interface ActionPanelRecentActivityProps {
  userID: number;
  onItemClick?: (claim_num: string, applicant: string) => void;
  showClearButton?: boolean;
}

export function ActionPanelRecentActivity({ 
  userID, 
  onItemClick, 
  showClearButton = true 
}: ActionPanelRecentActivityProps) {
    const { recentActivities } = useRecentActivity();
    const { triggerSearch } = useSearchTrigger();
    
    // Debug logging
    console.log('ActionPanelRecentActivity render:', {
      userID,
      allActivities: recentActivities,
      userActivities: recentActivities.filter(item => item.userID === userID)
    });

    const handleItemClick = (claim_num: string, applicant: string) => {
      // Trigger search with the claim number
      triggerSearch(claim_num);
      
      // Call the original onItemClick if provided
      if (onItemClick) {
        onItemClick(claim_num, applicant);
      }
    };

    return (
        <div>
            <RecentActivityList
                userID={userID}
                onItemClick={handleItemClick}
                showClearButton={showClearButton}
            />
        </div>
    )
}
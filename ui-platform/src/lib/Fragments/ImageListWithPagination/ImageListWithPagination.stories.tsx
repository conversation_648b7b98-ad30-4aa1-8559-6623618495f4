import React from 'react';
import { Meta, StoryObj } from '@storybook/react';
import { ImageListWithPagination } from './ImageListWithPagination';

const meta: Meta<typeof ImageListWithPagination> = {
  title: 'Fragments/ImageListWithPagination',
  component: ImageListWithPagination,
};

export default meta;

type Story = StoryObj<typeof ImageListWithPagination>;

const sampleImages = [
  {
    id: 1,
    job: 101,
    claim: 201,
    token: 'sample-token-1',
    purpose: 'Image 1',
    created: '2025-03-18',
    thumbnail: 'https://plus.unsplash.com/premium_photo-1734543942868-2470c4cba7b5?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwyfHx8ZW58MHx8fHx8', // Provide a valid thumbnail string (base64/URL) here if needed.
    filename: 'image1.jpg',
  },
  {
    id: 2,
    job: 102,
    claim: 202,
    token: 'sample-token-2',
    purpose: 'Image 2',
    created: '2025-03-18',
    thumbnail: 'https://plus.unsplash.com/premium_photo-1734543942868-2470c4cba7b5?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwyfHx8ZW58MHx8fHx8',
    filename: 'image2.jpg',
  },
  {
    id: 3,
    job: 103,
    claim: 203,
    token: 'sample-token-3',
    purpose: 'Image 3',
    created: '2025-03-18',
    thumbnail: 'https://plus.unsplash.com/premium_photo-1734543942868-2470c4cba7b5?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwyfHx8ZW58MHx8fHx8',
    filename: 'image3.jpg',
  },
  {
    id: 4,
    job: 104,
    claim: 204,
    token: 'sample-token-4',
    purpose: 'Image 4',
    created: '2025-03-18',
    thumbnail: 'https://plus.unsplash.com/premium_photo-1734543942868-2470c4cba7b5?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwyfHx8ZW58MHx8fHx8',
    filename: 'image4.jpg',
  },
  {
    id: 5,
    job: 105,
    claim: 205,
    token: 'sample-token-5',
    purpose: 'Image 5',
    created: '2025-03-18',
    thumbnail: 'https://plus.unsplash.com/premium_photo-1734543942868-2470c4cba7b5?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwyfHx8ZW58MHx8fHx8',
    filename: 'image5.jpg',
  },
  {
    id: 6,
    job: 106,
    claim: 206,
    token: 'sample-token-6',
    purpose: 'Image 6',
    created: '2025-03-18',
    thumbnail: 'https://plus.unsplash.com/premium_photo-1734543942868-2470c4cba7b5?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwyfHx8ZW58MHx8fHx8',
    filename: 'image6.jpg',
  },  
  {
    id: 7,
    job: 106,
    claim: 206,
    token: 'sample-token-6',
    purpose: 'Image 6',
    created: '2025-03-18',
    thumbnail: 'https://plus.unsplash.com/premium_photo-1734543942868-2470c4cba7b5?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwyfHx8ZW58MHx8fHx8',
    filename: 'image6.jpg',
  },  
  {
    id: 8,
    job: 106,
    claim: 206,
    token: 'sample-token-6',
    purpose: 'Image 6',
    created: '2025-03-18',
    thumbnail: 'https://plus.unsplash.com/premium_photo-1734543942868-2470c4cba7b5?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwyfHx8ZW58MHx8fHx8',
    filename: 'image6.jpg',
  },  
  {
    id: 9,
    job: 106,
    claim: 206,
    token: 'sample-token-6',
    purpose: 'Image 6',
    created: '2025-03-18',
    thumbnail: 'https://plus.unsplash.com/premium_photo-1734543942868-2470c4cba7b5?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwyfHx8ZW58MHx8fHx8',
    filename: 'image6.jpg',
  },  
  {
    id: 10,
    job: 106,
    claim: 206,
    token: 'sample-token-6',
    purpose: 'Image 6',
    created: '2025-03-18',
    thumbnail: 'https://plus.unsplash.com/premium_photo-1734543942868-2470c4cba7b5?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwyfHx8ZW58MHx8fHx8',
    filename: 'image6.jpg',
  },  

];

export const Default: Story = {
  args: {
    heading: '',
    jobImageItems: sampleImages,
    usecase: 'DocumentView',
    mediaType: '',
  },
};

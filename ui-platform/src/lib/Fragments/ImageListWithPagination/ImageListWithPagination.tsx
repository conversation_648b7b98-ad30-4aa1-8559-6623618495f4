import React from 'react';
import { JobImageListItem } from '../../Components/JobImage/JobImageListItem/JobImageListItem';

interface ImageListWithPaginationProps {
  heading: string;
  jobImageItems: any[];
  usecase?: string;
  mediaType?: string;
}

export const ImageListWithPagination: React.FC<ImageListWithPaginationProps> = ({
  heading,
  jobImageItems,
  usecase = 'DocumentView',
  mediaType = '',
}) => {
  return (
    <>
      <h3>{heading}</h3>
      <JobImageListItem
        jobImageItems={jobImageItems}
        usecase={usecase}
        mediaType={mediaType}
      />
    </>
  );
};

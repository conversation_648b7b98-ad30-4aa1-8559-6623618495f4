import React from 'react';
import styled from 'styled-components';
import { TimeStampList } from '../../Components/Cards/TimeStampList/TimeStampList';

interface TimeStampEntry {
  dateTime: string;
  modifier: string;
  state: string;
  stateDescription: string;
  reason?: string;
}

interface JobTimeStamp {
  job_id: string;
  title: string;
  entries: TimeStampEntry[];
}

interface TimeStampData {
  jobs: JobTimeStamp[];
  claimStates: TimeStampEntry[];
}

interface ApiResponse {
  payload: {
    jobs: any[][];
    claim: any[];
  };
}

interface TimeStampsProps {
  timeStamps: ApiResponse;
  showClaimUpdates?: boolean;
  showJobUpdates?: boolean;
  title?: string;
}

const TimeStampsContainer = styled.div`
  width: 100%;
  max-width: 100%;
`;

const TimeStampsTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 600;
  color: ${(props) => props?.theme?.ColorsTypographyPrimary || '#333'};
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid ${(props) => props?.theme?.ColorsInputsInverse || '#e9ecef'};
`;

export function TimeStamps({ 
  timeStamps,
  showClaimUpdates = true, 
  showJobUpdates = true,
  title = 'Time Stamps'
}: TimeStampsProps) {
  // Transform API response according to the provided logic
  const jobs = timeStamps?.payload['jobs'];
  const claimStates = timeStamps?.payload['claim'];
  const temArr: JobTimeStamp[] = [];
  
  if (timeStamps && jobs) {
    jobs.forEach((element: any) => {
      element.forEach((inner: any) => {
        element.title = inner.job_skill;
        element.job_id = inner.job_id;
        inner.dateTime = inner['mytimestamp'];
      });
      temArr.push(element);
    });
  }
  
  const transformedData: TimeStampData = {
    jobs: temArr,
    claimStates: claimStates || [],
  };

  return (
    <TimeStampsContainer>
      <TimeStampsTitle>{title}</TimeStampsTitle>
      <TimeStampList
        timeStamps={transformedData}
        showClaimUpdates={showClaimUpdates}
        showJobUpdates={showJobUpdates}
      />
    </TimeStampsContainer>
  );
} 
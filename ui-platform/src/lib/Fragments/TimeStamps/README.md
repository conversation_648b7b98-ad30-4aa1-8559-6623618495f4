# TimeStamps Fragment

A React fragment component that provides a complete time stamps functionality for displaying state changes on claims and jobs. This component serves as the main entry point for the time stamps feature and can be easily mounted in any application that consumes this library.

## Features

- Complete time stamps display with title
- Configurable sections for claim and job updates
- Responsive design that adapts to container width
- Themed styling that matches the platform design system
- Easy integration into existing applications

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `timeStamps` | `TimeStampData` | Yes | - | The time stamp data object |
| `showClaimUpdates` | `boolean` | No | `true` | Whether to show claim updates section |
| `showJobUpdates` | `boolean` | No | `true` | Whether to show job updates section |
| `title` | `string` | No | `'Time Stamps'` | The title displayed at the top of the component |

## Usage

```tsx
import { TimeStamps } from '@your-org/ui-platform';

const timeStampData = {
  claimStates: [
    {
      dateTime: '2024-01-15T10:30:00Z',
      modifier: '<PERSON>',
      state: 'Status',
      stateDescription: 'Changed from Pending to Approved',
      reason: 'Claim approved after review',
    },
  ],
  jobs: [
    {
      job_id: 'JOB-001',
      title: 'Plumbing Repair',
      entries: [
        {
          dateTime: '2024-01-15T11:00:00Z',
          modifier: 'Mike Johnson',
          state: 'Status',
          stateDescription: 'Changed from Scheduled to In Progress',
          reason: 'Technician started work',
        },
      ],
    },
  ],
};

// Basic usage
<TimeStamps timeStamps={timeStampData} />

// Custom title
<TimeStamps 
  timeStamps={timeStampData} 
  title="Claim Activity Log" 
/>
```

## Integration

This fragment is designed to be easily integrated into existing applications and replicates the Angular component functionality. 
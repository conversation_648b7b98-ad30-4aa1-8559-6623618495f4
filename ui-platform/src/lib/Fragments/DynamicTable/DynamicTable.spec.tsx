import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { DynamicTable } from './DynamicTable';

describe('DynamicTable', () => {
  const testData = [
    { id: 1, name: 'Alice', email: '<EMAIL>', role: 'Manager' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'Developer' },
  ];

  const columnConfig = {
    id: { key: 'id', header: 'ID' },
    name: { key: 'name', header: 'Name' },
    email: { key: 'email', header: 'Email' },
    role: { key: 'role', header: 'Role' },
  };

  it('should render successfully with basic data', () => {
    render(<DynamicTable data={testData} columnConfig={columnConfig as any} />);

    // Check if table headers are rendered
    expect(screen.getByText('ID')).toBeInTheDocument();
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Role')).toBeInTheDocument();

    // Check if data is rendered
    expect(screen.getByText('Alice')).toBeInTheDocument();
    expect(screen.getByText('Bob')).toBeInTheDocument();
    expect(screen.getByText('Manager')).toBeInTheDocument();
    expect(screen.getByText('Developer')).toBeInTheDocument();
  });

  it('should handle empty data gracefully', () => {
    render(<DynamicTable data={[]} columnConfig={columnConfig as any} />);

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('should respect displayColumns prop and only show specified columns', () => {
    render(
      <DynamicTable
        data={testData}
        displayColumns={['id', 'name', 'role']}
        columnConfig={columnConfig as any}
      />
    );

    // Should show only the specified columns
    expect(screen.getByText('ID')).toBeInTheDocument();
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Role')).toBeInTheDocument();

    // Should not show hidden columns
    expect(screen.queryByText('Email')).not.toBeInTheDocument();

    // Should still show the data for visible columns
    expect(screen.getByText('Alice')).toBeInTheDocument();
    expect(screen.getByText('Bob')).toBeInTheDocument();
    expect(screen.getByText('Manager')).toBeInTheDocument();
    expect(screen.getByText('Developer')).toBeInTheDocument();
  });

  it('should show loading state when loading prop is true', () => {
    render(
      <DynamicTable
        data={testData}
        columnConfig={columnConfig as any}
        loading={true}
      />
    );

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should show error state when error prop is provided', () => {
    render(
      <DynamicTable
        data={testData}
        columnConfig={columnConfig as any}
        error="Something went wrong"
      />
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });

  it('should render table controls when filtering is enabled', () => {
    render(
      <DynamicTable
        data={testData}
        columnConfig={columnConfig as any}
        enableFiltering={true}
      />
    );

    // Should show global filter input
    expect(
      screen.getByPlaceholderText('Search all columns...')
    ).toBeInTheDocument();
  });

  it('should render pagination when data exceeds page size', () => {
    const largeData = Array.from({ length: 25 }, (_, i) => ({
      id: i + 1,
      name: `User ${i + 1}`,
      email: `user${i + 1}@test.com`,
      role: i % 2 === 0 ? 'Manager' : 'Developer',
    }));

    render(
      <DynamicTable
        data={largeData}
        columnConfig={columnConfig as any}
        pageSize={10}
      />
    );

    // Should show pagination info
    expect(
      screen.getByText(/Showing \d+ to \d+ of \d+ results/)
    ).toBeInTheDocument();
  });

  it('should render row selection checkboxes when enableRowSelection is true', () => {
    render(
      <DynamicTable
        data={testData}
        columnConfig={columnConfig as any}
        enableRowSelection={true}
      />
    );

    // Should show select all checkbox in header
    const selectAllCheckbox = screen.getByLabelText('Select all rows');
    expect(selectAllCheckbox).toBeInTheDocument();
    expect(selectAllCheckbox).toHaveAttribute('type', 'checkbox');

    // Should show individual row checkboxes
    const rowCheckboxes = screen.getAllByLabelText(/Select row \d+/);
    expect(rowCheckboxes).toHaveLength(testData.length);
  });

  it('should call onSelectionChange when row selection changes', () => {
    const onSelectionChange = vi.fn();

    render(
      <DynamicTable
        data={testData}
        columnConfig={columnConfig as any}
        enableRowSelection={true}
        onSelectionChange={onSelectionChange}
      />
    );

    // onSelectionChange should be called initially with empty selection
    expect(onSelectionChange).toHaveBeenCalledWith([], []);
  });
});

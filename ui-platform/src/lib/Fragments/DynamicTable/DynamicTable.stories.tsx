import React, { useState } from 'react';
import { ActionConfig, useClientActionAsync } from '../../Engine';
import { TextConfig } from '../../Engine/models/text.config';
import { DynamicTable } from './DynamicTable';
import { ColumnConfig } from './types';

type User = {
  id: number;
  name: string;
  email: string;
  isActive: boolean;
  createdAt: string;
  role: string;
  department: string;
  salary: number;
  avatar?: string;
};

const initialData: User[] = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    isActive: true,
    createdAt: '2024-06-01',
    role: 'Manager',
    department: 'Engineering',
    salary: 95000,
    avatar: 'https://i.pravatar.cc/40?img=1',
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    isActive: false,
    createdAt: '2024-05-15',
    role: 'Developer',
    department: 'Engineering',
    salary: 75000,
    avatar: 'https://i.pravatar.cc/40?img=2',
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
    isActive: true,
    createdAt: '2024-04-20',
    role: 'Designer',
    department: 'Design',
    salary: 70000,
    avatar: 'https://i.pravatar.cc/40?img=3',
  },
  {
    id: 4,
    name: 'David Brown',
    email: '<EMAIL>',
    isActive: true,
    createdAt: '2024-03-10',
    role: 'Product Manager',
    department: 'Product',
    salary: 90000,
    avatar: 'https://i.pravatar.cc/40?img=4',
  },
  {
    id: 5,
    name: 'Eva Davis',
    email: '<EMAIL>',
    isActive: false,
    createdAt: '2024-02-28',
    role: 'QA Engineer',
    department: 'Engineering',
    salary: 65000,
    avatar: 'https://i.pravatar.cc/40?img=5',
  },
];

const financeCosts = [
  {
    supplier: 'Builders warehouse',
    invoice: 30400.56,
    cost: 865,
    isAccepted: false,
  },
  {
    supplier: "Bob's Tiling Supplies",
    invoice: 10899.0,
    cost: 665,
    isAccepted: false,
  },
  {
    supplier: "Carol's Hardware",
    invoice: 18266.25,
    cost: 786,
    isAccepted: false,
  },
];

// Generate large dataset for performance testing
const generateLargeDataset = (size: number): User[] => {
  const roles = [
    'Manager',
    'Developer',
    'Designer',
    'Product Manager',
    'QA Engineer',
    'DevOps',
    'Analyst',
  ];
  const departments = [
    'Engineering',
    'Design',
    'Product',
    'Marketing',
    'Sales',
    'HR',
    'Finance',
  ];
  const names = [
    'Alice',
    'Bob',
    'Carol',
    'David',
    'Eva',
    'Frank',
    'Grace',
    'Henry',
    'Ivy',
    'Jack',
  ];
  const surnames = [
    'Smith',
    'Johnson',
    'Williams',
    'Brown',
    'Davis',
    'Miller',
    'Wilson',
    'Moore',
    'Taylor',
    'Anderson',
  ];

  return Array.from({ length: size }, (_, i) => ({
    id: i + 1,
    name: `${names[i % names.length]} ${surnames[i % surnames.length]}`,
    email: `user${i + 1}@example.com`,
    isActive: Math.random() > 0.3,
    createdAt: new Date(
      2024,
      Math.floor(Math.random() * 12),
      Math.floor(Math.random() * 28) + 1
    )
      .toISOString()
      .split('T')[0],
    role: roles[i % roles.length],
    department: departments[i % departments.length],
    salary: Math.floor(Math.random() * 50000) + 50000,
    avatar: `https://i.pravatar.cc/40?img=${(i % 70) + 1}`,
  }));
};

export default {
  title: 'Fragments/DynamicTable',
  component: DynamicTable,
};

export const WithCheckbox = () => {
  const [data, setData] = useState<User[]>(initialData);

  const handleCheckboxChange = (
    checked: boolean,
    row: User,
    rowIndex: number
  ) => {
    console.log('Checkbox changed:', { checked, row, rowIndex });
    setData((prev) =>
      prev.map((item, idx) =>
        idx === rowIndex ? { ...item, isActive: checked } : item
      )
    );
  };

  const columnConfig: Partial<Record<keyof User, ColumnConfig<User>>> = {
    id: {
      key: 'id',
      header: 'ID',
      width: '60',
      headerTextConfig: {
        text: 'User ID',
        options: { format: 'paragraph' },
      } as TextConfig,
      bodyTextConfig: (value: any) => ({
        text: String(value),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
    name: {
      key: 'name',
      header: 'Name',
      headerTextConfig: {
        text: 'Full Name',
        options: { format: 'paragraph' },
      } as TextConfig,
      // Example: render as input field
      controlType: 'plain-text',
      controlProps: { style: { width: 140 } },
      onControlChange: (val: string, row: User, rowIndex: number) => {
        console.log('Name changed:', { val, row, rowIndex });
        setData((prev) =>
          prev.map((item, idx) =>
            idx === rowIndex ? { ...item, name: val } : item
          )
        );
      },
    },
    email: {
      key: 'email',
      header: 'Email',
      headerTextConfig: {
        text: 'Email Address',
        options: { format: 'paragraph' },
      } as TextConfig,
    },
    isActive: {
      key: 'isActive',
      header: 'Active',
      headerTextConfig: {
        text: 'Active?',
        options: { format: 'paragraph' },
      } as TextConfig,
      controlType: 'checkbox',
      onControlChange: (val: boolean, row: User, rowIndex: number) =>
        handleCheckboxChange(val, row, rowIndex),
      controlProps: { 'aria-label': 'Active Checkbox' },
    },
    createdAt: {
      key: 'createdAt',
      header: 'Created',
      headerTextConfig: {
        text: 'Created At',
        options: { format: 'paragraph' },
      } as TextConfig,
      bodyTextConfig: (value: string) => ({
        text: new Date(value).toLocaleDateString(),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
  };

  return (
    <div style={{ padding: '20px' }}>
      <h3>Row-Specific Form Controls Demo</h3>
      <p>
        Each control shows and updates the actual row data. Check console for
        change events. The checkbox and text input are bound to specific row
        values.
      </p>
      <DynamicTable
        data={data}
        columnConfig={columnConfig as any}
        pageSize={5}
        // enableFiltering={false}
        // enableSorting={false}
        // enablePagination=
        // enableInfoText
        // tableBorder="1px solid #e5e7eb"
      />
      <div
        style={{
          marginTop: '20px',
          padding: '10px',
          borderRadius: '4px',
          maxHeight: '300px',
          overflow: 'auto',
        }}
      >
        <h4>Current Data State:</h4>
        <pre style={{ fontSize: '12px' }}>{JSON.stringify(data, null, 2)}</pre>
      </div>
    </div>
  );
};

// New story: WithInput
export const WithInput = () => {
  const [data, setData] = useState<User[]>(initialData);

  const handleNameChange = (val: string, row: User) => {
    setData((prev) =>
      prev.map((item) => (item.id === row.id ? { ...item, name: val } : item))
    );
  };

  const columnConfig: Partial<Record<keyof User, ColumnConfig<User>>> = {
    id: {
      key: 'id',
      header: 'ID',
      width: '60',
      headerTextConfig: {
        text: 'User ID',
        options: { format: 'heading' },
      } as TextConfig,
      bodyTextConfig: (value: any) => ({
        text: String(value),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
    name: {
      key: 'name',
      header: 'Name (Editable)',
      headerTextConfig: {
        text: 'Full Name (Edit)',
        options: { format: 'heading' },
      } as TextConfig,
      controlType: 'plain-text',
      controlProps: { style: { width: 180 } },
      customControlName: 'full_name',
      onControlChange: handleNameChange,
    },
    email: {
      key: 'email',
      header: 'Email',
      headerTextConfig: {
        text: 'Email Address',
        options: { format: 'heading' },
      } as TextConfig,
    },
    isActive: {
      key: 'isActive',
      header: 'Active',
      headerTextConfig: {
        text: 'Active?',
        options: { format: 'heading' },
      } as TextConfig,
      // Just display as text in this story
      bodyTextConfig: (value: boolean) => ({
        text: value ? 'Yes' : 'No',
        options: {
          format: 'paragraph',
          style: {
            color: value ? '#166534' : '#991b1b',
            background: value ? '#dcfce7' : '#fee2e2',
            borderRadius: '4px',
            padding: '2px 8px',
            fontWeight: 500,
          },
        },
      }),
    },
    createdAt: {
      key: 'createdAt',
      header: 'Created',
      headerTextConfig: {
        text: 'Created At',
        options: { format: 'heading' },
      } as TextConfig,
      bodyTextConfig: (value: string) => ({
        text: new Date(value).toLocaleDateString(),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
  };

  return (
    <DynamicTable
      data={data}
      columnConfig={columnConfig as any}
      pageSize={5}
      enableFiltering
      enableSorting
      enablePagination
      enableInfoText
      tableBorder="1px solid #e5e7eb"
    />
  );
};

// New story: WithInputArray
export const WithInputArray = () => {
  // Store name as an array of values
  const [nameValues, setNameValues] = useState(initialData.map((u) => u.name));
  const [data, setData] = useState<User[]>(initialData);

  // Update the array at the correct index
  const handleNameChange = (val: string, row: User, rowIndex: number) => {
    setNameValues((prev) => {
      const updated = [...prev];
      updated[rowIndex] = val;
      return updated;
    });
    setData((prev) =>
      prev.map((item, idx) =>
        idx === rowIndex ? { ...item, name: val } : item
      )
    );
  };

  const columnConfig: Partial<Record<keyof User, ColumnConfig<User>>> = {
    id: {
      key: 'id',
      header: 'ID',
      width: '60',
      headerTextConfig: {
        text: 'User ID',
        options: { format: 'heading' },
      } as TextConfig,
      bodyTextConfig: (value: any) => ({
        text: String(value),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
    name: {
      key: 'name',
      header: 'Name (Editable)',
      headerTextConfig: {
        text: 'Full Name (Edit)',
        options: { format: 'heading' },
      } as TextConfig,
      controlType: 'plain-text',
      controlProps: { style: { width: 180 } },
      customControlName: 'full_name',
      // Pass rowIndex to onControlChange
      onControlChange: (val: string, row: User) => {
        const rowIndex = data.findIndex((item) => item.id === row.id);
        handleNameChange(val, row, rowIndex);
      },
      // Use value from the array for each row
      cell: (_value, row) => {
        const rowIndex = data.findIndex((item) => item.id === row.id);
        return nameValues[rowIndex];
      },
    },
    email: {
      key: 'email',
      header: 'Email',
      headerTextConfig: {
        text: 'Email Address',
        options: { format: 'heading' },
      } as TextConfig,
    },
    isActive: {
      key: 'isActive',
      header: 'Active',
      headerTextConfig: {
        text: 'Active?',
        options: { format: 'heading' },
      } as TextConfig,
      bodyTextConfig: (value: boolean) => ({
        text: value ? 'Yes' : 'No',
        options: {
          format: 'paragraph',
          style: {
            color: value ? '#166534' : '#991b1b',
            background: value ? '#dcfce7' : '#fee2e2',
            borderRadius: '4px',
            padding: '2px 8px',
            fontWeight: 500,
          },
        },
      }),
    },
    createdAt: {
      key: 'createdAt',
      header: 'Created',
      headerTextConfig: {
        text: 'Created At',
        options: { format: 'heading' },
      } as TextConfig,
      bodyTextConfig: (value: string) => ({
        text: new Date(value).toLocaleDateString(),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
  };

  // Provide the value for each row from the array
  const tableData = data.map((row, idx) => ({
    ...row,
    name: nameValues[idx],
  }));

  return (
    <DynamicTable
      data={tableData}
      columnConfig={columnConfig as any}
      pageSize={5}
      enableFiltering
      enableSorting
      enablePagination
      enableInfoText
      tableBorder="1px solid #e5e7eb"
    />
  );
};

// Enhanced Features Stories
export const WithRowSelection = () => {
  const [data] = useState<User[]>(initialData);
  const [selectedRows, setSelectedRows] = useState<User[]>([]);

  const columnConfig: Partial<Record<keyof User, ColumnConfig<User>>> = {
    // avatar: {
    //   key: 'avatar',
    //   header: 'Avatar',
    //   width: '60px',
    //   cell: (value) => (
    //     <img
    //       src={value}
    //       alt="Avatar"
    //       style={{ width: '32px', height: '32px', borderRadius: '50%' }}
    //     />
    //   ),
    // },
    name: {
      key: 'name',
      header: 'Name',
      // sortable: true,
    },
    email: {
      key: 'email',
      header: 'Email',
      // filterable: true,
    },
    // role: {
    //   key: 'role',
    //   header: 'Role',
    //   controlType: 'single-select',
    //   controlProps: {
    //     options: {
    //       source: 'literal',
    //       data: [
    //         { label: 'Manager', value: 'Manager' },
    //         { label: 'Developer', value: 'Developer' },
    //         { label: 'Designer', value: 'Designer' },
    //         { label: 'Product Manager', value: 'Product Manager' },
    //         { label: 'QA Engineer', value: 'QA Engineer' },
    //       ],
    //     },
    //   },
    //   onControlChange: (val, row, rowIndex) => {
    //     console.log('Role changed:', { val, row, rowIndex });
    //     setSelectedRows((prev) =>
    //       prev.map((item, idx) =>
    //         idx === rowIndex ? { ...item, role: val } : item
    //       )
    //     );
    //   },
    // },
    department: {
      key: 'department',
      header: 'Department',
      // filterable: true,
    },
    salary: {
      key: 'salary',
      header: 'Salary',
      formatter: (value) => `$${value.toLocaleString()}`,
      // sortable: true,
    },
    isActive: {
      key: 'isActive',
      header: 'Active',
      controlType: 'checkbox',
      onControlChange: (val: boolean, row: User, rowIndex: number) => {
        console.log('Active status changed:', { val, row, rowIndex });
        setSelectedRows((prev) =>
          prev.map((item, idx) =>
            idx === rowIndex ? { ...item, isActive: val } : item
          )
        );
      },
    },
  };

  const rowActions = [
    {
      id: 'edit',
      label: 'Edit',
      onClick: (row: any, rowIndex: number) =>
        alert(`Edit ${row.name} at index ${rowIndex}`),
      variant: 'primary' as const,
    },
    {
      id: 'delete',
      label: 'Delete',
      onClick: (row: any, rowIndex: number) =>
        alert(`Delete ${row.name} at index ${rowIndex}`),
      variant: 'danger' as const,
      disabled: (row: any) => row.role === 'Manager',
    },
  ];

  const bulkActions = [
    {
      id: 'activate',
      label: 'Activate Selected',
      onClick: (rows: any[], indices: number[]) =>
        alert(`Activate ${rows.length} users at indices ${indices.join(', ')}`),
      variant: 'success' as const,
    },
    {
      id: 'deactivate',
      label: 'Deactivate Selected',
      onClick: (rows: any[], indices: number[]) =>
        alert(
          `Deactivate ${rows.length} users at indices ${indices.join(', ')}`
        ),
      variant: 'secondary' as const,
    },
    {
      id: 'delete-bulk',
      label: 'Delete Selected',
      onClick: (rows: any[], indices: number[]) =>
        alert(`Delete ${rows.length} users at indices ${indices.join(', ')}`),
      variant: 'danger' as const,
    },
  ];

  return (
    <div style={{ padding: '20px' }}>
      <h3>Enhanced Table with Row Selection & Actions</h3>
      <p>Selected: {selectedRows.length} users</p>
      <DynamicTable
        data={data}
        columnConfig={columnConfig as any}
        enableRowSelection
        enableFiltering
        enableSorting
        enablePagination
        enableColumnResizing
        pageSize={10}
        rowActions={rowActions}
        bulkActions={bulkActions}
        onSelectionChange={(rows, indices) => {
          console.log('Selection changed:', { rows, indices });
          setSelectedRows(rows as User[]);
        }}
        onRowClick={(row) => console.log('Row clicked:', row)}
        exportConfig={{
          enabled: true,
          formats: ['csv', 'excel', 'pdf'],
          filename: 'users-export',
        }}
        ariaLabel="Users table with selection and actions"
      />
    </div>
  );
};

export const WithVirtualScrolling = () => {
  const [data] = useState<User[]>(generateLargeDataset(1000));

  const columnConfig: Partial<Record<keyof User, ColumnConfig<User>>> = {
    id: {
      key: 'id',
      header: 'ID',
      width: 80,
    },
    name: {
      key: 'name',
      header: 'Name',
      width: 150,
    },
    email: {
      key: 'email',
      header: 'Email',
      width: 200,
    },
    role: {
      key: 'role',
      header: 'Role',
      width: 120,
    },
    department: {
      key: 'department',
      header: 'Department',
      width: 120,
    },
    salary: {
      key: 'salary',
      header: 'Salary',
      width: 100,
      formatter: (value) => `$${value.toLocaleString()}`,
    },
    isActive: {
      key: 'isActive',
      header: 'Status',
      width: 80,
      cell: (value) => (
        <span
          style={{
            padding: '2px 8px',
            borderRadius: '12px',
            fontSize: '12px',
            fontWeight: 500,
            background: value ? '#dcfce7' : '#fee2e2',
            color: value ? '#166534' : '#991b1b',
          }}
        >
          {value ? 'Active' : 'Inactive'}
        </span>
      ),
    },
  };

  return (
    <div style={{ padding: '20px' }}>
      <h3>Virtual Scrolling Performance Demo</h3>
      <p>1000 rows with virtual scrolling enabled</p>
      <DynamicTable
        data={data}
        columnConfig={columnConfig as any}
        enableVirtualScrolling={true}
        enableFiltering={true}
        enableSorting={true}
        enableColumnResizing={true}
        height="500px"
        maxHeight="500px"
        virtualScrollConfig={{
          enabled: true,
          itemHeight: 48,
          threshold: 50,
          overscan: 10,
        }}
        loadingConfig={{
          enabled: false,
          skeleton: true,
          rows: 10,
        }}
        ariaLabel="Large dataset with virtual scrolling"
      />
    </div>
  );
};

// Simple test story to demonstrate row-specific binding
export const RowSpecificBindingTest = () => {
  const [data, setData] = useState<User[]>([
    {
      id: 1,
      name: 'Alice Smith',
      email: '<EMAIL>',
      isActive: true,
      createdAt: '2024-06-01',
      role: 'Manager',
      department: 'Engineering',
      salary: 95000,
    },
    {
      id: 2,
      name: 'Bob Johnson',
      email: '<EMAIL>',
      isActive: false,
      createdAt: '2024-05-15',
      role: 'Developer',
      department: 'Engineering',
      salary: 75000,
    },
  ]);

  const columnConfig: Partial<Record<keyof User, ColumnConfig<User>>> = {
    id: {
      key: 'id',
      header: 'ID',
      width: '80px',
    },
    name: {
      key: 'name',
      header: 'Name',
      controlType: 'plain-text',
      onControlChange: (val: string, row: User, rowIndex: number) => {
        console.log('Name changed:', { val, row, rowIndex });
        setData((prev) =>
          prev.map((item, idx) =>
            idx === rowIndex ? { ...item, name: val } : item
          )
        );
      },
    },
    role: {
      key: 'role',
      header: 'Role',
      controlType: 'single-select',
      controlProps: {
        options: {
          source: 'literal',
          data: [
            { label: 'Manager', value: 'Manager' },
            { label: 'Developer', value: 'Developer' },
            { label: 'Designer', value: 'Designer' },
          ],
        },
      },
      onControlChange: (val: string, row: User, rowIndex: number) => {
        console.log('Role changed:', { val, row, rowIndex });
        setData((prev) =>
          prev.map((item, idx) =>
            idx === rowIndex ? { ...item, role: val } : item
          )
        );
      },
    },
    isActive: {
      key: 'isActive',
      header: 'Active',
      controlType: 'checkbox',
      onControlChange: (val: boolean, row: User, rowIndex: number) => {
        console.log('Active changed:', { val, row, rowIndex });
        setData((prev) =>
          prev.map((item, idx) =>
            idx === rowIndex ? { ...item, isActive: val } : item
          )
        );
      },
    },
  };

  return (
    <div style={{ padding: '20px' }}>
      <h3>Row-Specific Binding Test</h3>
      <p>
        Test that each control is bound to its specific row. Change values and
        see that only the intended row is updated. Check console for detailed
        change events.
      </p>
      <DynamicTable
        data={data}
        columnConfig={columnConfig as any}
        enableFiltering={false}
        enableSorting={false}
        enablePagination={false}
      />
      <div
        style={{
          marginTop: '20px',
          padding: '10px',
          background: '#f0f0f0',
          borderRadius: '4px',
        }}
      >
        <h4>Current Data:</h4>
        <pre style={{ fontSize: '12px' }}>{JSON.stringify(data, null, 2)}</pre>
      </div>
    </div>
  );
};

export const LimitedColumns = () => {
  const [data, setData] = useState<User[]>([
    {
      id: 1,
      name: 'Alice Smith',
      email: '<EMAIL>',
      isActive: true,
      createdAt: '2024-06-01',
      role: 'Manager',
      department: 'Engineering',
      salary: 95000,
    },
    {
      id: 2,
      name: 'Bob Johnson',
      email: '<EMAIL>',
      isActive: false,
      createdAt: '2024-05-15',
      role: 'Developer',
      department: 'Engineering',
      salary: 75000,
    },
  ]);

  const columnConfig: Partial<Record<keyof User, ColumnConfig<User>>> = {
    id: {
      key: 'id',
      header: 'ID',
      width: '80px',
    },
    name: {
      key: 'name',
      header: 'Name',
      controlType: 'plain-text',
      onControlChange: (val: string, row: User, rowIndex: number) => {
        console.log('Name changed:', { val, row, rowIndex });
        setData((prev) =>
          prev.map((item, idx) =>
            idx === rowIndex ? { ...item, name: val } : item
          )
        );
      },
    },
    role: {
      key: 'role',
      header: 'Role',
      controlType: 'single-select',
      controlProps: {
        options: {
          source: 'literal',
          data: [
            { label: 'Manager', value: 'Manager' },
            { label: 'Developer', value: 'Developer' },
            { label: 'Designer', value: 'Designer' },
          ],
        },
      },
      onControlChange: (val: string, row: User, rowIndex: number) => {
        console.log('Role changed:', { val, row, rowIndex });
        setData((prev) =>
          prev.map((item, idx) =>
            idx === rowIndex ? { ...item, role: val } : item
          )
        );
      },
    },
    isActive: {
      key: 'isActive',
      header: 'Active',
      controlType: 'checkbox',
      onControlChange: (val: boolean, row: User, rowIndex: number) => {
        console.log('Active changed:', { val, row, rowIndex });
        setData((prev) =>
          prev.map((item, idx) =>
            idx === rowIndex ? { ...item, isActive: val } : item
          )
        );
      },
    },
  };

  return (
    <div style={{ padding: '20px' }}>
      <h3>Row-Specific Binding Test</h3>
      <p>
        Test that each control is bound to its specific row. Change values and
        see that only the intended row is updated. Check console for detailed
        change events.
      </p>
      <DynamicTable
        data={data}
        displayColumns={['id', 'name', 'role']}
        columnConfig={columnConfig as any}
        enableFiltering={false}
        enableSorting={false}
        enablePagination={false}
      />
      <div
        style={{
          marginTop: '20px',
          padding: '10px',
          background: '#f0f0f0',
          borderRadius: '4px',
        }}
      >
        <h4>Current Data:</h4>
        <pre style={{ fontSize: '12px' }}>{JSON.stringify(data, null, 2)}</pre>
      </div>
    </div>
  );
};

export const JobFinanceCosts = () => {
  const [data, setData] = useState(financeCosts);
  const { callClientAction } = useClientActionAsync({
    navigate: () => {
      console.log`navigating...`;
    },
    location: { search: '' } as any,
  });

  const handleCheckboxChange = (
    checked: boolean,
    row: Record<string, any>,
    rowIndex: number
  ) => {
    // console.log('Checkbox changed:', { checked, row, rowIndex });
    // setData((prev) =>
    //   prev.map((item, idx) =>
    //     idx === rowIndex ? { ...item, isAccepted: checked } : item
    //   )
    // );
    // const paramData = { checked, row, rowIndex };
    // callClientAction([
    //   {
    //     type: 'clientAction',
    //     action: 'log',
    //     payload: ['Acceptance changed', '@param'],
    //     param: paramData,
    //   },
    // ]);
  };

  const columnConfig: Partial<
    Record<keyof Record<string, any>, ColumnConfig<Record<string, any>>>
  > = {
    supplier: {
      key: 'supplier',
      headerTextConfig: {
        text: 'Supplier',
        options: { format: 'paragraph' },
      } as TextConfig,
      bodyTextConfig: (value: any) => ({
        text: String(value),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
    invoice: {
      key: 'invoice',
      headerTextConfig: {
        text: 'Invoice Amount',
        options: { format: 'paragraph' },
      } as TextConfig,
      bodyTextConfig: (value: any) => ({
        text: String(value),
        options: { format: 'paragraph', style: { fontFamily: 'monospace' } },
      }),
    },
    cost: {
      key: 'cost',
      headerTextConfig: {
        text: 'Finance Cost',
        options: { format: 'paragraph' },
      } as TextConfig,
    },
    isAccepted: {
      key: 'accept',
      header: 'Accept',
      headerTextConfig: {
        text: 'Accept',
        options: { format: 'paragraph' },
      } as TextConfig,
      controlType: 'checkbox',
      // onControlChange: (val, row, rowIndex) => {
      //   handleCheckboxChange(val, row, rowIndex);
      // },
      onControlChange: [
        {
          type: 'clientAction',
          action: 'log',
          payload: ['Acceptance changed', '@param'],
        },
      ] as ActionConfig[],
      controlProps: { 'aria-label': 'Active Checkbox', hideLabel: true },
    },
  };

  return (
    <div style={{ padding: '20px' }}>
      <h3>Accept job finance costs</h3>
      <p>Tick an item to accept the financing consts</p>
      <DynamicTable
        data={data}
        columnConfig={columnConfig}
        pageSize={5}
        _callClientAction={callClientAction}
        // enableFiltering={false}
        // enableSorting={false}
        // enablePagination=
        // enableInfoText
        // tableBorder="1px solid #e5e7eb"
      />
      <div
        style={{
          marginTop: '20px',
          padding: '10px',
          borderRadius: '4px',
          maxHeight: '300px',
          overflow: 'auto',
        }}
      >
        <h4>Current Data State:</h4>
        <pre style={{ fontSize: '12px' }}>{JSON.stringify(data, null, 2)}</pre>
      </div>
    </div>
  );
};

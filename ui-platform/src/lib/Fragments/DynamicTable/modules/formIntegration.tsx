import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

/**
 * Interface for table form context value
 */
export interface TableFormContextValue {
  formData: Record<string, any>;
  updateFormData: (key: string, value: any) => void;
  batchUpdateFormData: (updates: Record<string, any>) => void;
  getFormValue: (key: string) => any;
  validateField: (key: string, value: any) => string | null;
  isFieldDirty: (key: string) => boolean;
  resetField: (key: string) => void;
  getFieldError: (key: string) => string | null;
}

/**
 * Context for table form state management
 */
export const TableFormContext = createContext<TableFormContextValue | null>(null);

/**
 * Hook to use table form context with performance tracking
 */
export const useTableForm = () => {
  const context = useContext(TableFormContext);
  if (!context) {
    throw new Error('useTableForm must be used within a TableFormProvider');
  }
  return context;
};

/**
 * Props for TableFormProvider
 */
export interface TableFormProviderProps {
  children: React.ReactNode;
  initialData: Record<string, any>;
  onDataChange?: (data: Record<string, any>) => void;
  validators?: Record<string, (value: any) => string | null>;
}

/**
 * Optimized form provider with batched updates and dirty tracking
 */
export const TableFormProvider: React.FC<TableFormProviderProps> = ({
  children,
  initialData,
  onDataChange,
  validators = {},
}) => {
  const [formData, setFormData] = useState(initialData);
  const [dirtyFields, setDirtyFields] = useState<Set<string>>(new Set());
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const updateTimeoutRef = useRef<NodeJS.Timeout>();

  // Batched update function to prevent excessive re-renders
  const batchUpdateFormData = useCallback(
    (updates: Record<string, any>) => {
      setFormData((prev) => {
        const newData = { ...prev, ...updates };

        // Clear timeout if pending
        if (updateTimeoutRef.current) {
          clearTimeout(updateTimeoutRef.current);
        }

        // Batch the onChange callback
        updateTimeoutRef.current = setTimeout(() => {
          onDataChange?.(newData);
        }, 16); // Next frame

        return newData;
      });

      // Mark fields as dirty
      setDirtyFields((prev) => {
        const newDirty = new Set(prev);
        Object.keys(updates).forEach((key) => newDirty.add(key));
        return newDirty;
      });
    },
    [onDataChange]
  );

  const updateFormData = useCallback(
    (key: string, value: any) => {
      batchUpdateFormData({ [key]: value });
    },
    [batchUpdateFormData]
  );

  const getFormValue = useCallback(
    (key: string) => {
      return formData[key];
    },
    [formData]
  );

  const validateField = useCallback(
    (key: string, value: any) => {
      const validator = validators[key];
      const error = validator ? validator(value) : null;

      setFieldErrors((prev) => {
        if (error) {
          return { ...prev, [key]: error };
        } else {
          const { [key]: removed, ...rest } = prev;
          return rest;
        }
      });

      return error;
    },
    [validators]
  );

  const isFieldDirty = useCallback(
    (key: string) => {
      return dirtyFields.has(key);
    },
    [dirtyFields]
  );

  const resetField = useCallback(
    (key: string) => {
      setFormData((prev) => ({ ...prev, [key]: initialData[key] }));
      setDirtyFields((prev) => {
        const newDirty = new Set(prev);
        newDirty.delete(key);
        return newDirty;
      });
      setFieldErrors((prev) => {
        const { [key]: removed, ...rest } = prev;
        return rest;
      });
    },
    [initialData]
  );

  const getFieldError = useCallback(
    (key: string) => {
      return fieldErrors[key] || null;
    },
    [fieldErrors]
  );

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  const contextValue = useMemo(
    () => ({
      formData,
      updateFormData,
      batchUpdateFormData,
      getFormValue,
      validateField,
      isFieldDirty,
      resetField,
      getFieldError,
    }),
    [
      formData,
      updateFormData,
      batchUpdateFormData,
      getFormValue,
      validateField,
      isFieldDirty,
      resetField,
      getFieldError,
    ]
  );

  return (
    <TableFormContext.Provider value={contextValue}>
      {children}
    </TableFormContext.Provider>
  );
};

/**
 * Configuration interface for form integration
 */
export interface FormIntegrationConfig {
  enabled: boolean;
  sharedFormState?: boolean;
  onFormSubmit?: (data: Record<string, any>) => void;
  onFormValidation?: (errors: Record<string, string>) => void;
}

/**
 * Hook to manage form integration with table
 */
export const useFormIntegration = (
  data: any[],
  formIntegration: FormIntegrationConfig,
  onDataChange?: (data: any[]) => void
) => {
  const initialFormData = useMemo(() => {
    if (!formIntegration.enabled || !data.length) return {};
    
    // Create initial form data from table data
    const formData: Record<string, any> = {};
    data.forEach((row, index) => {
      Object.keys(row).forEach((key) => {
        formData[`${index}_${key}`] = row[key];
      });
    });
    
    return formData;
  }, [data, formIntegration.enabled]);

  const handleFormDataChange = useCallback(
    (formData: Record<string, any>) => {
      if (!onDataChange || !formIntegration.enabled) return;

      // Convert form data back to table data structure
      const updatedData = [...data];
      Object.entries(formData).forEach(([key, value]) => {
        const [indexStr, fieldName] = key.split('_');
        const index = parseInt(indexStr);
        if (!isNaN(index) && updatedData[index]) {
          updatedData[index] = { ...updatedData[index], [fieldName]: value };
        }
      });

      onDataChange(updatedData);
    },
    [data, onDataChange, formIntegration.enabled]
  );

  return {
    initialFormData,
    handleFormDataChange,
    shouldRenderFormProvider: formIntegration.enabled,
  };
};

import { Table } from '@tanstack/react-table';
import { useCallback } from 'react';
import { TextConfig } from '../../../Engine';
import { ColumnConfig, ExportConfig, ExportData } from '../types';

/**
 * Helper function to convert table data to export format
 */
export const prepareExportData = <T extends Record<string, any>>(
  table: Table<T>,
  columnConfig: Partial<Record<keyof T, ColumnConfig<T>>>,
  exportConfig: ExportConfig
): ExportData => {
  const visibleColumns = table.getVisibleLeafColumns();
  const headers = visibleColumns.map((column) => {
    const config = columnConfig[column.id as keyof T];
    return config?.header || config?.headerTextConfig?.text || column.id;
  });

  const rows = table.getRowModel().rows.map((row) => {
    return visibleColumns.map((column) => {
      const value = row.getValue(column.id);

      // Handle different data types for export
      if (value === null || value === undefined) return null;
      if (typeof value === 'boolean') return value;
      if (typeof value === 'number') return value;
      if (typeof value === 'object') {
        // For complex objects, try to extract a meaningful string representation
        if (value.toString && typeof value.toString === 'function') {
          return value.toString();
        }
        return JSON.stringify(value);
      }

      return String(value);
    });
  });

  const filename =
    exportConfig.filename ||
    `table-export-${new Date().toISOString().split('T')[0]}`;

  return { headers, rows, filename };
};

/**
 * Function to export data as CSV
 */
export const exportToCSV = (exportData: ExportData): void => {
  const { headers, rows, filename } = exportData;

  // Create CSV content
  const csvContent = [
    headers.join(','),
    ...rows.map((row) =>
      row
        .map((cell) => {
          // Escape cells that contain commas, quotes, or newlines
          if (cell === null || cell === undefined) return '';
          const cellStr = String(cell);
          if (
            cellStr.includes(',') ||
            cellStr.includes('"') ||
            cellStr.includes('\n')
          ) {
            return `"${cellStr.replace(/"/g, '""')}"`;
          }
          return cellStr;
        })
        .join(',')
    ),
  ].join('\n');

  // Create and trigger download
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);

  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
};

/**
 * Function to export data as Excel (basic implementation)
 */
export const exportToExcel = (exportData: ExportData): void => {
  const { headers, rows, filename } = exportData;

  // Create a simple HTML table that Excel can interpret
  const htmlContent = `
    <table>
      <thead>
        <tr>
          ${headers.map((header) => `<th>${header}</th>`).join('')}
        </tr>
      </thead>
      <tbody>
        ${rows
          .map(
            (row) =>
              `<tr>
            ${row.map((cell) => `<td>${cell || ''}</td>`).join('')}
          </tr>`
          )
          .join('')}
      </tbody>
    </table>
  `;

  const blob = new Blob([htmlContent], {
    type: 'application/vnd.ms-excel;charset=utf-8;',
  });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);

  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.xls`);
  link.style.visibility = 'hidden';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
};

/**
 * Function to export data as PDF (basic implementation)
 */
export const exportToPDF = (exportData: ExportData): void => {
  const { headers, rows, filename } = exportData;

  // Create a printable HTML version
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>${filename}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        tr:nth-child(even) { background-color: #f9f9f9; }
      </style>
    </head>
    <body>
      <h1>${filename}</h1>
      <table>
        <thead>
          <tr>
            ${headers.map((header) => `<th>${header}</th>`).join('')}
          </tr>
        </thead>
        <tbody>
          ${rows
            .map(
              (row) =>
                `<tr>
              ${row.map((cell) => `<td>${cell || ''}</td>`).join('')}
            </tr>`
            )
            .join('')}
        </tbody>
      </table>
    </body>
    </html>
  `;

  // Open in new window for printing
  const printWindow = window.open('', '_blank');
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();

    // Trigger print dialog
    setTimeout(() => {
      printWindow.print();
    }, 250);
  }
};

/**
 * Hook to handle export functionality
 */
export const useExportFunctionality = <T extends Record<string, any>>(
  table: Table<T>,
  columnConfig: Partial<Record<keyof T, ColumnConfig<T>>>,
  exportConfig: ExportConfig
) => {
  const handleExport = useCallback(
    (format: 'csv' | 'excel' | 'pdf') => {
      if (!exportConfig.enabled) return;

      const exportData = prepareExportData(table, columnConfig, exportConfig);

      switch (format) {
        case 'csv':
          exportToCSV(exportData);
          break;
        case 'excel':
          exportToExcel(exportData);
          break;
        case 'pdf':
          exportToPDF(exportData);
          break;
        default:
          console.warn(`Unsupported export format: ${format}`);
      }

      // Call custom export handler if provided
      if (exportConfig.onExport) {
        exportConfig.onExport(format, exportData);
      }
    },
    [table, columnConfig, exportConfig]
  );

  return { handleExport };
};

import {
  ColumnDef,
  ColumnFiltersState,
  ColumnSizingState,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  PaginationState,
  RowSelectionState,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { debounce } from 'lodash';
import { useCallback, useMemo, useRef, useState } from 'react';

/**
 * Interface for table state management
 */
export interface TableState {
  sorting: SortingState;
  setSorting: React.Dispatch<React.SetStateAction<SortingState>>;
  columnFilters: ColumnFiltersState;
  setColumnFilters: React.Dispatch<React.SetStateAction<ColumnFiltersState>>;
  globalFilter: string;
  setGlobalFilter: React.Dispatch<React.SetStateAction<string>>;
  rowSelection: RowSelectionState;
  setRowSelection: React.Dispatch<React.SetStateAction<RowSelectionState>>;
  columnSizing: ColumnSizingState;
  setColumnSizing: React.Dispatch<React.SetStateAction<ColumnSizingState>>;
  pagination: PaginationState;
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>;
}

/**
 * Hook to manage all table state
 */
export const useTableState = (pageSize = 10): TableState => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [columnSizing, setColumnSizing] = useState<ColumnSizingState>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize,
  });

  return {
    sorting,
    setSorting,
    columnFilters,
    setColumnFilters,
    globalFilter,
    setGlobalFilter,
    rowSelection,
    setRowSelection,
    columnSizing,
    setColumnSizing,
    pagination,
    setPagination,
  };
};

/**
 * Interface for table configuration options
 */
export interface TableConfigOptions {
  enableSorting: boolean;
  enableFiltering: boolean;
  enableRowSelection: boolean;
  enableColumnResizing: boolean;
  enablePagination: boolean;
  debounceMs: number;
}

/**
 * Hook to create debounced global filter handler
 */
export const useGlobalFilter = (
  setGlobalFilter: React.Dispatch<React.SetStateAction<string>>,
  debounceMs = 300
) => {
  const debouncedGlobalFilter = useRef(
    debounce((value: string) => setGlobalFilter(value), debounceMs)
  ).current;

  const handleGlobalFilterChange = useCallback(
    (value: string) => {
      debouncedGlobalFilter(value);
    },
    [debouncedGlobalFilter]
  );

  return handleGlobalFilterChange;
};

/**
 * Hook to configure and create TanStack Table instance
 */
export const useTableConfiguration = <T extends Record<string, any>>(
  data: T[],
  columns: ColumnDef<T>[],
  tableState: TableState,
  columnVisibility: VisibilityState,
  setColumnVisibility: React.Dispatch<React.SetStateAction<VisibilityState>>,
  options: TableConfigOptions
) => {
  const {
    sorting,
    setSorting,
    columnFilters,
    setColumnFilters,
    globalFilter,
    setGlobalFilter,
    rowSelection,
    setRowSelection,
    columnSizing,
    setColumnSizing,
    pagination,
    setPagination,
  } = tableState;

  const {
    enableSorting,
    enableFiltering,
    enableRowSelection,
    enableColumnResizing,
  } = options;

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    state: {
      sorting,
      columnFilters,
      globalFilter,
      rowSelection: enableRowSelection ? rowSelection : {},
      columnVisibility,
      columnSizing: enableColumnResizing ? columnSizing : {},
      pagination,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onRowSelectionChange: enableRowSelection ? setRowSelection : undefined,
    onColumnVisibilityChange: setColumnVisibility,
    onColumnSizingChange: enableColumnResizing ? setColumnSizing : undefined,
    onPaginationChange: setPagination,
    enableSorting,
    enableFilters: enableFiltering,
    enableRowSelection,
    enableColumnResizing,
    globalFilterFn: 'includesString',
  });

  return table;
};

/**
 * Combined hook that provides complete table configuration
 */
export const useCompleteTableConfiguration = <T extends Record<string, any>>(
  data: T[],
  columns: ColumnDef<T>[],
  columnVisibility: VisibilityState,
  setColumnVisibility: React.Dispatch<React.SetStateAction<VisibilityState>>,
  pageSize = 10,
  options: Partial<TableConfigOptions> = {}
) => {
  const configOptions: TableConfigOptions = {
    enableSorting: false,
    enableFiltering: false,
    enableRowSelection: false,
    enableColumnResizing: false,
    enablePagination: false,
    debounceMs: 300,
    ...options,
  };
  const itemsPerPage = useMemo(
    () => (!configOptions.enablePagination ? data.length : pageSize),
    [data.length, configOptions.enablePagination, pageSize]
  );
  const tableState = useTableState(itemsPerPage);
  const handleGlobalFilterChange = useGlobalFilter(
    tableState.setGlobalFilter,
    configOptions.debounceMs
  );

  const table = useTableConfiguration(
    data,
    columns,
    tableState,
    columnVisibility,
    setColumnVisibility,
    configOptions
  );

  return {
    table,
    tableState,
    handleGlobalFilterChange,
    configOptions,
  };
};

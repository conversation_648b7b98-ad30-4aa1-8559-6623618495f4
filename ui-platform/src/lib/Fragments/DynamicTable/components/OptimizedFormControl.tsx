import React, {
  CSSProperties,
  memo,
  Suspense,
  useCallback,
  useMemo,
} from 'react';
import SelectionBox from '../../../Components/Inputs/CheckBoxes/CheckBoxMolecule/CheckBoxMolecule';
import { ActionConfig } from '../../../Engine/models/action.config';
import { FormBuilder } from '../../form-builder';
import { InputControlConfig } from '../../form-builder/types/input-control.config';
import { ColumnConfig, TableControlType } from '../types';

interface OptimizedCheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  name: string;
  hideLabel?: boolean;
  disabled?: boolean;
  rowIndex?: number;
  customStyle?: CSSProperties;
}

// Highly optimized checkbox component with minimal re-renders
export const OptimizedCheckbox = memo<OptimizedCheckboxProps>(
  ({ checked, onChange, name, hideLabel, disabled, rowIndex, customStyle }) => {
    // Use a stable callback to prevent parent re-renders
    const handleChange = useCallback(
      (newChecked: boolean) => {
        onChange(newChecked);
      },
      [onChange]
    );

    return (
      <SelectionBox
        name={name}
        checked={checked}
        onChange={handleChange}
        hideLabel={hideLabel}
        disabled={disabled}
        aria-label={`${name} for row ${
          rowIndex !== undefined ? rowIndex + 1 : ''
        }`}
        customStyle={customStyle}
      />
    );
  },
  (prevProps, nextProps) => {
    // Only re-render if essential props changed
    return (
      prevProps.checked === nextProps.checked &&
      prevProps.disabled === nextProps.disabled &&
      prevProps.name === nextProps.name &&
      prevProps.hideLabel === nextProps.hideLabel
    );
  }
);

interface OptimizedFormControlProps<T extends Record<string, any>> {
  config: ColumnConfig<T>;
  currentValue: any;
  row: T;
  rowIndex: number;
  uniqueControlName: string;
  _callClientAction?: (config: ActionConfig[]) => void;
}

// Highly optimized form control wrapper with minimal FormBuilder usage
export const OptimizedFormControl = memo(
  <T extends Record<string, any>>({
    config,
    currentValue,
    row,
    rowIndex,
    uniqueControlName,
    _callClientAction,
  }: OptimizedFormControlProps<T>) => {
    // Stable change handler to prevent parent re-renders
    const handleChange = useCallback(
      (newValue: any) => {
        if (config.onControlChange) {
          if (typeof config.onControlChange === 'function') {
            config.onControlChange(newValue, row, rowIndex);
          } else if (
            _callClientAction &&
            Array.isArray(config.onControlChange)
          ) {
            _callClientAction(
              config.onControlChange.map((action) => ({
                ...action,
                param: { value: newValue, row, rowIndex },
              }))
            );
          }
        }
      },
      [config.onControlChange, row, rowIndex, _callClientAction]
    );

    // Memoized control configuration to prevent recreation on every render
    const controlConfig = useMemo((): InputControlConfig => {
      const baseConfig = {
        name: config?.customControlName || uniqueControlName,
        type: config.controlType as any,
        ...(config.controlConfig || {}),
        ...(config.controlProps || {}),
        value:
          config.controlType === 'checkbox' ? !!currentValue : currentValue,
      };

      // Enhanced configurations for specific control types
      switch (config.controlType) {
        case 'single-select':
          return {
            ...baseConfig,
            type: 'single-select',
            options: config.controlProps?.options || {
              source: 'literal',
              data: [],
            },
            labelProp: config.controlProps?.labelProp || 'label',
            valueProp: config.controlProps?.valueProp || 'value',
          } as any;

        case 'multi-select':
          return {
            ...baseConfig,
            type: 'multi-select',
            options: config.controlProps?.options || {
              source: 'literal',
              data: [],
            },
            labelProp: config.controlProps?.labelProp || 'label',
            valueProp: config.controlProps?.valueProp || 'value',
          } as any;

        case 'radio-group':
          return {
            ...baseConfig,
            type: 'radio-group',
            options: config.controlProps?.options || {
              source: 'literal',
              data: [],
            },
            returnBoolean: config.controlProps?.returnBoolean || false,
            size: config.controlProps?.size || 'medium',
          } as any;

        case 'datepicker':
          return {
            ...baseConfig,
            type: 'datepicker',
            placeholder: config.controlProps?.placeholder || 'Select date...',
          } as any;

        case 'date-and-time-picker':
          return {
            ...baseConfig,
            type: 'date-and-time-picker',
            placeholder:
              config.controlProps?.placeholder || 'Select date and time...',
          } as any;

        case 'timepicker':
          return {
            ...baseConfig,
            type: 'timepicker',
            placeholder: config.controlProps?.placeholder || 'Select time...',
            validMinutes: config.controlProps?.validMinutes,
          } as any;

        case 'textarea':
          return {
            ...baseConfig,
            type: 'textarea',
            rows: config.controlProps?.rows || 3,
            cols: config.controlProps?.cols,
            maxLength: config.controlProps?.maxLength,
            placeholder: config.controlProps?.placeholder || '',
          } as any;

        case 'add-file':
          return {
            ...baseConfig,
            type: 'add-file',
            placeholder: config.controlProps?.placeholder || 'Choose file...',
          } as any;

        default:
          return baseConfig as InputControlConfig;
      }
    }, [config, uniqueControlName, currentValue]);

    // Memoized form configuration with optimized change handling
    const formConfig = useMemo(
      () => ({
        controls: [controlConfig],
        style: { margin: 0, padding: 0 },
        onSubmit: (formData: any) => {
          const newValue = formData[uniqueControlName];
          handleChange(newValue);
        },
        onChange: (formData: any) => {
          const newValue = formData[uniqueControlName];
          handleChange(newValue);
        },
      }),
      [controlConfig, uniqueControlName, handleChange]
    );

    // Memoized default values
    const defaultValues = useMemo(
      () => ({
        [uniqueControlName]: currentValue,
      }),
      [uniqueControlName, currentValue]
    );

    // Memoized validation
    const validationErrors = useMemo(
      () => (config.validator ? config.validator(currentValue) : null),
      [config.validator, currentValue]
    );

    return (
      <div style={{ position: 'relative' }}>
        <Suspense fallback={<div>Loading...</div>}>
          <FormBuilder
            config={formConfig}
            defaultValues={defaultValues}
            isStory={true}
            key={`${uniqueControlName}`}
          />
        </Suspense>
        {validationErrors && (
          <div
            style={{
              position: 'absolute',
              top: '100%',
              left: 0,
              background: '#fee2e2',
              color: '#dc2626',
              padding: '2px 6px',
              fontSize: '12px',
              borderRadius: '3px',
              zIndex: 10,
              whiteSpace: 'nowrap',
            }}
          >
            {validationErrors}
          </div>
        )}
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Optimized comparison for form controls
    return (
      prevProps.currentValue === nextProps.currentValue &&
      prevProps.rowIndex === nextProps.rowIndex &&
      prevProps.config.controlType === nextProps.config.controlType &&
      prevProps.uniqueControlName === nextProps.uniqueControlName
    );
  }
);

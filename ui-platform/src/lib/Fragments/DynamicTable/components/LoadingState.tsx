import React from 'react';
import { LoadingContainer } from './StyledComponents';

/**
 * Props for LoadingState component
 */
export interface LoadingStateProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
}

/**
 * Simple loading spinner component
 */
const LoadingSpinner: React.FC<{ size: 'small' | 'medium' | 'large' }> = ({ size }) => {
  const sizeMap = {
    small: 20,
    medium: 32,
    large: 48,
  };

  const spinnerSize = sizeMap[size];

  return (
    <div
      style={{
        width: spinnerSize,
        height: spinnerSize,
        border: `3px solid #f3f4f6`,
        borderTop: `3px solid #2563eb`,
        borderRadius: '50%',
        animation: 'spin 1s linear infinite',
        marginBottom: '16px',
      }}
    />
  );
};

/**
 * Loading state component for the table
 */
export const LoadingState: React.FC<LoadingStateProps> = ({
  message = 'Loading...',
  size = 'medium',
}) => {
  return (
    <LoadingContainer>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <LoadingSpinner size={size} />
      <div>{message}</div>
    </LoadingContainer>
  );
};

/**
 * Table loading overlay component
 */
export const TableLoadingOverlay: React.FC<LoadingStateProps> = (props) => {
  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(255, 255, 255, 0.8)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10,
      }}
    >
      <LoadingState {...props} />
    </div>
  );
};

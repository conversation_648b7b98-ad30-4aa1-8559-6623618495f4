import { flexRender, Table } from '@tanstack/react-table';
import React, { CSSProperties } from 'react';
import { IndeterminateCheckbox } from './IndeterminateCheckbox';
import {
  EmptyStateContainer,
  EmptyStateSubtext,
  EmptyStateText,
  Tbody,
  Td,
  Tr,
} from './StyledComponents';

/**
 * Props for TableBody component
 */
export interface TableBodyProps<T extends Record<string, any>> {
  table: Table<T>;
  enableRowSelection: boolean;
  onRowClick?: (row: T, index: number) => void;
  emptyStateMessage?: string;
  emptyStateSubMessage?: string;
  customStyles?: CSSProperties;
}

/**
 * Memoized table row component for performance
 */
const TableRow = React.memo(
  <T extends Record<string, any>>({
    row,
    enableRowSelection,
    onRowClick,
    customStyles,
  }: {
    row: any;
    enableRowSelection: boolean;
    onRowClick?: (row: T, index: number) => void;
    customStyles?: CSSProperties;
  }) => {
    const handleRowClick = React.useCallback(() => {
      if (onRowClick) {
        onRowClick(row.original, row.index);
      }
    }, [row.original, row.index, onRowClick]);

    return (
      <Tr
        key={row.id}
        clickable={!!onRowClick}
        selected={row.getIsSelected()}
        onClick={handleRowClick}
      >
        {/* Row selection column */}
        {enableRowSelection && (
          <Td>
            <IndeterminateCheckbox
              checked={row.getIsSelected()}
              disabled={!row.getCanSelect()}
              indeterminate={row.getIsSomeSelected()}
              onChange={row.getToggleSelectedHandler()}
              onClick={(e: React.MouseEvent<HTMLInputElement>) =>
                e.stopPropagation()
              }
              aria-label={`Select row ${row.index + 1}`}
            />
          </Td>
        )}

        {/* Data cells */}
        {row.getVisibleCells().map((cell: any) => (
          <Td key={cell.id} style={customStyles}>
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </Td>
        ))}
      </Tr>
    );
  }
);

// TableRow.displayName = 'TableRow';

/**
 * Empty state component
 */
const EmptyState: React.FC<{
  message?: string;
  subMessage?: string;
}> = ({
  message = 'No data available',
  subMessage = 'There are no records to display.',
}) => (
  <EmptyStateContainer>
    <EmptyStateText>{message}</EmptyStateText>
    <EmptyStateSubtext>{subMessage}</EmptyStateSubtext>
  </EmptyStateContainer>
);

/**
 * Memoized table body component for performance
 */
export const TableBody = React.memo(
  <T extends Record<string, any>>({
    table,
    enableRowSelection,
    onRowClick,
    emptyStateMessage,
    emptyStateSubMessage,
    customStyles,
  }: TableBodyProps<T>) => {
    const rows = table.getRowModel().rows;

    // Show empty state if no data
    if (rows.length === 0) {
      return (
        <Tbody>
          <Tr>
            <Td
              colSpan={
                table.getAllColumns().length + (enableRowSelection ? 1 : 0)
              }
              style={{ textAlign: 'center', padding: 0}}
            >
              <EmptyState
                message={emptyStateMessage}
                subMessage={emptyStateSubMessage}
              />
            </Td>
          </Tr>
        </Tbody>
      );
    }

    return (
      <Tbody>
        {rows.map((row) => (
          <TableRow
            key={row.id}
            row={row}
            enableRowSelection={enableRowSelection}
            // onRowClick={onRowClick}
          />
        ))}
      </Tbody>
    );
  }
) as <T extends Record<string, any>>(
  props: TableBodyProps<T>
) => React.ReactElement;

// TableBody.displayName = 'TableBody';

import React, { useEffect, useRef } from 'react';
import { SelectionCheckbox } from './StyledComponents';

/**
 * Props for IndeterminateCheckbox component
 */
export interface IndeterminateCheckboxProps {
  checked: boolean;
  indeterminate?: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onClick?: (event: React.MouseEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  'aria-label'?: string;
}

/**
 * Checkbox component that properly handles indeterminate state
 */
export const IndeterminateCheckbox: React.FC<IndeterminateCheckboxProps> = ({
  checked,
  indeterminate = false,
  onChange,
  onClick,
  disabled = false,
  'aria-label': ariaLabel,
}) => {
  const checkboxRef = useRef<HTMLInputElement>(null);

  // Set indeterminate state via DOM property
  useEffect(() => {
    if (checkboxRef.current) {
      checkboxRef.current.indeterminate = indeterminate;
    }
  }, [indeterminate]);

  return (
    <SelectionCheckbox
      ref={checkboxRef}
      checked={checked}
      onChange={onChange}
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
      indeterminate={indeterminate}
    />
  );
};

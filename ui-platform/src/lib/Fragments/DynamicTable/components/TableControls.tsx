import { Table } from '@tanstack/react-table';
import React, { useCallback } from 'react';
import styled from 'styled-components';
import { BulkAction, ColumnConfig, ExportConfig } from '../types';

// Styled components for table controls
const FilterContainer = styled.div`
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const GlobalFilterInput = styled.input`
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 8px;
  width: 240px;
  &:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 2px #bfdbfe;
  }
`;

const ColumnVisibilityContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const ColumnVisibilityButton = styled.button`
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background: #f9fafb;
  }
`;

const ColumnVisibilityDropdown = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 200px;
  max-height: 300px;
  overflow-y: auto;
  display: none;
`;

const ColumnVisibilityItem = styled.div`
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background: #f9fafb;
  }

  input {
    margin: 0;
  }

  label {
    margin: 0;
    cursor: pointer;
    font-size: 14px;
  }
`;

const ExportContainer = styled.div`
  display: flex;
  gap: 8px;
`;

const ExportButton = styled.button`
  padding: 8px 16px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;

  &:hover {
    background: #1d4ed8;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const BulkActionsContainer = styled.div`
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
`;

const BulkActionButton = styled.button<{ variant?: string }>`
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;

  ${({ variant }) => {
    switch (variant) {
      case 'danger':
        return `
          background: #dc2626;
          color: white;
          &:hover { background: #b91c1c; }
        `;
      case 'success':
        return `
          background: #16a34a;
          color: white;
          &:hover { background: #15803d; }
        `;
      case 'secondary':
        return `
          background: #6b7280;
          color: white;
          &:hover { background: #4b5563; }
        `;
      default:
        return `
          background: #2563eb;
          color: white;
          &:hover { background: #1d4ed8; }
        `;
    }
  }}

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

/**
 * Props for GlobalFilter component
 */
export interface GlobalFilterProps {
  onFilterChange: (value: string) => void;
  placeholder?: string;
}

/**
 * Global filter component
 */
export const GlobalFilter: React.FC<GlobalFilterProps> = ({
  onFilterChange,
  placeholder = 'Search all columns...',
}) => {
  return (
    <GlobalFilterInput
      type="text"
      placeholder={placeholder}
      onChange={(e) => onFilterChange(e.target.value)}
    />
  );
};

/**
 * Props for ColumnVisibilityToggle component
 */
export interface ColumnVisibilityToggleProps<T extends Record<string, any>> {
  table: Table<T>;
}

/**
 * Column visibility toggle component
 */
export const ColumnVisibilityToggle = <T extends Record<string, any>>({
  table,
}: ColumnVisibilityToggleProps<T>) => {
  const handleToggleDropdown = useCallback(() => {
    const dropdown = document.getElementById('column-visibility-dropdown');
    if (dropdown) {
      dropdown.style.display =
        dropdown.style.display === 'none' ? 'block' : 'none';
    }
  }, []);

  return (
    <ColumnVisibilityContainer>
      <ColumnVisibilityButton onClick={handleToggleDropdown}>
        Columns ▼
      </ColumnVisibilityButton>
      <ColumnVisibilityDropdown id="column-visibility-dropdown">
        {table.getAllColumns().map((column) => (
          <ColumnVisibilityItem key={column.id}>
            <input
              type="checkbox"
              id={`column-${column.id}`}
              checked={column.getIsVisible()}
              onChange={column.getToggleVisibilityHandler()}
              aria-describedby={`column-${column.id}-label`}
            />
            <label
              htmlFor={`column-${column.id}`}
              id={`column-${column.id}-label`}
            >
              {(column.columnDef.header as string) || column.id}
            </label>
          </ColumnVisibilityItem>
        ))}
      </ColumnVisibilityDropdown>
    </ColumnVisibilityContainer>
  );
};

/**
 * Props for ExportControls component
 */
export interface ExportControlsProps<T extends Record<string, any>> {
  table: Table<T>;
  exportConfig: ExportConfig;
  columnConfig: Partial<Record<keyof T, ColumnConfig<T>>>;
  onExport: (format: 'csv' | 'excel' | 'pdf') => void;
}

/**
 * Export controls component
 */
export const ExportControls = <T extends Record<string, any>>({
  exportConfig,
  onExport,
}: ExportControlsProps<T>) => {
  if (!exportConfig.enabled) return null;

  return (
    <ExportContainer>
      <ExportButton onClick={() => onExport('csv')}>Export CSV</ExportButton>
      {exportConfig.formats?.includes('excel') && (
        <ExportButton onClick={() => onExport('excel')}>
          Export Excel
        </ExportButton>
      )}
      {exportConfig.formats?.includes('pdf') && (
        <ExportButton onClick={() => onExport('pdf')}>Export PDF</ExportButton>
      )}
    </ExportContainer>
  );
};

/**
 * Props for BulkActions component
 */
export interface BulkActionsProps<T extends Record<string, any>> {
  table: Table<T>;
  bulkActions: BulkAction<T>[];
  enableRowSelection: boolean;
}

/**
 * Bulk actions component
 */
export const BulkActions = <T extends Record<string, any>>({
  table,
  bulkActions,
  enableRowSelection,
}: BulkActionsProps<T>) => {
  const rowSelection = table.getState().rowSelection;
  const hasSelectedRows = Object.keys(rowSelection).length > 0;

  if (!enableRowSelection || bulkActions.length === 0 || !hasSelectedRows) {
    return null;
  }

  return (
    <BulkActionsContainer>
      {bulkActions.map((action) => (
        <BulkActionButton
          key={action.id}
          variant={action.variant}
          onClick={() => {
            const selectedRows = table
              .getSelectedRowModel()
              .rows.map((row) => row.original);
            const selectedIndices = table
              .getSelectedRowModel()
              .rows.map((row) => row.index);
            action.onClick(selectedRows, selectedIndices);
          }}
          disabled={action.disabled?.(
            table.getSelectedRowModel().rows.map((row) => row.original)
          )}
        >
          {action.label}
        </BulkActionButton>
      ))}
    </BulkActionsContainer>
  );
};

/**
 * Props for TableControlsBar component
 */
export interface TableControlsBarProps<T extends Record<string, any>> {
  table: Table<T>;
  enableFiltering: boolean;
  exportConfig: ExportConfig;
  columnConfig: Partial<Record<keyof T, ColumnConfig<T>>>;
  onGlobalFilterChange: (value: string) => void;
  onExport: (format: 'csv' | 'excel' | 'pdf') => void;
  showColumnVisibility?: boolean;
  bulkActions?: BulkAction<T>[];
  enableRowSelection?: boolean;
}

/**
 * Combined table controls bar component
 */
export const TableControlsBar = <T extends Record<string, any>>({
  table,
  enableFiltering,
  exportConfig,
  columnConfig,
  onGlobalFilterChange,
  onExport,
  showColumnVisibility = false,
  bulkActions = [],
  enableRowSelection = false,
}: TableControlsBarProps<T>) => {
  const shouldShowControls =
    enableFiltering ||
    Object.keys(table.getState().columnVisibility).length > 0 ||
    exportConfig.enabled;

  if (!shouldShowControls) return null;

  return (
    <>
      <BulkActions
        table={table}
        bulkActions={bulkActions}
        enableRowSelection={enableRowSelection}
      />
      <FilterContainer>
        {enableFiltering && (
          <GlobalFilter onFilterChange={onGlobalFilterChange} />
        )}

        {showColumnVisibility && <ColumnVisibilityToggle table={table} />}

        <ExportControls
          table={table}
          exportConfig={exportConfig}
          columnConfig={columnConfig}
          onExport={onExport}
        />
      </FilterContainer>
    </>
  );
};

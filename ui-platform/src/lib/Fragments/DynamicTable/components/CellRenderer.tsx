import React, { memo, useCallback, useMemo } from 'react';
import { ActionConfig } from '../../../Engine/models/action.config';
import {
  excludeMatchingDataInArray,
  matchingDataInArray,
} from '../../../Utilities';
import { Text } from '../../Text';
import { ColumnConfig } from '../types';
import {
  OptimizedCheckbox,
  OptimizedFormControl,
} from './OptimizedFormControl';

interface CellRendererProps<T extends Record<string, any>> {
  value: any;
  config?: ColumnConfig<T>;
  row: T;
  rowIndex: number;
  _callClientAction?: (config: ActionConfig[]) => void;
  _store?: any;
  storeKeyName?: string;
  updatedCheckValueKey?: string;
  storeRefColumnName?: string;
}

// Memoized cell renderer for performance optimization
export const CellRenderer = memo(
  <T extends Record<string, any>>({
    value,
    config,
    row,
    rowIndex,
    _callClientAction,
    _store,
    storeKeyName = 'modifiedTableRows',
    updatedCheckValueKey = 'updatedVal',
    storeRefColumnName,
  }: CellRendererProps<T>) => {
    const configWithPresets = useMemo(
      () => ({
        cellAlignment: 'left',
        ...config,
      }),
      [config]
    );
    // Stable change handler for checkbox
    const handleCheckboxChange = useCallback(
      (checked: boolean) => {
        // state synchronisation
        if (_store) {
          _store.setState((state: any) => {
            const modifiedTableRows = state?.[storeKeyName] ?? [];
            const updatedModifiedTableRows = excludeMatchingDataInArray(
              modifiedTableRows,
              row,
              storeRefColumnName
            );
            // const previouslyModifiedMatch = matchingDataInArray(
            //   modifiedTableRows,
            //   row,
            //   storeRefColumnName
            // );
            // if (row[updatedCheckValueKey] === checked) {
            //   return updatedModifiedTableRows;
            // }
            return {
              [storeKeyName]: [
                ...updatedModifiedTableRows,
                { ...row, [updatedCheckValueKey]: checked },
              ],
            };
          });
        }

        if (configWithPresets?.onControlChange) {
          if (typeof configWithPresets.onControlChange === 'function') {
            configWithPresets.onControlChange(checked, row, rowIndex);
          } else if (
            _callClientAction &&
            Array.isArray(configWithPresets.onControlChange)
          ) {
            _callClientAction(
              configWithPresets.onControlChange.map((action) => ({
                ...action,
                param: { value: checked, row, rowIndex },
              }))
            );
          }
        }
      },
      [configWithPresets?.onControlChange, row, rowIndex, _callClientAction]
    );

    // If a cell render function is provided, use it
    if (configWithPresets?.cell) {
      return configWithPresets.cell(
        value,
        row,
        rowIndex,
        configWithPresets.onControlChange
          ? (val: any) =>
              typeof configWithPresets.onControlChange === 'function'
                ? configWithPresets.onControlChange(val, row, rowIndex)
                : _callClientAction &&
                  Array.isArray(configWithPresets.onControlChange) &&
                  configWithPresets.onControlChange.length > 0 &&
                  (configWithPresets.onControlChange satisfies ActionConfig[])
                ? _callClientAction(
                    (configWithPresets.onControlChange as ActionConfig[]).map((o) => ({
                      ...o,
                      param: { value: val, row, rowIndex },
                    }))
                  )
                : undefined
          : undefined
      );
    }

    // Render form controls if specified
    if (configWithPresets?.controlType && row && configWithPresets.key) {
      const controlName = configWithPresets.key as keyof T as string;
      const currentValue = row[configWithPresets.key];

      // For simple checkbox, use optimized component to avoid FormBuilder overhead
      if (configWithPresets.controlType === 'checkbox') {
        const headerText =
          typeof configWithPresets.header === 'string'
            ? configWithPresets.header
            : configWithPresets.headerTextConfig
            ? configWithPresets.headerTextConfig.text
            : controlName;

        const alignmentStyle: Record<string, {display: string, justifyContent: string}> = {
          'left': {display: 'flex', justifyContent: 'start'},
          'right': {display: 'flex', justifyContent: 'end'},
          'center': {display: 'flex', justifyContent: 'center'}
        }

        return (
          <OptimizedCheckbox
            data-testId={`${controlName}_row_${rowIndex}`}
            checked={!!currentValue}
            onChange={handleCheckboxChange}
            name={headerText}
            hideLabel={configWithPresets.controlProps?.hideLabel}
            disabled={configWithPresets.controlProps?.disabled}
            rowIndex={rowIndex}
            customStyle={alignmentStyle[configWithPresets.cellAlignment]}
          />
        );
      }

      // For other controls, use optimized form control wrapper
      const uniqueControlName = `${controlName}_row_${rowIndex}`;

      return (
        <OptimizedFormControl
          config={configWithPresets as ColumnConfig<Record<string, any>>}
          currentValue={currentValue}
          row={row}
          rowIndex={rowIndex}
          uniqueControlName={uniqueControlName}
          _callClientAction={_callClientAction}
        />
      );
    }

    // If a bodyTextConfig is provided, use it for Text rendering
    if (configWithPresets?.bodyTextConfig) {
      const textConfig = configWithPresets.bodyTextConfig(value, row);
      return <Text textItems={[textConfig]} />;
    }

    // Fallbacks with enhanced type handling
    if (value === null || value === undefined) {
      return (
        <Text
          textItems={[
            {
              text: '-',
              options: {
                format: 'paragraph',
                type: 'value',
              },
            },
          ]}
        />
      );
    }

    if (typeof value === 'boolean') {
      return (
        <Text
          textItems={[
            {
              text: value ? 'Yes' : 'No',
              options: {
                format: 'paragraph',
                type: 'value',
                style: {
                  padding: '2px 8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: 500,
                  background: value ? '#dcfce7' : '#fee2e2',
                  color: value ? '#166534' : '#991b1b',
                },
              },
            },
          ]}
        />
      );
    }

    if (typeof value === 'number') {
      const formattedValue = configWithPresets?.formatter
        ? configWithPresets.formatter(value)
        : value.toLocaleString();
      return (
        <Text
          textItems={[
            {
              text: formattedValue,
              options: {
                format: 'paragraph',
                type: 'value',
                style: { fontFamily: 'monospace' },
              },
            },
          ]}
        />
      );
    }

    if (value instanceof Date) {
      const formattedValue = configWithPresets?.formatter
        ? configWithPresets.formatter(value)
        : value.toLocaleDateString();
      return (
        <Text
          textItems={[
            {
              text: formattedValue,
              options: {
                format: 'paragraph',
                type: 'value',
                style: { fontFamily: 'monospace' },
              },
            },
          ]}
        />
      );
    }

    if (typeof value === 'string' && value.startsWith('http')) {
      return (
        <Text
          textItems={[
            {
              text: 'Link',
              options: {
                format: 'paragraph',
                type: 'value',
                style: { color: '#2563eb', textDecoration: 'underline' },
              },
            },
          ]}
        />
      );
    }

    if (typeof value === 'object') {
      const displayValue = configWithPresets?.formatter
        ? configWithPresets.formatter(value)
        : JSON.stringify(value, null, 2);
      return (
        <Text
          textItems={[
            {
              text: displayValue,
              options: {
                format: 'paragraph',
                type: 'value',
                style: {
                  fontSize: '12px',
                  background: '#f3f4f6',
                  padding: '4px',
                  borderRadius: '4px',
                  maxWidth: '320px',
                  overflow: 'hidden',
                },
              },
            },
          ]}
        />
      );
    }

    const displayValue = configWithPresets?.formatter
      ? configWithPresets.formatter(value)
      : String(value);
    return (
      <Text
        textItems={[
          {
            text: displayValue,
            options: {
              format: 'paragraph',
              type: 'value',
            },
          },
        ]}
      />
    );
  },
  (prevProps, nextProps) => {
    // Fast path: if row index changed, always re-render
    if (prevProps.rowIndex !== nextProps.rowIndex) return false;

    // Fast path: if value changed, re-render
    if (prevProps.value !== nextProps.value) return false;

    // Fast path: if config changed, re-render
    if (prevProps.config !== nextProps.config) return false;

    // For control types, check the actual row value for the specific key
    if (prevProps.config?.controlType && nextProps.config?.controlType) {
      const prevRowValue = prevProps.row[prevProps.config.key];
      const nextRowValue = nextProps.row[nextProps.config.key];

      // Only re-render if the specific field value changed
      if (prevRowValue !== nextRowValue) return false;

      // Also check if control type changed
      if (prevProps.config.controlType !== nextProps.config.controlType)
        return false;
    }

    // For cells with custom render functions, do a shallow comparison
    if (prevProps.config?.cell && nextProps.config?.cell) {
      // Re-render if the cell function changed or row data changed
      if (prevProps.config.cell !== nextProps.config.cell) return false;
      if (prevProps.row !== nextProps.row) return false;
    }

    // For text config cells, check if the config changed
    if (prevProps.config?.bodyTextConfig && nextProps.config?.bodyTextConfig) {
      if (prevProps.config.bodyTextConfig !== nextProps.config.bodyTextConfig)
        return false;
    }

    // Default: don't re-render if nothing significant changed
    return true;
  }
);

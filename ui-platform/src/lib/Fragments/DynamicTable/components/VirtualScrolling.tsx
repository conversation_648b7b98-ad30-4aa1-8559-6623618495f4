import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import { VirtualScrollConfig } from '../types';

// Virtual scrolling styled components
const VirtualScrollContainer = styled.div<{ height: number }>`
  height: ${({ height }) => height}px;
  overflow: auto;
  position: relative;
`;

const VirtualScrollContent = styled.div<{ totalHeight: number }>`
  height: ${({ totalHeight }) => totalHeight}px;
  position: relative;
`;

const VirtualScrollViewport = styled.div<{
  translateY: number;
  height: number;
}>`
  transform: translateY(${({ translateY }) => translateY}px);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: ${({ height }) => height}px;
`;

/**
 * Interface for virtual scrolling calculations
 */
export interface VirtualScrollCalculations {
  totalHeight: number;
  visibleCount: number;
  startIndex: number;
  endIndex: number;
  offsetY: number;
}

/**
 * Interface for virtual scrolling hook return value
 */
export interface VirtualScrollingResult<T> extends VirtualScrollCalculations {
  visibleItems: T[];
  handleScroll: (e: React.UIEvent<HTMLDivElement>) => void;
}

/**
 * Enhanced virtual scrolling hook with performance optimizations
 */
export const useVirtualScrolling = <T,>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5,
}: {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}): VirtualScrollingResult<T> => {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollTopRef = useRef(0);
  const rafRef = useRef<number>();

  // Optimized scroll handler with RAF throttling
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    scrollTopRef.current = newScrollTop;

    // Cancel previous RAF if still pending
    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
    }

    // Use RAF to throttle scroll updates
    rafRef.current = requestAnimationFrame(() => {
      setScrollTop(scrollTopRef.current);
    });
  }, []);

  // Cleanup RAF on unmount
  useEffect(() => {
    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, []);

  // Memoized calculations for better performance
  const calculations = useMemo(() => {
    const totalHeight = items.length * itemHeight;
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const startIndex = Math.max(
      0,
      Math.floor(scrollTop / itemHeight) - overscan
    );
    const endIndex = Math.min(
      items.length - 1,
      startIndex + visibleCount + overscan * 2
    );
    const offsetY = startIndex * itemHeight;

    return {
      totalHeight,
      visibleCount,
      startIndex,
      endIndex,
      offsetY,
    };
  }, [items.length, itemHeight, containerHeight, scrollTop, overscan]);

  // Memoized visible items slice
  const visibleItems = useMemo(() => {
    return items.slice(calculations.startIndex, calculations.endIndex + 1);
  }, [items, calculations.startIndex, calculations.endIndex]);

  return {
    ...calculations,
    visibleItems,
    handleScroll,
  };
};

/**
 * Props for VirtualScrollWrapper component
 */
export interface VirtualScrollWrapperProps {
  children: React.ReactNode;
  height: number;
  totalHeight: number;
  offsetY: number;
  viewportHeight: number;
  onScroll: (e: React.UIEvent<HTMLDivElement>) => void;
}

/**
 * Virtual scroll wrapper component
 */
export const VirtualScrollWrapper: React.FC<VirtualScrollWrapperProps> = ({
  children,
  height,
  totalHeight,
  offsetY,
  viewportHeight,
  onScroll,
}) => {
  return (
    <VirtualScrollContainer height={height} onScroll={onScroll}>
      <VirtualScrollContent totalHeight={totalHeight}>
        <VirtualScrollViewport translateY={offsetY} height={viewportHeight}>
          {children}
        </VirtualScrollViewport>
      </VirtualScrollContent>
    </VirtualScrollContainer>
  );
};

/**
 * Hook to determine if virtual scrolling should be enabled
 */
export const useShouldUseVirtualScrolling = (
  enableVirtualScrolling: boolean,
  virtualScrollConfig: VirtualScrollConfig,
  dataLength: number
) => {
  return useMemo(() => {
    return (
      enableVirtualScrolling &&
      virtualScrollConfig.enabled &&
      dataLength >= (virtualScrollConfig.threshold || 100)
    );
  }, [enableVirtualScrolling, virtualScrollConfig, dataLength]);
};

/**
 * Hook to calculate container height for virtual scrolling
 */
export const useContainerHeight = (height?: string | number) => {
  return useMemo(() => {
    if (typeof height === 'number') return height;
    if (typeof height === 'string' && height.endsWith('px')) {
      return parseInt(height);
    }
    return 400; // Default height for virtual scrolling
  }, [height]);
};

import { Table } from '@tanstack/react-table';
import React from 'react';
import {
  PageInfo,
  PaginationButton,
  PaginationButtons,
  PaginationWrapper,
} from './StyledComponents';

/**
 * Props for TablePagination component
 */
export interface TablePaginationProps<T extends Record<string, any>> {
  table: Table<T>;
  showPageSizeSelector?: boolean;
  pageSizeOptions?: number[];
}

/**
 * Memoized table pagination component
 */
export const TablePagination = React.memo(
  <T extends Record<string, any>>({
    table,
    showPageSizeSelector = true,
    pageSizeOptions = [10, 20, 30, 40, 50],
  }: TablePaginationProps<T>) => {
    const pageCount = table.getPageCount();
    const currentPage = table.getState().pagination.pageIndex;
    const pageSize = table.getState().pagination.pageSize;
    const totalRows = table.getFilteredRowModel().rows.length;

    // Don't show pagination if there's only one page or no data
    if (pageCount <= 1 && totalRows <= pageSize) {
      return null;
    }

    const startRow = currentPage * pageSize + 1;
    const endRow = Math.min((currentPage + 1) * pageSize, totalRows);

    return (
      <PaginationWrapper>
        {/* Page info */}
        <PageInfo>
          Showing {startRow} to {endRow} of {totalRows} results
        </PageInfo>

        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {/* Page size selector */}
          {showPageSizeSelector && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ fontSize: '14px' }}>Show:</span>
              <select
                value={pageSize}
                onChange={(e) => {
                  table.setPageSize(Number(e.target.value));
                }}
                style={{
                  padding: '4px 8px',
                  fontSize: '14px',
                  borderRadius: '4px',
                  border: '1px solid #d1d5db',
                }}
              >
                {pageSizeOptions.map((size) => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Pagination buttons */}
          <PaginationButtons>
            {/* First page */}
            <PaginationButton
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
              title="Go to first page"
            >
              {'<<'}
            </PaginationButton>

            {/* Previous page */}
            <PaginationButton
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              title="Go to previous page"
            >
              {'<'}
            </PaginationButton>

            {/* Page numbers */}
            {(() => {
              const pages = [];
              const maxVisiblePages = 5;
              let startPage = Math.max(
                0,
                currentPage - Math.floor(maxVisiblePages / 2)
              );
              const endPage = Math.min(
                pageCount - 1,
                startPage + maxVisiblePages - 1
              );

              // Adjust start page if we're near the end
              if (endPage - startPage < maxVisiblePages - 1) {
                startPage = Math.max(0, endPage - maxVisiblePages + 1);
              }

              // Add ellipsis at the beginning if needed
              if (startPage > 0) {
                pages.push(
                  <PaginationButton
                    key={0}
                    onClick={() => table.setPageIndex(0)}
                    style={{
                      background: currentPage === 0 ? '#2563eb' : 'transparent',
                      color: currentPage === 0 ? 'white' : 'inherit',
                    }}
                  >
                    1
                  </PaginationButton>
                );
                if (startPage > 1) {
                  pages.push(
                    <span key="ellipsis-start" style={{ padding: '0 8px' }}>
                      ...
                    </span>
                  );
                }
              }

              // Add visible page numbers
              for (let i = startPage; i <= endPage; i++) {
                pages.push(
                  <PaginationButton
                    key={i}
                    onClick={() => table.setPageIndex(i)}
                    style={{
                      background: currentPage === i ? '#2563eb' : 'transparent',
                      color: currentPage === i ? 'white' : 'inherit',
                    }}
                  >
                    {i + 1}
                  </PaginationButton>
                );
              }

              // Add ellipsis at the end if needed
              if (endPage < pageCount - 1) {
                if (endPage < pageCount - 2) {
                  pages.push(
                    <span key="ellipsis-end" style={{ padding: '0 8px' }}>
                      ...
                    </span>
                  );
                }
                pages.push(
                  <PaginationButton
                    key={pageCount - 1}
                    onClick={() => table.setPageIndex(pageCount - 1)}
                    style={{
                      background:
                        currentPage === pageCount - 1
                          ? '#2563eb'
                          : 'transparent',
                      color:
                        currentPage === pageCount - 1 ? 'white' : 'inherit',
                    }}
                  >
                    {pageCount}
                  </PaginationButton>
                );
              }

              return pages;
            })()}

            {/* Next page */}
            <PaginationButton
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              title="Go to next page"
            >
              {'>'}
            </PaginationButton>

            {/* Last page */}
            <PaginationButton
              onClick={() => table.setPageIndex(pageCount - 1)}
              disabled={!table.getCanNextPage()}
              title="Go to last page"
            >
              {'>>'}
            </PaginationButton>
          </PaginationButtons>
        </div>
      </PaginationWrapper>
    );
  }
) as <T extends Record<string, any>>(
  props: TablePaginationProps<T>
) => React.ReactElement;

// TablePagination.displayName = 'TablePagination';

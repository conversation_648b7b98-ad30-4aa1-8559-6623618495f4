import { ColumnMeta, flexRender, Table } from '@tanstack/react-table';
import React from 'react';
import { IndeterminateCheckbox } from './IndeterminateCheckbox';
import {
  ColumnResizer,
  FilterInput,
  SortIndicator,
  Th,
  Thead,
  Tr,
} from './StyledComponents';

/**
 * Props for TableHeader component
 */
export interface TableHeaderProps<T extends Record<string, any>> {
  table: Table<T>;
  enableFiltering: boolean;
  enableRowSelection: boolean;
  enableColumnResizing: boolean;
  onRowClick?: (row: T, index: number) => void;
}

interface CustomColumnMeta<T> extends ColumnMeta<T, unknown> {
  cellAlignment: 'left' | 'right' | 'center';
}

/**
 * Memoized table header component for performance
 */
export const TableHeader = React.memo(
  <T extends Record<string, any>>({
    table,
    enableFiltering,
    enableRowSelection,
    enableColumnResizing,
  }: TableHeaderProps<T>) => {
    const alignmentStyle = {
      left: { display: 'flex', justifyContent: 'start' },
      right: { display: 'flex', justifyContent: 'end' },
      center: { display: 'flex', justifyContent: 'center' },
    };
    return (
      <Thead>
        {table.getHeaderGroups().map((headerGroup) => (
          <Tr key={headerGroup.id}>
            {/* Row selection column */}
            {enableRowSelection && (
              <Th>
                <IndeterminateCheckbox
                  checked={table.getIsAllRowsSelected()}
                  indeterminate={table.getIsSomeRowsSelected()}
                  onChange={table.getToggleAllRowsSelectedHandler()}
                  aria-label="Select all rows"
                />
              </Th>
            )}

            {/* Data columns */}
            {headerGroup.headers.map((header) => (
              <Th
                key={header.id}
                style={{
                  width: header.getSize(),
                  position: 'relative',
                }}
              >
                {/* Header content */}
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent:
                      (header.column.columnDef.meta as CustomColumnMeta<T>)
                        ?.cellAlignment || 'left',
                    cursor: header.column.getCanSort() ? 'pointer' : 'default',
                  }}
                  onClick={header.column.getToggleSortingHandler()}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}

                  {/* Sort indicator */}
                  {header.column.getCanSort() && (
                    <SortIndicator
                      direction={
                        header.column.getIsSorted() === 'asc'
                          ? 'asc'
                          : header.column.getIsSorted() === 'desc'
                          ? 'desc'
                          : undefined
                      }
                    />
                  )}
                </div>

                {/* Column filter */}
                {enableFiltering && header.column.getCanFilter() && (
                  <FilterInput
                    type="text"
                    value={(header.column.getFilterValue() ?? '') as string}
                    onChange={(e) =>
                      header.column.setFilterValue(e.target.value)
                    }
                    placeholder={`Filter ${header.id}...`}
                    onClick={(e) => e.stopPropagation()}
                  />
                )}

                {/* Column resizer */}
                {enableColumnResizing && header.column.getCanResize() && (
                  <ColumnResizer
                    onMouseDown={header.getResizeHandler()}
                    onTouchStart={header.getResizeHandler()}
                    className={
                      header.column.getIsResizing() ? 'isResizing' : ''
                    }
                  />
                )}
              </Th>
            ))}
          </Tr>
        ))}
      </Thead>
    );
  }
) as <T extends Record<string, any>>(
  props: TableHeaderProps<T>
) => React.ReactElement;

// TableHeader.displayName = 'TableHeader';

import styled, { css } from 'styled-components';

// Main table wrapper
export const TableWrapper = styled.div<{ border?: string }>`
  overflow-x: auto;
  border-radius: 8px;
  ${({ border }) =>
    border
      ? css`
          border: ${border};
        `
      : ''}
`;

// Table element
export const StyledTable = styled.table`
  min-width: 100%;
  border-collapse: collapse;
`;

// Header components
export const Thead = styled.thead`
  /* background: #f9fafb; */
`;

export const Th = styled.th`
  padding: 12px 24px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  /* color: #6b7280; */
  /* text-transform: uppercase; */
  letter-spacing: 0.05em;
  /* background: #f9fafb; */
  position: relative;
`;

// Filter input for column filters
export const FilterInput = styled.input`
  margin-top: 4px;
  width: 100%;
  padding: 4px 8px;
  font-size: 12px;
  /* border: 1px solid #d1d5db; */
  border-radius: 4px;
  box-sizing: border-box;
`;

// Body components
export const Tbody = styled.tbody`
  /* background: #fff; */
`;

export const Tr = styled.tr<{ clickable?: boolean; selected?: boolean }>`
  ${({ clickable }) => clickable && `cursor: pointer;`}
  /* ${({ selected }) => selected && `background: #eff6ff;`} */

  &:hover {
    /* background: ${({ selected }) => (selected ? '#dbeafe' : '#f3f4f6')}; */
  }
`;

export const Td = styled.td`
  padding: 16px 24px;
  white-space: nowrap;
  font-size: 14px;
  font-style: '';
  /* color: #111827; */
`;

// Pagination components
export const PaginationWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
`;

export const PaginationButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const PaginationButton = styled.button`
  padding: 4px 12px;
  font-size: 14px;
  /* border: 1px solid #d1d5db; */
  border-radius: 4px;
  /* background: #fff; */
  cursor: pointer;
  transition: background 0.2s;
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

export const PageInfo = styled.span`
  font-size: 14px;
  /* color: #374151; */
`;

// Loading and empty state components
export const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  font-size: 16px;
  color: #6b7280;
`;

export const EmptyStateContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
  text-align: center;
`;

export const EmptyStateText = styled.p`
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 8px 0;
`;

export const EmptyStateSubtext = styled.p`
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
`;

// Sorting indicator
export const SortIndicator = styled.span<{ direction?: 'asc' | 'desc' }>`
  margin-left: 8px;
  font-size: 12px;
  color: #6b7280;

  ${({ direction }) => {
    if (direction === 'asc') return '&::after { content: "↑"; }';
    if (direction === 'desc') return '&::after { content: "↓"; }';
    return '&::after { content: "↕"; opacity: 0.3; }';
  }}
`;

// Row selection checkbox
export const SelectionCheckbox = styled.input.attrs({ type: 'checkbox' })<{
  indeterminate?: boolean;
}>`
  margin: 0;
  cursor: pointer;

  /* Handle indeterminate state styling */
  &:indeterminate {
    opacity: 0.8;
  }
`;

// Resizer for column resizing
export const ColumnResizer = styled.div`
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 5px;
  background: rgba(0, 0, 0, 0.1);
  cursor: col-resize;
  user-select: none;
  touch-action: none;
  opacity: 0;
  transition: opacity 0.2s;

  &:hover,
  &.isResizing {
    opacity: 1;
    background: #2563eb;
  }
`;

// Container for table controls
export const TableControlsContainer = styled.div`
  margin-bottom: 16px;
`;

// Container for the entire table component
export const DynamicTableContainer = styled.div`
  width: 100%;
`;

// Error state container
export const ErrorContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
  text-align: center;
  border: 1px solid #fecaca;
  border-radius: 8px;
  background: #fef2f2;
`;

export const ErrorText = styled.p`
  font-size: 16px;
  color: #dc2626;
  margin: 0 0 8px 0;
  font-weight: 600;
`;

export const ErrorSubtext = styled.p`
  font-size: 14px;
  color: #7f1d1d;
  margin: 0;
`;

// Virtual scrolling specific components
export const VirtualTableContainer = styled.div<{ height: number }>`
  height: ${({ height }) => height}px;
  overflow: auto;
  position: relative;
`;

export const VirtualTableContent = styled.div<{ totalHeight: number }>`
  height: ${({ totalHeight }) => totalHeight}px;
  position: relative;
`;

export const VirtualTableViewport = styled.div<{
  translateY: number;
  height: number;
}>`
  transform: translateY(${({ translateY }) => translateY}px);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: ${({ height }) => height}px;
`;

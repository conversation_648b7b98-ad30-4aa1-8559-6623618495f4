import React from 'react';
import {
  ErrorContainer,
  ErrorText,
  ErrorSubtext,
} from './StyledComponents';

/**
 * Props for ErrorState component
 */
export interface ErrorStateProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  showRetryButton?: boolean;
}

/**
 * Error state component for the table
 */
export const ErrorState: React.FC<ErrorStateProps> = ({
  title = 'Something went wrong',
  message = 'An error occurred while loading the data. Please try again.',
  onRetry,
  showRetryButton = true,
}) => {
  return (
    <ErrorContainer>
      <ErrorText>{title}</ErrorText>
      <ErrorSubtext>{message}</ErrorSubtext>
      {showRetryButton && onRetry && (
        <button
          onClick={onRetry}
          style={{
            marginTop: '16px',
            padding: '8px 16px',
            background: '#dc2626',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
          }}
        >
          Try Again
        </button>
      )}
    </ErrorContainer>
  );
};

/**
 * Table error overlay component
 */
export const TableErrorOverlay: React.FC<ErrorStateProps> = (props) => {
  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(255, 255, 255, 0.95)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10,
      }}
    >
      <ErrorState {...props} />
    </div>
  );
};

import React, { useEffect } from 'react';

// Import modular components and hooks
import {
  useContainerHeight,
  useShouldUseVirtualScrolling,
  useVirtualScrolling,
  VirtualScrollWrapper,
} from './components/VirtualScrolling';
import { useColumnVisibility } from './hooks/useColumnVisibility';
import { usePerformanceOptimization } from './hooks/usePerformanceOptimization';
import { useColumnGeneration } from './modules/columnGeneration';
import { useExportFunctionality } from './modules/exportFunctionality';
import {
  TableFormProvider,
  useFormIntegration,
} from './modules/formIntegration';
import { useCompleteTableConfiguration } from './modules/tableConfiguration';

// Import UI components
import { logger } from '../../Utilities';
import { ErrorState } from './components/ErrorState';
import { LoadingState } from './components/LoadingState';
import {
  DynamicTableContainer,
  StyledTable,
  TableControlsContainer,
  TableWrapper,
} from './components/StyledComponents';
import { TableBody } from './components/TableBody';
import { TableControlsBar } from './components/TableControls';
import { TableHeader } from './components/TableHeader';
import { TablePagination } from './components/TablePagination';

// Import types
import { DynamicTableProps } from './types';

/**
 * Enhanced DynamicTable component with modular architecture
 */
export const DynamicTable = <
  T extends Record<string, any> // Data type
  // U extends Partial<keyof T>
>({
  data = [],
  columnConfig = {},
  displayColumns,
  columnVisibilityConfig,
  enableSorting = false,
  enableFiltering = false,
  enableRowSelection = false,
  enableColumnResizing = false,
  enableVirtualScrolling = false,
  virtualScrollConfig = { enabled: false, itemHeight: 50, threshold: 100 },
  exportConfig = { enabled: false },
  formIntegration = { enabled: false },
  bulkActions = [],
  pageSize = 10,
  height,
  border,
  onRowClick,
  onSelectionChange,
  onDataChange,
  _callClientAction,
  loading = false,
  error,
  loadingConfig = { enabled: true },
  errorConfig = { enabled: true },
  debounceMs = 300,
  _store,
  storeKeyName = 'modifiedTableRows',
  updatedCheckValueKey = 'updatedVal',
  storeRefColumnName,
  showColumnVisibility = false,
  debug = false,
  enablePagination = false,
}: DynamicTableProps<T>) => {
  // Performance optimization hook
  logger.configure({
    enabled: debug,
    prefix: '🥸[Dynamic Table]:',
    options: { style: { backgroundColor: '#efefef', color: '#460a50' } },
  });
  const log = logger.log;
  log`Rendering DynamicTable - props: ${{
    data,
    columnConfig,
    displayColumns,
    columnVisibilityConfig,
    enableSorting,
    enableFiltering,
    enableRowSelection,
    enableColumnResizing,
    enableVirtualScrolling,
    virtualScrollConfig,
    exportConfig,
    formIntegration,
    bulkActions,
    pageSize,
    height,
    border,
    onRowClick,
    onSelectionChange,
    onDataChange,
    _callClientAction,
    loading,
    error,
    loadingConfig,
    errorConfig,
    debounceMs,
    _store,
    storeKeyName,
    updatedCheckValueKey,
    storeRefColumnName,
    showColumnVisibility,
    debug,
  }}`;
  const { optimizedData, isOptimized } = usePerformanceOptimization(data, true);
  const tableData = isOptimized ? optimizedData : data;
  log`Optimized data: ${optimizedData}`;

  // Column visibility management
  const { columnVisibility, setColumnVisibility } = useColumnVisibility(
    data,
    columnConfig,
    columnVisibilityConfig,
    displayColumns
  );
  log`Column visibility: ${columnVisibility}`;

  // Column generation with visibility awareness
  const { columns } = useColumnGeneration(
    tableData,
    columnConfig,
    displayColumns,
    columnVisibilityConfig,
    enableSorting,
    enableFiltering,
    enableColumnResizing,
    _callClientAction,
    _store,
    storeKeyName,
    updatedCheckValueKey,
    storeRefColumnName
  );
  log`Columns: ${columns}`;

  // Table configuration and state management
  const { table, handleGlobalFilterChange } = useCompleteTableConfiguration(
    tableData,
    columns,
    columnVisibility,
    setColumnVisibility,
    pageSize,
    {
      enableSorting,
      enableFiltering,
      enableRowSelection,
      enableColumnResizing,
      debounceMs,
    }
  );
  log`Table state: ${table.getState()}`;

  // Export functionality
  const { handleExport } = useExportFunctionality(
    table,
    columnConfig,
    exportConfig
  );

  // Handle selection changes
  const rowSelection = table.getState().rowSelection;
  useEffect(() => {
    if (onSelectionChange && enableRowSelection) {
      const selectedRows = table
        .getSelectedRowModel()
        .rows.map((row) => row.original);
      const selectedIndices = table
        .getSelectedRowModel()
        .rows.map((row) => row.index);
      onSelectionChange(selectedRows, selectedIndices);
    }
  }, [rowSelection, onSelectionChange, enableRowSelection, table]);

  // Form integration
  const { initialFormData, handleFormDataChange, shouldRenderFormProvider } =
    useFormIntegration(tableData, formIntegration, onDataChange);

  // Virtual scrolling
  const containerHeight = useContainerHeight(height);
  const shouldUseVirtualScrolling = useShouldUseVirtualScrolling(
    enableVirtualScrolling,
    virtualScrollConfig,
    tableData.length
  );

  const virtualScrolling = useVirtualScrolling({
    items: table.getRowModel().rows,
    itemHeight: virtualScrollConfig.itemHeight || 50,
    containerHeight,
    overscan: 5,
  });

  // Loading state
  if (loading && loadingConfig.enabled) {
    return (
      <DynamicTableContainer>
        <LoadingState message={loadingConfig.message} />
      </DynamicTableContainer>
    );
  }

  // Error state
  if (error && errorConfig.enabled) {
    return (
      <DynamicTableContainer>
        <ErrorState
          title={errorConfig.title}
          message={error}
          onRetry={errorConfig.onRetry}
          showRetryButton={errorConfig.showRetryButton}
        />
      </DynamicTableContainer>
    );
  }

  // Main table content
  const tableContent = (
    <DynamicTableContainer>
      {/* Table Controls */}
      <TableControlsContainer>
        <TableControlsBar
          table={table}
          enableFiltering={enableFiltering}
          exportConfig={exportConfig}
          columnConfig={columnConfig}
          onGlobalFilterChange={handleGlobalFilterChange}
          onExport={handleExport}
          showColumnVisibility={showColumnVisibility}
          bulkActions={bulkActions}
          enableRowSelection={enableRowSelection}
        />
      </TableControlsContainer>

      {/* Table */}
      <TableWrapper border={border}>
        {shouldUseVirtualScrolling ? (
          <VirtualScrollWrapper
            height={containerHeight}
            totalHeight={virtualScrolling.totalHeight}
            offsetY={virtualScrolling.offsetY}
            viewportHeight={
              virtualScrolling.visibleItems.length *
              (virtualScrollConfig.itemHeight || 50)
            }
            onScroll={virtualScrolling.handleScroll}
          >
            <StyledTable>
              <TableHeader
                table={table}
                enableFiltering={enableFiltering}
                enableRowSelection={enableRowSelection}
                enableColumnResizing={enableColumnResizing}
              />
              <TableBody
                table={table}
                enableRowSelection={enableRowSelection}
                onRowClick={onRowClick}
              />
            </StyledTable>
          </VirtualScrollWrapper>
        ) : (
          <StyledTable>
            <TableHeader
              table={table}
              enableFiltering={enableFiltering}
              enableRowSelection={enableRowSelection}
              enableColumnResizing={enableColumnResizing}
            />
            <TableBody
              table={table}
              enableRowSelection={enableRowSelection}
              onRowClick={onRowClick}
            />
          </StyledTable>
        )}
      </TableWrapper>

      {/* Pagination */}
      {enablePagination && <TablePagination table={table} />}
    </DynamicTableContainer>
  );

  // Wrap with form provider if needed
  if (shouldRenderFormProvider) {
    return (
      <TableFormProvider
        initialData={initialFormData}
        onDataChange={handleFormDataChange}
      >
        {tableContent}
      </TableFormProvider>
    );
  }

  return tableContent;
};

// Export default for backward compatibility
export default DynamicTable;

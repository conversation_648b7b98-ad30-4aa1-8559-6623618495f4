import React from 'react';
import UploadDocuments from './UploadReportContextMenu';
import { Meta, StoryObj } from '@storybook/react';

const meta: Meta<typeof UploadDocuments> = {
  title: 'Fragments/UploadReportContextMenu',
  component: UploadDocuments,
};
export default meta;

type Story = StoryObj<typeof UploadDocuments>;

export const Default: Story = {
  args: {
    canUploadInvoice: async () => true,
  },
};

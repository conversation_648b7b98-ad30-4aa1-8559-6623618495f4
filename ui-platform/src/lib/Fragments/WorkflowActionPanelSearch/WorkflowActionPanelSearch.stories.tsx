import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { WorkflowActionPanelSearch, WorkflowActionPanelSearchRef } from './WorkflowActionPanelSearch';
import styled from 'styled-components';

const ActionPanelContainer = styled.div`
  display: flex;
  flex-direction: column;
  padding: 20px;
  background: #1a1a1a;
  border-radius: 10px;
  max-width: 400px;
`;

const meta: Meta<typeof WorkflowActionPanelSearch> = {
  title: 'Fragments/WorkflowActionPanelSearch',
  component: WorkflowActionPanelSearch,
  parameters: {
    layout: 'padded',
  },
};

export default meta;
type Story = StoryObj<typeof WorkflowActionPanelSearch>;

// Basic story showing just the search component
export const Default: Story = {
  render: (args) => (
    <ActionPanelContainer>
      <WorkflowActionPanelSearch {...args} />
    </ActionPanelContainer>
  ),
  args: {
    searchUrl: 'https://api.example.com/search',
    token: 'your-auth-token',
    tokenPrefix: 'Bearer',
  },
};

// Story demonstrating external search trigger functionality
export const WithExternalTrigger: Story = {
  render: (args) => {
    const searchRef = React.useRef<WorkflowActionPanelSearchRef>(null);

    const handleSearchTrigger = (searchTerm: string) => {
      console.log('Search triggered from external component:', searchTerm);
    };

    const triggerExampleSearch = () => {
      if (searchRef.current) {
        searchRef.current.performSearch('CLM-12345');
      }
    };

    return (
      <ActionPanelContainer>
        <WorkflowActionPanelSearch
          ref={searchRef}
          {...args}
          onSearchTrigger={handleSearchTrigger}
        />
        <div style={{ marginTop: '20px', textAlign: 'center' }}>
          <button 
            onClick={triggerExampleSearch}
            style={{
              padding: '8px 16px',
              background: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Trigger Example Search (CLM-12345)
          </button>
          <p style={{ color: '#666', fontSize: '12px', marginTop: '8px' }}>
            This demonstrates how external components can trigger searches
          </p>
        </div>
      </ActionPanelContainer>
    );
  },
  args: {
    searchUrl: 'https://api.example.com/search',
    token: 'your-auth-token',
    tokenPrefix: 'Bearer',
  },
};

// Story showing the component with different states
export const WithClosedClaimsSwitch: Story = {
  render: (args) => (
    <ActionPanelContainer>
      <WorkflowActionPanelSearch {...args} />
    </ActionPanelContainer>
  ),
  args: {
    searchUrl: 'https://api.example.com/search',
    token: 'your-auth-token',
    tokenPrefix: 'Bearer',
  },
  parameters: {
    docs: {
      description: {
        story: 'The search component includes a switch to include/exclude closed claims in search results.',
      },
    },
  },
};



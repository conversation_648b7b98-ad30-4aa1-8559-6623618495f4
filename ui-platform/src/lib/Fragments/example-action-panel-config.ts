import { ActionPanelConfig } from '../Engine/models/action-panel.config';

/**
 * Example action panel configuration that demonstrates how to use
 * WorkflowActionPanelSearch and ActionPanelRecentActivity together.
 * 
 * This configuration creates an action panel with both search functionality
 * and recent activity list, where clicking on recent activity items
 * automatically triggers searches in the search panel.
 */
export const exampleActionPanelConfig: ActionPanelConfig[] = [
  {
    icon: 'search-sm',
    title: 'Search & Recent Activity',
    layout: {},
    onEnter: [],
    onLeave: [],
    fragments: [
      // Search Panel Fragment
      {
        component: 'WorkflowActionPanelSearch',
        props: {
          searchUrl: '{VITE_API_BASE_URL}/api/v1/search/claims',
          token: '{auth.token}',
          tokenPrefix: 'Bearer',
        },
        layout: {
          marginBottom: '20px',
        },
      },
      // Recent Activity Fragment
      {
        component: 'ActionPanelRecentActivity',
        props: {
          userID: '{auth.userID}',
          showClearButton: true,
        },
        layout: {
          marginTop: '10px',
        },
      },
    ],
    actionLevel: 'bottomControls',
  },
];

/**
 * Alternative configuration with separate action panels
 * for search and recent activity
 */
export const separateActionPanelsConfig: ActionPanelConfig[] = [
  {
    icon: 'search-sm',
    title: 'Search Claims',
    layout: {},
    onEnter: [],
    onLeave: [],
    fragments: [
      {
        component: 'WorkflowActionPanelSearch',
        props: {
          searchUrl: '{VITE_API_BASE_URL}/api/v1/search/claims',
          token: '{auth.token}',
          tokenPrefix: 'Bearer',
        },
        layout: {},
      },
    ],
    actionLevel: 'topControls',
  },
  {
    icon: 'alarm-clock',
    title: 'Recent Activity',
    layout: {},
    onEnter: [],
    onLeave: [],
    fragments: [
      {
        component: 'ActionPanelRecentActivity',
        props: {
          userID: '{auth.userID}',
          showClearButton: true,
        },
        layout: {},
      },
    ],
    actionLevel: 'bottomControls',
  },
];

/**
 * Usage Instructions:
 * 
 * 1. Add one of these configurations to your workflow or state config
 * 2. Ensure both components are included in your componentMap
 * 3. The search trigger functionality will work automatically when both
 *    components are present in the same action panel or different panels
 * 
 * Example componentMap:
 * ```typescript
 * const componentMap = {
 *   WorkflowActionPanelSearch: WorkflowActionPanelSearch,
 *   ActionPanelRecentActivity: ActionPanelRecentActivity,
 * };
 * ```
 * 
 * When a user clicks on a recent activity item, it will automatically
 * trigger a search in the WorkflowActionPanelSearch component using
 * the claim number as the search term.
 */ 
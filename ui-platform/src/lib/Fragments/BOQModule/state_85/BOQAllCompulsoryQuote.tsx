import React from 'react';

import { Item } from '../../../Components/BOQ/LineItemModify/LineItemModify';
import {
  BOQItemsResponse,
  getAllBOQItemsAsCompulsoryItems,
  getAllBOQItemsAsOptionalItems,
  JobFeeOptions,
} from '../../../Engine/helpers/BOQ-utils';
import {
  BOQComplete,
  PreselectedOptionalItem,
  PreselectedCompulsoryItem,
} from '../../BOQComplete/BOQComplete';

interface BOQAllCompulsoryQuoteProps {
  boqItems: BOQItemsResponse;
  travelDistance?: number;
  excessAmount?: number;
  jobFeeOptions?: JobFeeOptions;
  vat?: number;
  initialValue?: string;
  invoiceHeading?: string;
  preselectedCompulsoryItem?: PreselectedCompulsoryItem;
  _formContext?: any;
}

const handleItemsChange = (updatedItems: Item[]) => {
  console.log('Updated items:', updatedItems);
};

export function BOQAllCompulsoryQuote({
  boqItems = { items: [] },
  travelDistance,
  excessAmount,
  jobFeeOptions,
  vat,
  initialValue,
  invoiceHeading,
  preselectedCompulsoryItem,
  _formContext,
}: BOQAllCompulsoryQuoteProps) {
  // const compulsoryItems = boqItems.items.map(item => ({
  //     ...item,
  //     description: item.name,
  //   }));
  return (
    <BOQComplete
      travelDistance={travelDistance}
      excessAmount={excessAmount}
      jobFeeOptions={jobFeeOptions}
      vat={vat}
      initialValue={initialValue}
      invoiceHeading={invoiceHeading}
      preselectedCompulsoryItem={preselectedCompulsoryItem}
      lineItemsTableProps={{
        compulsoryItems: [],
        optionalItems: getAllBOQItemsAsOptionalItems(boqItems),
        columnNames: {
          description: 'Description',
          quantity: 'Quantity',
          unitPrice: 'Unit Price',
          total: 'Total',
        },
        onItemsChange: handleItemsChange,
      }}
      invoiceSummaryNotesProps={{
        notes: '',
      }}
      invoiceSummaryProps={{
        // vat: {vat |},
        subTotal: 0,
      }}
    />
  );
}

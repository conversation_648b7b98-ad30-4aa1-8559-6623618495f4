import React from 'react';
import { ComponentPropsWithoutRef } from 'react';
import styled from 'styled-components';
import { JobCardNormalSize } from '../../Components/Cards/JobCardNormalSize/JobCardNormalSize';
import { ScrollableContent } from '../../Components/Scrollbar/Scrollbar';
import { ActionConfig } from '../../Engine/models/action.config';

type JobCardListWithNormalSizeProps = {
  scrollable?: boolean;
  jobs?: ComponentPropsWithoutRef<typeof JobCardNormalSize>['job'][];
  jobCardNumberPrefix?: string;
  LinkRouter: any;
  callClientAction: (config: ActionConfig) => void;
  getMenuItems: (
    job: any
  ) => {
    icon: string;
    label: string;
    path: string;
    onClick?: ActionConfig[];
  }[];
};

const Container = styled.div<
  ComponentPropsWithoutRef<'div'> & { scrollable?: boolean }
>`
  width: ${(props) => (props.scrollable ? 'calc(100% - 1rem)' : '100%')};
  gap: ${(props) => props.theme.GapXs};
  display: grid;
  grid-auto-flow: row;
  height: 100%;
  // flex-direction: column;
  // overflow: hidden;
`;

/**
 * Renders a list of claim cards either in a scrollable container or a regular container based on the scrollable flag.
 * @param {JobCardListProps} items - The list of claim data to display.
 * @param {boolean} scrollable - Flag indicating whether the list should be scrollable.
 * @returns {JSX.Element} The rendered list of claim cards.
 */

export function JobCardListWithNormalSize({
  jobs = [],
  scrollable = false,
  jobCardNumberPrefix = 'MID',
  callClientAction,
  LinkRouter,
  getMenuItems = (job: any) => [],
}: JobCardListWithNormalSizeProps) {
  return (
    <ScrollableContent style={{ height: 'calc(100% - 176px', width: '100%' }}>
      <Container scrollable={scrollable}>
        {jobs?.map((job) => (
          <JobCardNormalSize
            LinkRouter={LinkRouter}
            key={job?.id}
            job={job}
            jobCardNumberPrefix={jobCardNumberPrefix}
            getMenuItems={getMenuItems}
            callClientAction={callClientAction}
          />
        ))}
      </Container>
    </ScrollableContent>
  );
}

import { <PERSON>a, StoryObj } from '@storybook/react';
import { JobCardListWithNormalSize } from './JobCardListWithNormalSize';

const meta: Meta<typeof JobCardListWithNormalSize> = {
  title: 'Fragments/JobCardListWithNormalSize',
  component: JobCardListWithNormalSize,
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof JobCardListWithNormalSize>;

export const Overview: Story = {
  args: {
    scrollable: true,
    // items: makeItemArray({
    //   item: job,
    //   length: 5,
    // }),
    jobs: [
      {
        id: 1,
        skill: 'Plumbing',
        sp: '<PERSON> Team leader',
        date: '2024-04-04',
        time: 'Between 10:00 and 14:00',
        state: 0,
        stateTextDislplay: '0: Job Created',
        description: 'Invoice the allowable fees',
        sla: '2d 01h 37m',
        claim: {
          applicant: {
            first_name: '<PERSON><PERSON>',
            surname: '<PERSON><PERSON><PERSON><PERSON>'
          }
        }
      },
      {
        id: 2,
        skill: 'Roofing',
        sp: '<PERSON> Team leader',
        date: '2024-04-04',
        time: 'Between 10:00 and 14:00',
        state: 6,
        stateTextDislplay: '0: Job Created',
        description: 'Appoint a team leader',
        sla: '2d 01h 37m',
        customer: '<PERSON> Mc<PERSON>',
        claim: {
          applicant: {
            first_name: 'Warrant',
            surname: 'Shondlani'
          }
        }
      },
      {
        id: 3,
        skill: 'Roofing',
        sp: 'Vaughn Botha Team leader',
        date: '2024-04-04',
        time: 'Between 10:00 and 14:00',
        state: 5,
        stateTextDislplay: '0: Job Created',
        description: 'Invoice for a job',
        sla: '2d 01h 37m',
        customer: 'Sifiso Mhlangu',
        claim: {
          applicant: {
            first_name: 'Warrant',
            surname: 'Shondlani'
          }
        }
      },
      {
        skill: 'Glass and Aluminium',
        sp: 'Vaughn Botha Team leader',
        date: '2024-04-04',
        time: 'Between 10:00 and 14:00',
        state: 3,
        stateTextDislplay: '0: Job Created',
        description: 'Invoice the allowable fees',
        sla: '2d 01h 37m',
        customer: 'Poppie Ferreira',
        claim: {
          applicant: {
            first_name: 'Warrant',
            surname: 'Shondlani'
          }
        }
      },
      {
        skill: 'Carpeting',
        sp: 'Vaughn Botha Team leader',
        date: '2024-04-04',
        time: 'Between 10:00 and 14:00',
        state: 7,
        stateTextDislplay: '0: Job Created',
        description: 'Invoice the allowable fees',
        sla: '2d 01h 37m',
        customer: 'Joseph Mutua',
        claim: {
          applicant: {
            first_name: 'Warrant',
            surname: 'Shondlani'
          }
        }
      },
      {
        skill: 'Roofing',
        sp: 'Vaughn Botha Team leader',
        date: '2024-04-04',
        time: 'Between 10:00 and 14:00',
        state: 6,
        stateTextDislplay: '0: Job Created',
        description: 'Invoice the allowable fees',
        sla: '2d 01h 37m',
        customer: 'Xitshembiso Warrant Shondlani',
        claim: {
          applicant: {
            first_name: 'Warrant',
            surname: 'Shondlani'
          }
        }
      },
      {
        skill: 'Roofing',
        sp: 'Vaughn Botha Team leader',
        date: '2024-04-04',
        time: 'Between 10:00 and 14:00',
        state: 5,
        stateTextDislplay: '0: Job Created',
        description: 'Invoice the allowable fees',
        sla: '2d 01h 37m',
        customer: 'Xitshembiso Warrant Shondlani',
        claim: {
          applicant: {
            first_name: 'Warrant',
            surname: 'Shondlani'
          }
        }
      },
    ],
    LinkRouter: ({ children }) => children,
  },
};
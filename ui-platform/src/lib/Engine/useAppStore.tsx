'use client';
import { create, StoreApi, UseBoundStore } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export interface AppState {
  filtersData: any[];
  auth: any;
  setState: (fn: (state: AppState) => Partial<AppState>) => void;
  reset: (exemptionKeys?: string[], options?: { softReset?: boolean }) => void;
  setDynamicState: (key: string, value: any) => void;
  removeDynamicState: (key: string) => void;
}

let store: UseBoundStore<StoreApi<AppState>> | null = null;
const initialState = {
  filtersData: [],
  auth: {},
};

const createDefaultStore = () =>
  create<AppState>()(
    devtools(
      // persist(
        (set, get) => ({
          ...initialState,
          setState: (fn) => set(fn),
          reset: (
            exemptKeys: string[] = [],
            options: { softReset?: boolean } = { softReset: true }
          ) => {
            const currentState = get();
            let persist: string[] = [];
            const defaultKeys = Object.keys(initialState);
            const currentKeys = Object.keys(currentState);
            const actionKeys = [
              'setState',
              'reset',
              'setDynamicState',
              'removeDynamicState',
              'env',
            ];
            if (!options.softReset && exemptKeys.length === 0) {
              persist = defaultKeys;
            } else {
              persist = [...defaultKeys, ...actionKeys, ...(exemptKeys || [])];
            }
            const newState: any = { ...initialState };
            currentKeys.forEach((key) => {
              if (persist.includes(key)) {
                newState[key] = currentState[key as keyof AppState];
              } else {
                newState[key] = undefined;
              }
            });
            set(newState);
            // localStorage.removeItem('4sure-ui-platform-store');
          },
          setDynamicState: (key: string, value: any) => set({ [key]: value }),
          removeDynamicState: (key: string) => set({ [key]: undefined }),
        }),
      //   {
      //     name: '4sure-ui-platform-store',
      //   }
      // ),
      {
        name: '4sure-ui-platform-store',
        enabled: process.env.NODE_ENV === 'development',
      }
    )
  );

export const setAppStore = (customStore: UseBoundStore<StoreApi<AppState>>) => {
  store = customStore;
  return store;
};

export const getAppStore = () => {
  if (!store) {
    store = createDefaultStore();
  }
  return store;
};

export const useAppStore = (() => getAppStore())();
export const useResetStore = () => useAppStore((state) => state.reset);
export const useSetDynamicState = () =>
  useAppStore((state) => state.setDynamicState);
export const useRemoveDynamicState = () =>
  useAppStore((state) => state.removeDynamicState);
export const useSetState = () => useAppStore((state) => state.setState);

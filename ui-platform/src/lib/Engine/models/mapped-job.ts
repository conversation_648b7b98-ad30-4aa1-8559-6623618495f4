export interface MappedJob {
  id: number;
  appointment?: {
    id?: number | null;
    job?: number | null;
    state?: number | null;
    range_start?: string | null;
    range_end?: string | null;
    appointment_type?: number | null;
    reason?: string | null;
  } | null;
  note_count?: number | null;
  unread_note_count?: number | null;
  claim_type_id?: number | null;
  assessor_name?: string | null;
  lat?: string | null;
  long?: string | null;
  claim?: {
    id?: number | null;
    mid?: string | null;
    is_cat?: boolean | null;
    cat_code?: string | null;
    note_count?: number | null;
    applicant?: {
      first_name?: string | null;
      surname?: string | null;
    } | null;
    property_city?: string | null;
    property_complex?: string | null;
    property_complex_block?: string | null;
    property_complex_unit_number?: string | null;
    property_street_name?: string | null;
    property_street_number?: string | null;
    property_suburb?: string | null;
  } | null;
  source?: string | null;
  source_id?: number | null;
  source_key?: string | null;
  suburb?: string | null;
  address?: string | null;
  postal_code?: string | null;
  claim_value?: string | null;
  mid?: string | null;
  ping_count?: number | null;
  token?: string | null;
  valid_job?: number | null;
  updated?: string | null;
  on_site?: boolean | null;
  distance?: string | null;
  job_creator?: number | null;
  skill?: number | null;
  sp?: number | null;
  team_leader?: number | null;
  area?: number | null;
  state?: number | null;
  supplier_type?: number | null;
  forced_location?: number | null;
  assessor?: number | null;
  authorizer?: number | null;
  location?: number | null;

  // CUSTOM FRONTEND PROPS
  stateTextDisplay?: string | null;
  customer?: string | null;
  permissionGranted?: boolean | null;
  formattedDate?: string | null;
  skillName?: string | null;
  appointmentType?: string | null;
  appointmentTime?: string | null;
  teamleaderName?: string | null;
  spName?: string | null;

  // other
  notesLink?: string | null;
}

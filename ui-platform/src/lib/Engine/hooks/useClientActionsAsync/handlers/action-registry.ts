import { ActionContext, ActionRegistry } from '../types';
import { 
  handleNavigate, 
  handleLog, 
  handleLogout, 
  handleDownloadPdfFromUrl, 
  handleOpenPdfFromUrl, 
  handleClearErrors, 
  handleViewJobCardPDF, 
  handleDefaultModalAction 
} from './simple-actions';
import { handleTriggerModal, handleCloseModal } from './modal-actions';
import { handleClearStore, handleUpdateStore } from './store-actions';
import { handleResetFields } from './form-actions';

/**
 * Creates the action registry for simple actions that don't need special execution logic
 */
export const createActionRegistry = (context: ActionContext): ActionRegistry => ({
  navigate: handleNavigate,
  downloadPdfFromUrl: handleDownloadPdfFromUrl,
  openPdfFromUrl: handleOpenPdfFromUrl,
  log: handleLog,
  logout: handleLogout,
  triggerModal: handleTriggerModal,
  closeModal: handleCloseModal,
  defaultModalAction: handleDefaultModalAction,
  clearErrors: handleClearErrors,
  viewJobCardPDF: handleViewJobCardPDF,
  clearStore: handleClearStore,
  updateStore: handleUpdateStore,
  resetFields: handleResetFields,
});

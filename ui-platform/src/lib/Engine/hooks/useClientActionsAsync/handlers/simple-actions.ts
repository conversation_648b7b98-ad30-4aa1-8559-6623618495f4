import { checkIsOnline } from '../../../../Utilities/checkNetworkOnline';
import { downloadPdfFromUrl } from '../../../../Utilities/downloadPdfFromUrl';
import { openPdfFromUrl } from '../../../../Utilities/openPdfFromUrl';
import { openPdfUrl } from '../../../helpers/pdf-handler';
import { ActionConfig, ActionContext } from '../types';

/**
 * Navigate to a different route
 */
export const handleNavigate = async (config: ActionConfig, context: ActionContext) => {
  if (checkIsOnline(() => context.offlineHandler()) === false) return;

  const currentParams = new URLSearchParams(context.location.search);
  const options =
    config?.payload[1] && Object.keys(config?.payload[1]).length !== 0
      ? Object.keys(config?.payload[1] as Record<string, any>).reduce(
          (acc, key) => {
            if (key !== 'clearParams') {
              if (config?.payload[1]) {
                acc[key] =
                  config.payload[1][key] !== undefined
                    ? config.payload[1][key]
                    : undefined;
              }
            }
            return acc;
          },
          {} as Record<string, any>
        )
      : undefined;

  setTimeout(
    () =>
      context.navigate(
        {
          pathname: config.payload[0],
          search: config.payload[1]?.clearParams
            ? ''
            : currentParams.toString(),
        },
        options
      ),
    config.debounce || 0
  );
};

/**
 * Log values to console
 */
export const handleLog = (config: ActionConfig, context: ActionContext) => {
  console.log(...config.payload);
};

/**
 * Logout user via Keycloak
 */
export const handleLogout = async (config: ActionConfig, context: ActionContext) => {
  if (checkIsOnline(() => context.offlineHandler()) === false) return;
  setTimeout(() => context.keycloak?.logout(), config.debounce || 0);
};

/**
 * Download PDF files from URLs
 */
export const handleDownloadPdfFromUrl = async (
  config: ActionConfig,
  context: ActionContext
) => {
  setTimeout(() => {
    for (const fileOptions of config.payload) {
      downloadPdfFromUrl(fileOptions.url, fileOptions.filename);
    }
  }, config.debounce || 1); // default to 1ms debounce to prevent potential rerender issues, configurable
};

/**
 * Opens a PDF from a URL in a new Window.
 * @param config - ActionConfig object containing the payload.
 * @param context - ActionContext object containing the context.
 * @returns Promise that resolves when the PDF has been opened.
 */
export const handleOpenPdfFromUrl = async (
  config: ActionConfig,
  context: ActionContext
) => {
  // Debounce the function to prevent multiple calls from conflicting with each other
  setTimeout(() => {
    // Loop through each file in the payload and open it
    for (const file of config.payload) {
      // If the file is a string, use it as the URL
      if (typeof file === 'string') openPdfFromUrl(file);
      // If the file is an object, use the URL from the object
      else openPdfFromUrl(file.url);
    }
  }, config.debounce || 1); // default to 1ms debounce to prevent potential rerender issues, configurable
};

/**
 * Clear errors from error store
 */
export const handleClearErrors = (config: ActionConfig, context: ActionContext) => {
  if (!config.payload) {
    context.clearAllErrors();
    return;
  }
  context.clearError(config.payload);
};

/**
 * View job card PDF
 */
export const handleViewJobCardPDF = (config: ActionConfig, context: ActionContext) => {
  const payload = config.payload;
  openPdfUrl(
    context.keycloak?.token as string,
    payload?.jobId,
    payload?.apiBaseUrl,
    payload?.pdfUrl
  );
};

/**
 * Handle default modal actions
 */
export const handleDefaultModalAction = (
  config: ActionConfig,
  context: ActionContext
) => {
  if (typeof config.payload === 'function') {
    config.payload();
  }
};

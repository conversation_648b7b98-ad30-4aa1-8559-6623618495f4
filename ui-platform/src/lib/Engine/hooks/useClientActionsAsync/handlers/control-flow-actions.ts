import { evalStringExpression, evaluateFormConditionExpression } from '../../../helpers';
import { ActionConfig, ActionContext, SwitchCase } from '../types';
import { evaluateExpression } from '../utils/expression-evaluation';

/**
 * Handle conditional actions
 */
export const handleConditional = async (
  config: ActionConfig,
  context: ActionContext,
  executeClientAction?: (config: ActionConfig) => Promise<void>
) => {
  if (!executeClientAction) return;

  const executeConditionalActions = async (
    condition: string | boolean,
    actions: { whenTrue: ActionConfig[]; whenFalse?: ActionConfig[] }
  ) => {
    const callConditionalActions = async (
      conditionResult: boolean,
      whenTrue: ActionConfig[],
      whenFalse?: ActionConfig[]
    ) => {
      if (conditionResult) {
        for (const cf of whenTrue || []) {
          await executeClientAction(cf);
        }
      } else {
        if (!whenFalse || whenFalse.length === 0) return;
        for (const cf of whenFalse || []) {
          await executeClientAction(cf);
        }
      }
    };

    if (context.formContext) {
      const conditionResult =
        typeof condition === 'string'
          ? evaluateFormConditionExpression(
              condition,
              context.getStoreState(),
              context.formContext.watch(),
              context.formContext.formState
            )
          : condition;
      await callConditionalActions(
        conditionResult,
        actions.whenTrue,
        actions.whenFalse
      );
    } else {
      const conditionResult =
        typeof condition === 'string'
          ? evaluateFormConditionExpression(condition, context.getStoreState())
          : condition;
      await callConditionalActions(
        conditionResult,
        actions.whenTrue,
        actions.whenFalse
      );
    }
  };

  await executeConditionalActions(
    config.payload.condition,
    config.payload.actions
  );
};

/**
 * Handle timeout actions
 */
export const handleTimeout = async (
  config: ActionConfig,
  context: ActionContext,
  executeClientAction?: (config: ActionConfig) => Promise<void>
) => {
  if (!executeClientAction) return;

  const timeout = config.payload[0];
  const actions = config.payload[1];

  return new Promise<void>((resolve) => {
    setTimeout(async () => {
      for (const cf of actions || []) {
        await executeClientAction(cf);
      }
      resolve();
    }, timeout);
  });
};

/**
 * Handle switch actions
 */
export const handleSwitch = async (
  config: ActionConfig,
  context: ActionContext,
  executeClientAction?: (config: ActionConfig) => Promise<void>
) => {
  if (!executeClientAction) return;

  const { value, pathToValue, cases } = config.payload;
  const { param } = config;

  let valueToCompare;
  if (param) {
    valueToCompare =
      typeof param === 'object'
        ? evalStringExpression(`#{${pathToValue}}`, param)
        : param;
  } else if (pathToValue) {
    valueToCompare = evalStringExpression(
      `#{${pathToValue}}`,
      context.getStoreState()
    );
  } else {
    valueToCompare = value || 'default';
  }
  
  const matchingCase = (cases as SwitchCase[]).find((item) => {
    if (typeof item.caseName !== 'string') {
      return (
        String(item?.caseName).toLowerCase() ===
        String(valueToCompare).toLowerCase()
      );
    }
    return (
      item.caseName?.toLowerCase() === String(valueToCompare)?.toLowerCase() ||
      item.caseName === 'default'
    );
  });
  
  if (matchingCase && matchingCase.actions) {
    for (const action of matchingCase.actions) {
      await executeClientAction(action);
    }
  }
};

/**
 * Handle forEach actions
 */
export const handleForEach = async (
  config: ActionConfig,
  context: ActionContext,
  executeClientAction?: (config: ActionConfig, dbg?: boolean) => Promise<void>
) => {
  if (!executeClientAction) return;

  const { value, pathToValue, actions } = config.payload;
  const { param, debug } = config;

  let values;
  if (param) {
    values = Array.isArray(param) ? param : [param];
  } else if (pathToValue) {
    values = pathToValue.map((itm: string) => {
      if (typeof itm !== 'string') return itm;
      return evalStringExpression(`#{${itm}}`, context.getStoreState());
    });
  } else {
    values = value || [];
  }

  const batchActions = [];
  for (const val of values) {
    for (const action of actions) {
      const param = typeof val !== 'object' ? { val } : val;
      // Create a copy of the action and evaluate expressions with the current param
      const actionCopy = { ...action };

      // Evaluate expressions in the payload with the current param context
      if (actionCopy.payload) {
        actionCopy.payload = evaluateExpression(
          actionCopy.payload, 
          param, 
          context.getStoreState,
          undefined,
          { debug }
        );
      }

      // Set the param for the action
      actionCopy.param = param;
      batchActions.push(actionCopy);
    }
  }
  
  for (const action of batchActions) {
    await executeClientAction(action, debug || context.debug);
  }
};

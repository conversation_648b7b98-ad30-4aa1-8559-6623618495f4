import { ActionConfig, ActionContext } from '../types';
import { createStateSynchronizer } from '../context/state-synchronizer';

/**
 * Clear store values - with immediate state synchronization
 */
export const handleClearStore = (config: ActionConfig, context: ActionContext) => {
  const stateSynchronizer = createStateSynchronizer();
  
  if (config.payload.length === 0) {
    stateSynchronizer.clearState();
    return;
  }
  
  stateSynchronizer.clearState(config.payload);
};

/**
 * Update store values - with immediate state synchronization
 * This fixes the stale state issue by removing setTimeout delays
 */
export const handleUpdateStore = (
  config: ActionConfig,
  context: ActionContext
) => {
  if (config.payload.length === 0) {
    return;
  }

  const stateSynchronizer = createStateSynchronizer();
  
  // Process payload and update state immediately
  const updates = config.payload.reduce(
    (acc: any, curr: string) => ({
      ...acc,
      ...Object.keys(curr).reduce(
        (acc2, key: string) => ({
          ...acc2,
          [key]: (curr as unknown as Record<string, any>)[key],
        }),
        {}
      ),
    }),
    {}
  );

  // Apply debounce if specified, but still use immediate state updates
  if (config.debounce && config.debounce > 0) {
    setTimeout(() => {
      stateSynchronizer.updateState(updates);
    }, config.debounce);
  } else {
    // Immediate update for state synchronization
    stateSynchronizer.updateState(updates);
  }
};

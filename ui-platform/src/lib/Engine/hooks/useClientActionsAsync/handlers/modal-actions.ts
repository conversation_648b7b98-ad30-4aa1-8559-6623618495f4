import { ActionConfig, ActionContext } from '../types';

/**
 * Trigger modal display
 * Debounce triggerModal to avoid multiple rerenders as a result of modal state conflicting with other state
 * will make debounce duration configurable from config though it should be as short as possible for ux
 */
const debouncedHandleTriggerModal = async (
  config: ActionConfig,
  context: ActionContext
) =>
  setTimeout(() => {
    context.setModalState({ ...config.payload[0], display: true });
  }, config.debounce || 1); // default to 1ms debounce to prevent potential rerender issues, configurable

export const handleTriggerModal = async (
  config: ActionConfig,
  context: ActionContext
) => {
  await debouncedHandleTriggerModal(config, context);
};

/**
 * Close modal
 */
export const handleCloseModal = async (
  config: ActionConfig,
  context: ActionContext
) => {
  setTimeout(
    () => context.setModalState({ display: false }),
    config.debounce || 0
  );
};

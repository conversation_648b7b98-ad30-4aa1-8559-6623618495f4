import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ActionConfig } from '../types';
import { evaluateExpression } from '../utils/expression-evaluation';
import { validateConfig } from '../utils/validation';
import { createActionRegistry } from '../handlers/action-registry';

// Mock context for testing
const mockContext = {
  navigate: vi.fn(),
  location: { pathname: '/test', search: '' },
  keycloak: { token: 'test-token' },
  fetcher: undefined,
  submit: undefined,
  formContext: undefined,
  envObject: undefined,
  tokenPrefix: 'Bearer' as const,
  setState: vi.fn(),
  setModalState: vi.fn(),
  setAsyncLoading: vi.fn(),
  addError: vi.fn(),
  clearError: vi.fn(),
  clearAllErrors: vi.fn(),
  fc: undefined,
  offlineHandler: vi.fn(),
  debug: false,
  getStoreState: vi.fn(() => ({ user: { name: '<PERSON>' }, counter: 5 })),
};

describe('Refactored useClientActionsAsync - Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Expression Evaluation', () => {
    it('should evaluate @param expressions correctly', () => {
      const payload = {
        message: '@param:{name}',
        id: '@param:{id}',
      };
      const param = { name: 'Alice', id: 123 };

      const result = evaluateExpression(
        payload,
        param,
        mockContext.getStoreState
      );

      expect(result).toEqual({
        message: 'Alice',
        id: 123,
      });
    });

    it('should handle nested @param expressions', () => {
      const payload = {
        user: {
          name: '@param:{user.name}',
          details: {
            age: '@param:{user.age}',
          },
        },
      };
      const param = { user: { name: 'Bob', age: 30 } };

      const result = evaluateExpression(
        payload,
        param,
        mockContext.getStoreState
      );

      expect(result).toEqual({
        user: {
          name: 'Bob',
          details: {
            age: 30,
          },
        },
      });
    });

    it('should handle arrays with @param expressions', () => {
      const payload = ['@param:{name}', 'static', '@param:{id}'];
      const param = { name: 'Charlie', id: 456 };

      const result = evaluateExpression(
        payload,
        param,
        mockContext.getStoreState
      );

      expect(result).toEqual(['Charlie', 'static', 456]);
    });
  });

  describe('Config Validation', () => {
    it('should validate correct config', () => {
      const config: ActionConfig = {
        action: 'log',
        payload: ['test message'],
      };

      expect(validateConfig(config, 'test')).toBe(true);
    });

    it('should reject null config', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      expect(validateConfig(null, 'test')).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        'test received null or undefined config, ignoring action'
      );
      
      consoleSpy.mockRestore();
    });

    it('should reject config without action', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      expect(validateConfig({ payload: [] }, 'test')).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        'test received config without action property, ignoring action'
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('Action Registry', () => {
    it('should create action registry with all handlers', () => {
      const registry = createActionRegistry(mockContext);

      expect(registry).toHaveProperty('navigate');
      expect(registry).toHaveProperty('log');
      expect(registry).toHaveProperty('updateStore');
      expect(registry).toHaveProperty('clearStore');
      expect(registry).toHaveProperty('triggerModal');
      expect(registry).toHaveProperty('closeModal');
      expect(typeof registry.log).toBe('function');
    });

    it('should execute log action through registry', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const registry = createActionRegistry(mockContext);

      const config: ActionConfig = {
        action: 'log',
        payload: ['Test message', { data: 'test' }],
      };

      await registry.log(config, mockContext);

      expect(consoleSpy).toHaveBeenCalledWith('Test message', { data: 'test' });
      consoleSpy.mockRestore();
    });
  });

  describe('State Synchronization', () => {
    it('should provide fresh state through getStoreState', () => {
      const freshState = { user: { name: 'Fresh' }, counter: 10 };
      mockContext.getStoreState.mockReturnValue(freshState);

      const result = mockContext.getStoreState();

      expect(result).toEqual(freshState);
      expect(result.user.name).toBe('Fresh');
      expect(result.counter).toBe(10);
    });

    it('should evaluate expressions with fresh state', () => {
      const freshState = { user: { name: 'Updated' }, counter: 15 };
      mockContext.getStoreState.mockReturnValue(freshState);

      const payload = {
        userName: 'js:{user.name}',
        counterValue: 'js:{counter}',
      };

      // Mock the expression evaluation to simulate fresh state access
      const result = evaluateExpression(
        payload,
        undefined,
        mockContext.getStoreState
      );

      // Verify that getStoreState was called (indicating fresh state access)
      expect(mockContext.getStoreState).toHaveBeenCalled();
    });
  });

  describe('Circular Reference Protection', () => {
    it('should handle circular references in payload', () => {
      const circularObj: any = { name: 'test' };
      circularObj.self = circularObj;

      const result = evaluateExpression(
        circularObj,
        undefined,
        mockContext.getStoreState
      );

      // Should return the original object without infinite recursion
      expect(result).toBeDefined();
      expect(result.name).toBe('test');
    });
  });

  describe('Mixed Expression Types', () => {
    it('should handle mixed @param and store expressions', () => {
      const payload = {
        paramValue: '@param:{name}',
        storeValue: 'js:{user.name}',
        staticValue: 'static',
      };
      const param = { name: 'ParamName' };

      const result = evaluateExpression(
        payload,
        param,
        mockContext.getStoreState
      );

      expect(result.paramValue).toBe('ParamName');
      expect(result.staticValue).toBe('static');
      // storeValue would be evaluated by the actual expression evaluator
    });
  });
});

import { act, renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useClientActionsAsync } from '../hooks/useClientActionsAsync';
import { ActionConfig } from '../types';

// Mock dependencies
vi.mock('../../../useAppStore', () => ({
  useAppStore: {
    setState: vi.fn(),
    getState: vi.fn(() => ({ user: { name: 'John' }, counter: 0 })),
  },
}));

vi.mock('../../useModalStore', () => ({
  useModalStore: () => ({
    setModalState: vi.fn(),
  }),
}));

vi.mock('../../useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: () => ({
    setAsyncLoading: vi.fn(),
  }),
}));

vi.mock('../../useErrorStore', () => ({
  useErrorStore: () => ({
    addError: vi.fn(),
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  }),
}));

vi.mock('react-hook-form', () => ({
  useFormContext: () => null,
}));

describe('useClientActionsAsync - Refactored Implementation', () => {
  const mockNavigate = vi.fn();
  const mockLocation = { pathname: '/test', search: '' };
  const mockKeycloak = { token: 'test-token', logout: vi.fn() };

  const defaultProps = {
    navigate: mockNavigate,
    location: mockLocation as any,
    keycloak: mockKeycloak as any,
    debug: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Hook Functionality', () => {
    it('should return all expected methods', () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));

      expect(result.current).toHaveProperty('callClientAction');
      expect(result.current).toHaveProperty('callClientActionAsync');
      expect(result.current).toHaveProperty('callClientActionsSequentially');
      expect(result.current).toHaveProperty('callClientActionsConcurrently');
      expect(result.current).toHaveProperty('callClientActionsWithLimit');
      expect(result.current).toHaveProperty(
        'executeClientActionAsynchronously'
      );
      expect(result.current).toHaveProperty('executeClientActionSynchronously');
      expect(result.current).toHaveProperty('withAsync');
      expect(result.current).toHaveProperty('createBatch');
    });
  });

  describe('Simple Actions', () => {
    it('should execute log action', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const logConfig: ActionConfig = {
        action: 'log',
        payload: ['Test message', { data: 'test' }],
      };

      await act(async () => {
        await result.current.callClientActionAsync(logConfig);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Test message', { data: 'test' });
      consoleSpy.mockRestore();
    });

    it('should execute conditional action with true condition', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const conditionalConfig: ActionConfig = {
        action: 'conditional',
        payload: {
          condition: true,
          actions: {
            whenTrue: [{ action: 'log', payload: ['Condition was true'] }],
            whenFalse: [{ action: 'log', payload: ['Condition was false'] }],
          },
        },
      };

      await act(async () => {
        await result.current.callClientActionAsync(conditionalConfig);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Condition was true');
      expect(consoleSpy).not.toHaveBeenCalledWith('Condition was false');
      consoleSpy.mockRestore();
    });
  });

  describe('Expression Evaluation', () => {
    it('should evaluate @param expressions', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const forEachConfig: ActionConfig = {
        action: 'forEach',
        payload: {
          value: [{ name: 'Alice' }, { name: 'Bob' }],
          actions: [
            {
              action: 'log',
              payload: ['User name:', '@param:{name}'],
            },
          ],
        },
      };

      await act(async () => {
        await result.current.callClientActionAsync(forEachConfig);
      });

      expect(consoleSpy).toHaveBeenCalledWith('User name:', 'Alice');
      expect(consoleSpy).toHaveBeenCalledWith('User name:', 'Bob');
      consoleSpy.mockRestore();
    });
  });

  describe('Concurrent Execution', () => {
    it('should execute actions concurrently', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const actions: ActionConfig[] = [
        { action: 'log', payload: ['Action 1'] },
        { action: 'log', payload: ['Action 2'] },
        { action: 'log', payload: ['Action 3'] },
      ];

      await act(async () => {
        await result.current.callClientActionsConcurrently(actions);
      });

      expect(consoleSpy).toHaveBeenCalledTimes(3);
      expect(consoleSpy).toHaveBeenCalledWith('Action 1');
      expect(consoleSpy).toHaveBeenCalledWith('Action 2');
      expect(consoleSpy).toHaveBeenCalledWith('Action 3');
      consoleSpy.mockRestore();
    });

    it('should execute actions with concurrency limit', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const actions: ActionConfig[] = [
        { action: 'log', payload: ['Action 1'] },
        { action: 'log', payload: ['Action 2'] },
        { action: 'log', payload: ['Action 3'] },
        { action: 'log', payload: ['Action 4'] },
        { action: 'log', payload: ['Action 5'] },
      ];

      await act(async () => {
        await result.current.callClientActionsWithLimit(actions, 2);
      });

      expect(consoleSpy).toHaveBeenCalledTimes(5);
      consoleSpy.mockRestore();
    });
  });

  describe('Batch Executor', () => {
    it('should create and execute batch', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await act(async () => {
        const batch = result.current.createBatch();
        batch.add({ action: 'log', payload: ['Batch Action 1'] });
        batch.add({ action: 'log', payload: ['Batch Action 2'] });

        expect(batch.size()).toBe(2);

        await batch.execute({ mode: 'sequential' });
      });

      expect(consoleSpy).toHaveBeenCalledWith('Batch Action 1');
      expect(consoleSpy).toHaveBeenCalledWith('Batch Action 2');
      consoleSpy.mockRestore();
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain backward compatibility with callClientAction', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const config: ActionConfig = {
        action: 'log',
        payload: ['Backward compatibility test'],
      };

      await act(async () => {
        // Test sync execution
        result.current.callClientAction(config, false);

        // Test async execution
        await result.current.callClientAction(config, true);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Backward compatibility test');
      expect(consoleSpy).toHaveBeenCalledTimes(2);
      consoleSpy.mockRestore();
    });
  });

  describe('withAsync Method', () => {
    it('should execute single action with withAsync', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const config: ActionConfig = {
        action: 'log',
        payload: ['withAsync test'],
      };

      await act(async () => {
        await result.current.withAsync(config);
      });

      expect(consoleSpy).toHaveBeenCalledWith('withAsync test');
      consoleSpy.mockRestore();
    });

    it('should execute multiple actions with withAsync', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const configs: ActionConfig[] = [
        { action: 'log', payload: ['withAsync Action 1'] },
        { action: 'log', payload: ['withAsync Action 2'] },
      ];

      await act(async () => {
        await result.current.withAsync(configs, { mode: 'concurrent' });
      });

      expect(consoleSpy).toHaveBeenCalledWith('withAsync Action 1');
      expect(consoleSpy).toHaveBeenCalledWith('withAsync Action 2');
      consoleSpy.mockRestore();
    });
  });
});

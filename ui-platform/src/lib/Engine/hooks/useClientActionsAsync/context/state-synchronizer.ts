import { useAppStore } from '../../../useAppStore';
import { StateSynchronizer } from '../types';

/**
 * Creates a state synchronizer that ensures immediate state updates
 * This fixes the stale state issue by removing setTimeout delays
 */
export const createStateSynchronizer = (): StateSynchronizer => {
  const store = useAppStore;

  return {
    /**
     * Updates state immediately without setTimeout delays
     */
    updateState: (updates: any) => {
      if (typeof updates === 'function') {
        store.setState(updates);
      } else {
        store.setState((state: any) => ({ ...state, ...updates }));
      }
    },

    /**
     * Clears specified state keys or all state
     */
    clearState: (keys?: string[]) => {
      if (!keys || keys.length === 0) {
        store.setState({}, true);
        return;
      }
      
      const clearUpdates = keys.reduce(
        (acc: any, key: string) => ({ ...acc, [key]: undefined }),
        {}
      );
      store.setState((state: any) => ({ ...state, ...clearUpdates }));
    },

    /**
     * Gets current state - always returns fresh state
     */
    getState: () => {
      return store.getState();
    },

    /**
     * Subscribes to state changes
     */
    onStateChange: (callback: (state: any) => void) => {
      return store.subscribe(callback);
    },
  };
};

// external modules imports
import Keycloak from 'keycloak-js';
import { UseFormReturn } from 'react-hook-form';
import {
  FetcherWithComponents,
  Location,
  NavigateFunction,
  SubmitFunction,
} from 'react-router-dom';

// internal module imports
import { ActionConfig, ExtendedActionConfig, SwitchCase } from '../../../models/action.config';

/**
 * Props interface for the useClientActionsAsync hook
 */
export interface UseClientActionsAsyncProps {
  navigate: NavigateFunction;
  location: Location<any>;
  keycloak?: Keycloak;
  fetcher?: any; // FetcherWithComponents<any>;
  submit?: SubmitFunction;
  formContext?: UseFormReturn;
  envObject?: any;
  tokenPrefix?: 'Bearer' | 'Token';
  debug?: boolean;
}

/**
 * Context object to reduce parameter passing between action handlers
 */
export interface ActionContext {
  navigate: NavigateFunction;
  location: Location<any>;
  keycloak?: Keycloak;
  fetcher?: FetcherWithComponents<any>;
  submit?: SubmitFunction;
  formContext?: UseFormReturn;
  envObject?: any;
  tokenPrefix: 'Bearer' | 'Token';
  setState: any;
  setModalState: any;
  setAsyncLoading: any;
  addError: any;
  clearError: any;
  clearAllErrors: any;
  fc: any;
  offlineHandler: () => void;
  debug?: boolean;
  // Fresh store access function
  getStoreState: () => any;
}

/**
 * Action handler function type
 */
export type ActionHandler = (
  config: ActionConfig,
  context: ActionContext
) => Promise<void> | void;

/**
 * Extended action properties for configuration
 */
export type ExtendedActionProperties =
  | 'async'
  | 'debug'
  | 'concurrencyLimit'
  | 'asyncLoadStart'
  | 'asyncLoadEnd';

/**
 * Async loader management type
 */
export type ManageAsyncLoaderType = {
  startLoader?: boolean;
  stopLoader?: boolean;
};

/**
 * Expression evaluation options
 */
export interface ExpressionEvaluationOptions {
  debug?: boolean;
}

/**
 * Action execution options
 */
export interface ActionExecutionOptions {
  mode?: 'sequential' | 'concurrent';
  concurrencyLimit?: number;
  debug?: boolean;
}

/**
 * Batch executor interface
 */
export interface BatchExecutor {
  add: (config: ActionConfig) => void;
  execute: (options?: ActionExecutionOptions) => Promise<void>;
  size: () => number;
  clear: () => void;
}

/**
 * Hook return type interface
 */
export interface UseClientActionsAsyncReturn {
  callClientAction: (
    config: ActionConfig | ActionConfig[],
    async?: boolean,
    concurrencyLimit?: number,
    debug?: boolean
  ) => void | Promise<void>;
  callClientActionAsync: (config: ActionConfig) => Promise<void>;
  callClientActionsSequentially: (configs: ActionConfig[], debug?: boolean) => Promise<void>;
  callClientActionsConcurrently: (configs: ActionConfig[], debug?: boolean) => Promise<void>;
  callClientActionsWithLimit: (
    configs: ActionConfig[],
    concurrencyLimit?: number,
    debug?: boolean
  ) => Promise<void>;
  executeClientActionAsynchronously: (config: ActionConfig, debug?: boolean) => Promise<void>;
  executeClientActionSynchronously: (config: ActionConfig, debug?: boolean) => void | Promise<void>;
  withAsync: <T extends ActionConfig | ActionConfig[]>(
    config: T,
    options?: ActionExecutionOptions
  ) => Promise<void>;
  createBatch: () => BatchExecutor;
}

/**
 * State synchronization interface
 */
export interface StateSynchronizer {
  updateState: (updates: any) => void;
  clearState: (keys?: string[]) => void;
  getState: () => any;
  onStateChange: (callback: (state: any) => void) => () => void;
}

/**
 * Action registry interface
 */
export interface ActionRegistry {
  [actionName: string]: ActionHandler;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Error handler type
 */
export type ErrorHandler = (error: unknown, actionName: string) => void;

// Re-export commonly used types from models
export type { ActionConfig, ExtendedActionConfig, SwitchCase };

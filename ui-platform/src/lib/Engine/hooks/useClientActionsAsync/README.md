# useClientActionsAsync - Refactored Module

## Overview

This is a comprehensive refactoring of the `useClientActionsAsync` hook that addresses critical state synchronization issues and improves maintainability through modular architecture.

## Key Improvements

### 🔧 State Synchronization Fix
- **Problem**: The original implementation used `setTimeout` in `handleUpdateStore`, causing stale state issues where subsequent actions couldn't see fresh store updates
- **Solution**: Implemented immediate state updates through `createStateSynchronizer` that removes setTimeout delays and ensures `getStoreState()` always returns fresh state

### 🏗️ Modular Architecture
- **Before**: Single 1,618-line monolithic file
- **After**: Organized into focused modules with clear separation of concerns:
  - `types/` - TypeScript interfaces and type definitions
  - `utils/` - Expression evaluation, validation, and helper functions
  - `handlers/` - Individual action handler functions
  - `context/` - State management and synchronization
  - `hooks/` - Main hook and execution logic

### 🔄 Backward Compatibility
- All existing public APIs are preserved
- Existing code using the hook will work without changes
- Same function signatures and behavior patterns

## Module Structure

```
useClientActionsAsync/
├── types/
│   └── index.ts                 # All TypeScript interfaces
├── utils/
│   ├── validation.ts           # Config validation functions
│   ├── expression-evaluation.ts # Expression processing logic
│   ├── error-handling.ts       # Error management utilities
│   ├── async-loader.ts         # Async loading state management
│   └── index.ts               # Utility exports
├── handlers/
│   ├── simple-actions.ts       # Basic actions (log, navigate, etc.)
│   ├── modal-actions.ts        # Modal-related actions
│   ├── store-actions.ts        # Store update actions (with sync fix)
│   ├── form-actions.ts         # Form manipulation actions
│   ├── fetch-actions.ts        # API call actions
│   ├── control-flow-actions.ts # Conditional, forEach, switch actions
│   ├── action-registry.ts      # Action handler registry
│   └── index.ts               # Handler exports
├── context/
│   ├── state-synchronizer.ts   # Immediate state sync implementation
│   └── index.ts               # Context exports
├── hooks/
│   ├── useClientActionsAsync.ts # Main refactored hook
│   ├── action-executor.ts      # Core execution engine
│   ├── batch-executor.ts       # Concurrent execution management
│   └── index.ts               # Hook exports
├── __tests__/
│   ├── integration.test.ts     # Integration tests (✅ passing)
│   ├── useClientActionsAsync.test.ts # Hook tests
│   └── state-synchronization.test.ts # State sync tests
├── index.ts                    # Main module exports
└── README.md                   # This documentation
```

## Usage

### Basic Usage (Unchanged)
```typescript
const {
  callClientAction,
  callClientActionAsync,
  callClientActionsSequentially,
  callClientActionsConcurrently
} = useClientActionsAsync({
  navigate,
  location,
  keycloak,
  // ... other props
});

// Execute single action
await callClientActionAsync({
  action: 'updateStore',
  payload: [{ user: { name: 'John' } }]
});

// Execute multiple actions sequentially
await callClientActionsSequentially([
  { action: 'updateStore', payload: [{ counter: 1 }] },
  { action: 'log', payload: ['Counter updated'] }
]);
```

### New Advanced Features

#### Batch Executor
```typescript
const batch = createBatch();
batch.add({ action: 'log', payload: ['Action 1'] });
batch.add({ action: 'log', payload: ['Action 2'] });

await batch.execute({
  mode: 'concurrent',
  concurrencyLimit: 3
});
```

#### withAsync Method
```typescript
await withAsync(actions, {
  mode: 'concurrent',
  concurrencyLimit: 5,
  debug: true
});
```

## State Synchronization Fix

### Before (Problematic)
```typescript
// In handleUpdateStore
setTimeout(() => context.setState(updates), config.debounce || 0);

// Later action sees stale state
const staleState = getStoreState(); // ❌ Stale data
```

### After (Fixed)
```typescript
// Immediate state updates
const stateSynchronizer = createStateSynchronizer();
stateSynchronizer.updateState(updates); // ✅ Immediate

// Fresh state access
const freshState = context.getStoreState(); // ✅ Always fresh
```

## Expression Evaluation

The refactored module maintains full support for all expression types:

- `@param:{path}` - Parameter substitution
- `js:{expression}` - JavaScript evaluation with store state
- `#{template}` - Template expressions
- `${template}` - Alternative template syntax

### Example: State Synchronization in Action Chains
```typescript
const actionChain = [
  {
    action: 'updateStore',
    payload: [{ user: { name: 'Updated' } }]
  },
  {
    action: 'conditional',
    payload: {
      condition: 'js:{user.name === "Updated"}', // ✅ Sees fresh state
      actions: {
        whenTrue: [{ action: 'log', payload: ['State is fresh!'] }]
      }
    }
  }
];
```

## Testing

The module includes comprehensive tests:

```bash
# Run integration tests (✅ passing)
npm test -- ui-platform/src/lib/Engine/hooks/useClientActionsAsync/__tests__/integration.test.ts

# Run all tests
npm test -- ui-platform/src/lib/Engine/hooks/useClientActionsAsync/__tests__/
```

## Migration Guide

### For Existing Code
No changes required! The refactored module maintains full backward compatibility.

### For New Development
Consider using the new advanced features:
- Use `withAsync()` for flexible execution options
- Use `createBatch()` for complex concurrent operations
- Import specific utilities for custom implementations

## Performance Benefits

1. **Immediate State Updates**: No more setTimeout delays causing stale state
2. **Modular Loading**: Only load needed functionality
3. **Better Tree Shaking**: Smaller bundle sizes with focused modules
4. **Optimized Execution**: Improved concurrent action handling

## Architecture Benefits

1. **Maintainability**: Focused, single-responsibility modules
2. **Testability**: Individual components can be tested in isolation
3. **Extensibility**: Easy to add new action handlers or utilities
4. **Type Safety**: Comprehensive TypeScript interfaces
5. **Documentation**: Clear separation of concerns and responsibilities

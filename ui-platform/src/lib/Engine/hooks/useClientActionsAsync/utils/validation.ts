import { ActionConfig } from '../types';

/**
 * Validates that a config object is valid for action execution
 */
export const validateConfig = (
  config: any,
  functionName: string
): config is ActionConfig => {
  if (config === null || config === undefined) {
    console.warn(
      `${functionName} received null or undefined config, ignoring action`
    );
    return false;
  }

  if (typeof config !== 'object') {
    console.warn(
      `${functionName} received invalid config type, expected object`
    );
    return false;
  }

  if (!('action' in config)) {
    console.warn(
      `${functionName} received config without action property, ignoring action`
    );
    return false;
  }

  if (typeof config.action !== 'string') {
    console.warn(
      `${functionName} received config with invalid action type, expected string`
    );
    return false;
  }

  return true;
};

/**
 * Validation for sync execution
 */
export const validateConfigSync = (config: any): config is ActionConfig => {
  return validateConfig(config, 'executeClientActionSynchronously');
};

/**
 * Validation for async execution
 */
export const validateConfigAsync = (config: any): config is ActionConfig => {
  return validateConfig(config, 'executeClientActionAsynchronously');
};

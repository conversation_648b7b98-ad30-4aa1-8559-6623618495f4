import { <PERSON>rror<PERSON><PERSON><PERSON> } from '../types';

/**
 * Creates a standardized error handler function
 */
export const createErrorHandler = (addError: any): ErrorHandler => {
  return (error: unknown, actionName: string) => {
    addError({
      key: `${actionName}-${Date.now()}`,
      message:
        error instanceof Error
          ? `${error.message}: Please try refreshing the page`
          : 'Unknown error: Try refreshing the website',
      source: 'server',
      stackTrace: error instanceof Error ? error.stack : undefined,
    });
  };
};

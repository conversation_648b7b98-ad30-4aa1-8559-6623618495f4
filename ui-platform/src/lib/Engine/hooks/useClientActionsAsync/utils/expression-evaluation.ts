import { Template<PERSON>iteralLogger } from '../../../../Utilities';
import { evalStringExpression, processTemplates } from '../../../helpers';
import { ExpressionEvaluationOptions } from '../types';

// instantiate logger for debugging
const logger = new TemplateLiteralLogger({
  prefix: '🔍[Expression Evaluation]:',
  enabled: false,
  options: { style: { backgroundColor: '#e3f2fd', color: '#1565c0' } },
});

/**
 * Helper function to get nested value from an object using dot notation
 */
export const getNestedValue = (obj: any, path: string): any => {
  if (!path || !obj) return obj;
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

/**
 * Helper function to evaluate string expressions and handle @param substitutions.
 * Consolidates all string processing logic in one place.
 */
export const evaluateStringExpression = (
  value: any,
  param?: any,
  storeState?: any,
  options?: ExpressionEvaluationOptions
): any => {
  // Handle non-string primitives
  if (typeof value !== 'string') {
    return value;
  }

  // Handle @param direct substitution
  if (value === '@param') {
    return param;
  }

  // Handle @param template expressions
  if (value.startsWith('@param:{')) {
    const expression = value.substring(8, value.length - 1); // Remove '@param:{' and trailing '}'
    // If param is undefined, return the original expression to be evaluated later
    if (param === undefined || param === null) {
      return value;
    }
    // Directly extract the value from param using the path
    return getNestedValue(param, expression);
  }

  // Handle store-based template expressions (starting with # or $)
  if (value.startsWith('#') || value.startsWith('js:')) {
    return evalStringExpression(value, storeState, {
      debug: options?.debug,
    });
  }

  if (value.startsWith('$')) {
    const evaluatedResult = evalStringExpression(value, storeState, {
      debug: options?.debug,
    });
    if (!evaluatedResult) {
      return value;
    }
    return evaluatedResult;
  }

  // Handle mixed template expressions that might contain both param and store references
  // Check if the string contains template patterns
  if (value.includes('#{') || value.includes('${') || value.includes('js:')) {
    return evalStringExpression(value, storeState, {
      debug: options?.debug,
    });
  }

  // Handle store-based template expressions with curly brackets - (containing '{' and '}' respectively)
  // This should always be the final attempt to evaluate the template before returning the value as-is because
  // it is a fallback for evaluating "#" templates that use renderTemplate and is supposed to be the default method.
  // Thus it should only trigger when all the other templates fail
  if (/\{.*\}/.test(value)) {
    return evalStringExpression(value, storeState, {
      debug: options?.debug,
    });
  }

  // Return plain strings as-is
  return value;
};

/**
 * Recursively evaluates a payload, resolving dynamic expressions in both keys and values.
 * Handles arrays, nested objects, and dynamic keys/values using param or store state.
 *
 * @param payload - The input object, array, or value to evaluate.
 * @param param - Optional parameter object for dynamic substitution.
 * @param getStoreState - Function to get fresh store state.
 * @param visited - Set to track visited objects for circular reference detection.
 * @param options - Evaluation options including debug flag.
 * @returns The evaluated payload with all dynamic expressions resolved.
 */
export const evaluateExpression = (
  payload: any,
  param?: any,
  getStoreState?: () => any,
  visited = new WeakSet(),
  options?: ExpressionEvaluationOptions
): any => {
  // Get fresh store state - this is critical for state synchronization
  const storeState = getStoreState ? getStoreState() : {};

  logger.configure({
    enabled: options?.debug,
  });

  logger.info`Expression being evaluated... ${{
    template: payload,
    clientActionParameter: param,
    storeData: storeState,
    visited,
  }}`;

  // Handle primitive types and null/undefined
  if (
    payload === null ||
    payload === undefined ||
    typeof payload !== 'object'
  ) {
    logger.info`Template evaluated to be primitive, null or undefined... ${{
      template: payload,
      clientActionParameter: param,
      storeData: storeState,
    }}`;
    return evaluateStringExpression(payload, param, storeState, {
      debug: options?.debug,
    });
  }

  // Circular reference detection
  if (visited.has(payload)) {
    logger.warn`Circular reference detected, returning original payload`;
    return payload;
  }

  // Handle arrays
  if (Array.isArray(payload)) {
    visited.add(payload);
    const result = payload.map((item) =>
      evaluateExpression(item, param, getStoreState, visited, {
        debug: options?.debug,
      })
    );
    visited.delete(payload);
    return result;
  }

  if (
    Object.keys(payload).includes('$') ||
    Object.keys(payload).includes('$spread')
  ) {
    const result = processTemplates(payload, storeState, {
      debug: options?.debug,
    });
    logger.info`The payload after evaluating spread expressions is ${result}`;
    return result;
  }

  // Handle objects (including dynamic keys)
  visited.add(payload);
  const result = Object.entries(payload).reduce((acc, [key, value]) => {
    // Evaluate key if it's a dynamic expression
    const evaluatedKey = evaluateStringExpression(key, param, storeState, {
      debug: options?.debug,
    });

    // Evaluate value recursively
    acc[evaluatedKey] = evaluateExpression(
      value,
      param,
      getStoreState,
      visited,
      {
        debug: options?.debug,
      }
    );

    return acc;
  }, {} as Record<string, any>);

  visited.delete(payload);
  return result;
};

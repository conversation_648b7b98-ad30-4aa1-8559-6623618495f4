import { ExtendedActionConfig, ActionContext, ManageAsyncLoaderType, ExtendedActionProperties } from '../types';

/**
 * Checks if config has extended action property
 */
export const checkConfigHasExtendedActionProperty = (
  config: any,
  propertyName: ExtendedActionProperties
): config is ExtendedActionConfig =>
  propertyName in config && config[propertyName] !== undefined;

/**
 * Manages async loader state for actions
 */
export const manageAsyncLoader = async (
  config: ExtendedActionConfig,
  context: ActionContext,
  func: ({
    startLoader,
    stopLoader,
  }?: ManageAsyncLoaderType) => void | Promise<void>
) => {
  let startLoader: boolean | undefined;
  let stopLoader: boolean | undefined;

  if (checkConfigHasExtendedActionProperty(config, 'asyncLoadStart')) {
    startLoader = config.asyncLoadStart === true;
    if (startLoader) {
      context.setAsyncLoading(true);
    }
  }

  try {
    await func({ startLoader, stopLoader });
  } finally {
    if (checkConfigHasExtendedActionProperty(config, 'asyncLoadEnd')) {
      stopLoader = config.asyncLoadEnd === true;
      if (stopLoader) {
        context.setAsyncLoading(false);
      }
    }
  }
};

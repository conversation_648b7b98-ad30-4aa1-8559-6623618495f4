// Main export file for the refactored useClientActionsAsync module
// This maintains backward compatibility while providing access to all new modular components

// Export the main hook
export { useClientActionsAsync } from './hooks/useClientActionsAsync';

// Export types for consumers who need them
export type {
  UseClientActionsAsyncProps,
  UseClientActionsAsyncReturn,
  ActionConfig,
  ActionContext,
  ActionExecutionOptions,
  BatchExecutor,
  StateSynchronizer,
} from './types';

// Export utilities for advanced usage
export {
  evaluateExpression,
  validateConfig,
  createErrorHandler,
} from './utils';

// Export action handlers for custom implementations
export {
  createActionRegistry,
  executeAction,
  executeActionSync,
  createBatchExecutor,
} from './handlers';

// Export context management
export { createStateSynchronizer } from './context';

import { TemplateLiteralLogger } from '../../../../Utilities';
import { ActionConfig, ActionContext, ExtendedActionConfig, ManageAsyncLoaderType } from '../types';
import { validateConfigAsync, validateConfigSync, evaluateExpression, manageAsyncLoader } from '../utils';
import { createActionRegistry } from '../handlers/action-registry';
import { 
  handleSubmitAndNavigate, 
  handleSubmitAndFetch, 
  handleSubmitAsync, 
  handleTriggerFetchCall,
  handleConditional,
  handleTimeout,
  handleSwitch,
  handleForEach
} from '../handlers';

// instantiate logger for debugging
const logger = new TemplateLiteralLogger({
  prefix: '⚡[Action Executor]:',
  enabled: false,
  options: { style: { backgroundColor: '#fff3cd', color: '#856404' } },
});

/**
 * Core action execution function that handles both simple and complex actions
 * This version fixes state synchronization by ensuring fresh state access
 */
export const executeAction = async (
  config: ActionConfig,
  context: ActionContext,
  executeClientAction?: (
    config: ActionConfig,
    debug?: boolean
  ) => Promise<void>,
  debug?: boolean,
  loaderOptions?: ManageAsyncLoaderType
): Promise<void> => {
  logger.configure({
    enabled: config?.debug || debug,
    options: { style: { color: '#d69f14' } },
  });
  
  if (!validateConfigAsync(config)) {
    return;
  }

  const { type = 'clientAction', action, payload, param } = config;

  if (type !== 'clientAction') {
    return;
  }

  // Evaluate expressions with fresh store state - critical for state synchronization
  let newPayload = payload;
  logger.info`The payload before evaluating expressions is ${payload}`;
  newPayload = evaluateExpression(
    payload,
    param,
    context.getStoreState, // This ensures fresh state access
    undefined,
    {
      debug: config?.debug || debug,
    }
  );
  config.payload = newPayload;
  logger.info`The payload after evaluating expressions is ${config.payload}`;

  // Try simple actions first using the registry
  const actionRegistry = createActionRegistry(context);
  const handler = actionRegistry[action];
  if (handler) {
    logger.info`Executing action ${action} with payload ${config.payload}`;
    return await handler(config, context);
  }

  // Handle complex actions that need special logic
  switch (action) {
    case 'submitAndNavigate':
      return handleSubmitAndNavigate(config, context);
    case 'submitAndFetch':
      return handleSubmitAndFetch(config, context);
    case 'submitAsync':
      return handleSubmitAsync(
        config,
        context,
        executeClientAction,
        loaderOptions
      );
    case 'triggerFetchCall':
      return handleTriggerFetchCall(config, context, loaderOptions);
    case 'conditional':
      return handleConditional(config, context, executeClientAction);
    case 'timeout':
      return handleTimeout(config, context, executeClientAction);
    case 'switch':
      return handleSwitch(config, context, executeClientAction);
    case 'forEach':
      return handleForEach(config, context, executeClientAction);
    default:
      logger.warn`no handler for the function you called: ${action}`;
      break;
  }
};

/**
 * Synchronous version of executeAction for backward compatibility
 */
export const executeActionSync = (
  config: ActionConfig,
  context: ActionContext,
  executeClientActionSynchronously?: (
    config: ActionConfig,
    debug?: boolean
  ) => void | Promise<void>,
  debug?: boolean
): void => {
  logger.configure({
    enabled: debug,
    options: { style: { color: '#d69f14' } },
  });
  
  if (!validateConfigSync(config)) {
    return;
  }

  const { type = 'clientAction', action, payload, param } = config;

  if (type !== 'clientAction') {
    return;
  }

  // Evaluate expressions with fresh store state
  let newPayload = payload;
  logger.info`The payload before evaluating expressions is ${payload}`;
  newPayload = evaluateExpression(
    payload, 
    param, 
    context.getStoreState, // Fresh state access
    undefined,
    { debug }
  );
  config.payload = newPayload;
  logger.info`The new payload after evaluating expressions is ${newPayload}`;
  logger.info`The payload after evaluating expressions is ${config.payload}`;

  const actionRegistry = createActionRegistry(context);
  const handler = actionRegistry[action];

  if (handler) {
    logger.info`Executing action ${action} with payload ${config.payload}`;
    handler(config, context);
    return;
  }

  // Handle complex actions that need special logic
  switch (action) {
    case 'submitAndNavigate':
      handleSubmitAndNavigate(config, context);
      break;
    case 'submitAndFetch':
      handleSubmitAndFetch(config, context);
      break;
    case 'submitAsync':
      handleSubmitAsync(
        config,
        context,
        executeClientActionSynchronously as any
      );
      break;
    case 'triggerFetchCall':
      handleTriggerFetchCall(config, context);
      break;
    case 'conditional':
      handleConditional(
        config,
        context,
        executeClientActionSynchronously as any
      );
      break;
    case 'timeout':
      handleTimeout(config, context, executeClientActionSynchronously as any);
      break;
    case 'switch':
      handleSwitch(config, context, executeClientActionSynchronously as any);
      break;
    case 'forEach':
      handleForEach(config, context, executeClientActionSynchronously as any);
      break;
    default:
      logger.warn`no handler for the function you called: ${action}`;
      break;
  }
};

/**
 * Executes an action with async loader management
 */
export const executeActionWithLoaderManagement = async (
  config: ExtendedActionConfig,
  context: ActionContext,
  executeClientAction: (config: ActionConfig, debug?: boolean) => Promise<void>,
  debug?: boolean
): Promise<void> => {
  // For actions that need loader management
  if (
    config.asyncLoadStart !== undefined ||
    config.asyncLoadEnd !== undefined
  ) {
    return manageAsyncLoader(
      config,
      context,
      async (loaderOptions) => {
        return executeAction(
          config,
          context,
          executeClientAction,
          debug,
          loaderOptions
        );
      }
    );
  } else {
    // Regular execution for actions without loader management
    return executeAction(
      config,
      context,
      executeClientAction,
      debug
    );
  }
};

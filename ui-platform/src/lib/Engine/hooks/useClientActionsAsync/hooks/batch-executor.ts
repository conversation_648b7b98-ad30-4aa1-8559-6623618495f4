import { ActionConfig, ActionContext, BatchExecutor, ActionExecutionOptions } from '../types';
import { executeActionWithLoaderManagement } from './action-executor';

/**
 * Creates a batch executor for managing concurrent action execution
 */
export const createBatchExecutor = (
  context: ActionContext,
  executeClientAction: (config: ActionConfig, debug?: boolean) => Promise<void>
): BatchExecutor => {
  const actions: ActionConfig[] = [];

  return {
    add: (config: ActionConfig) => {
      actions.push(config);
    },

    execute: async (options?: ActionExecutionOptions) => {
      const { mode = 'sequential', concurrencyLimit = 5, debug } = options || {};

      if (mode === 'sequential') {
        // Execute actions sequentially
        for (const config of actions) {
          await executeActionWithLoaderManagement(
            config,
            context,
            executeClientAction,
            debug
          );
        }
      } else {
        // Execute actions concurrently with limit
        const executeWithLimit = async (configs: ActionConfig[], limit: number) => {
          const results: Promise<void>[] = [];
          
          for (let i = 0; i < configs.length; i += limit) {
            const batch = configs.slice(i, i + limit);
            const batchPromises = batch.map(config =>
              executeActionWithLoaderManagement(
                config,
                context,
                executeClientAction,
                debug
              )
            );
            results.push(...batchPromises);
            
            // Wait for current batch to complete before starting next batch
            await Promise.all(batchPromises);
          }
          
          return Promise.all(results);
        };

        await executeWithLimit(actions, concurrencyLimit);
      }
    },

    size: () => actions.length,

    clear: () => {
      actions.length = 0;
    },
  };
};

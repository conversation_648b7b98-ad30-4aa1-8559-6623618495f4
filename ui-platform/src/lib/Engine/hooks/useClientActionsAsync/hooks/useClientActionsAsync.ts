import { useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { useAppStore } from '../../../useAppStore';
import { useAsyncLoaderStore } from '../../useAsyncLoaderStore';
import { useErrorStore } from '../../useErrorStore';
import { useModalStore } from '../../useModalStore';
import {
  ActionConfig,
  ActionContext,
  ActionExecutionOptions,
  UseClientActionsAsyncProps,
  UseClientActionsAsyncReturn,
} from '../types';
import {
  executeAction,
  executeActionSync,
  executeActionWithLoaderManagement,
} from './action-executor';
import { createBatchExecutor } from './batch-executor';

/**
 * Refactored useClientActionsAsync hook with modular architecture and fixed state synchronization
 */
export const useClientActionsAsync = ({
  navigate,
  location,
  keycloak,
  fetcher,
  submit,
  formContext,
  envObject,
  tokenPrefix = 'Bearer',
  debug = false,
}: UseClientActionsAsyncProps): UseClientActionsAsyncReturn => {
  // Store hooks
  const { setState, getState } = useAppStore();
  const { setModalState } = useModalStore();
  const { setAsyncLoading } = useAsyncLoaderStore();
  const { addError, clearError, clearAllErrors } = useErrorStore();
  const fc = useFormContext();

  // Create action context with fresh state access
  const createActionContext = useCallback(
    (): ActionContext => ({
      navigate,
      location,
      keycloak,
      fetcher,
      submit,
      formContext: formContext || fc,
      envObject,
      tokenPrefix,
      setState,
      setModalState,
      setAsyncLoading,
      addError,
      clearError,
      clearAllErrors,
      fc,
      offlineHandler: () => {
        addError({
          key: 'offline-error',
          message: 'You are offline. Please check your internet connection.',
          source: 'network',
        });
      },
      debug,
      // Critical: Always return fresh state
      getStoreState: () => getState(),
    }),
    [
      navigate,
      location,
      keycloak,
      fetcher,
      submit,
      formContext,
      fc,
      envObject,
      tokenPrefix,
      setState,
      setModalState,
      setAsyncLoading,
      addError,
      clearError,
      clearAllErrors,
      debug,
      getState,
    ]
  );

  // Core execution functions
  const executeClientActionAsynchronously = useCallback(
    async (config: ActionConfig, dbg?: boolean): Promise<void> => {
      const context = createActionContext();
      return executeActionWithLoaderManagement(
        config,
        context,
        executeClientActionAsynchronously,
        dbg || debug
      );
    },
    [createActionContext, debug]
  );

  const executeClientActionSynchronously = useCallback(
    (config: ActionConfig, dbg?: boolean): void | Promise<void> => {
      const context = createActionContext();
      return executeActionSync(
        config,
        context,
        executeClientActionSynchronously,
        dbg || debug
      );
    },
    [createActionContext, debug]
  );

  // Sequential execution
  const callClientActionsSequentially = useCallback(
    async (configs: ActionConfig[], dbg?: boolean): Promise<void> => {
      for (const config of configs) {
        await executeClientActionAsynchronously(config, dbg || debug);
      }
    },
    [executeClientActionAsynchronously, debug]
  );

  // Concurrent execution with limit
  const callClientActionsConcurrently = useCallback(
    async (configs: ActionConfig[], dbg?: boolean): Promise<void> => {
      const promises = configs.map((config) =>
        executeClientActionAsynchronously(config, dbg || debug)
      );
      await Promise.all(promises);
    },
    [executeClientActionAsynchronously, debug]
  );

  const callClientActionsWithLimit = useCallback(
    async (
      configs: ActionConfig[],
      concurrencyLimit: number = 5,
      dbg?: boolean
    ): Promise<void> => {
      const context = createActionContext();
      const batch = createBatchExecutor(
        context,
        executeClientActionAsynchronously
      );

      configs.forEach((config) => batch.add(config));

      await batch.execute({
        mode: 'concurrent',
        concurrencyLimit,
        debug: dbg || debug,
      });
    },
    [createActionContext, executeClientActionAsynchronously, debug]
  );

  // Single action execution
  const callClientActionAsync = useCallback(
    async (config: ActionConfig): Promise<void> => {
      return executeClientActionAsynchronously(config, debug);
    },
    [executeClientActionAsynchronously, debug]
  );

  // Main entry point - maintains backward compatibility
  const callClientAction = useCallback(
    (
      config: ActionConfig | ActionConfig[],
      async?: boolean,
      concurrencyLimit?: number,
      dbg?: boolean
    ): void | Promise<void> => {
      if (Array.isArray(config)) {
        if (async) {
          if (concurrencyLimit && concurrencyLimit > 1) {
            return callClientActionsWithLimit(config, concurrencyLimit, dbg);
          } else {
            return callClientActionsConcurrently(config, dbg);
          }
        } else {
          return callClientActionsSequentially(config, dbg);
        }
      } else {
        if (async) {
          return executeClientActionAsynchronously(config, dbg);
        } else {
          return executeClientActionSynchronously(config, dbg);
        }
      }
    },
    [
      callClientActionsSequentially,
      callClientActionsConcurrently,
      callClientActionsWithLimit,
      executeClientActionAsynchronously,
      executeClientActionSynchronously,
    ]
  );

  // Advanced execution with options
  const withAsync = useCallback(
    async <T extends ActionConfig | ActionConfig[]>(
      config: T,
      options?: ActionExecutionOptions
    ): Promise<void> => {
      const {
        mode = 'sequential',
        concurrencyLimit = 5,
        debug: dbg,
      } = options || {};

      if (Array.isArray(config)) {
        if (mode === 'concurrent') {
          return callClientActionsWithLimit(config, concurrencyLimit, dbg);
        } else {
          return callClientActionsSequentially(config, dbg);
        }
      } else {
        return executeClientActionAsynchronously(config, dbg);
      }
    },
    [
      callClientActionsSequentially,
      callClientActionsWithLimit,
      executeClientActionAsynchronously,
    ]
  );

  // Batch executor factory
  const createBatch = useCallback(() => {
    const context = createActionContext();
    return createBatchExecutor(context, executeClientActionAsynchronously);
  }, [createActionContext, executeClientActionAsynchronously]);

  return {
    callClientAction,
    callClientActionAsync,
    callClientActionsSequentially,
    callClientActionsConcurrently,
    callClientActionsWithLimit,
    executeClientActionAsynchronously,
    executeClientActionSynchronously,
    withAsync,
    createBatch,
  };
};

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export type WorkflowViewType = 'normal' | 'thin';

interface WorkflowViewState {
  currentView: WorkflowViewType;
  toggleView: () => void;
  setView: (view: WorkflowViewType) => void;
}

export const useWorkflowViewStore = create<WorkflowViewState>()(
  devtools(
    (set) => ({
      currentView: 'normal' as WorkflowViewType,
      toggleView: () =>
        set((state) => ({
          currentView: state.currentView === 'normal' ? 'thin' : 'normal',
        })),
      setView: (view: WorkflowViewType) =>
        set(() => ({
          currentView: view,
        })),
    }),
    {
      name: 'workflow-view-store',
    }
  )
);

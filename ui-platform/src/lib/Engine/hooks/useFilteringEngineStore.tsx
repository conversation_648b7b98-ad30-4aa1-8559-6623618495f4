'use client';
import { create } from 'zustand';

// Define the type for filter functions
export type FilterFunction = {
    name: string;
    filterFn: (item: any) => boolean;
    getSearchResults?: () => any[];
};

// Define the type for sort functions
export type SortOrder = 'asc' | 'desc';

export type SortFunction = {
    name: string;
    sortFn: (a: any, b: any) => number;
    active: boolean;
    order: SortOrder;
};

export interface FilteringEngineState {
    filterFunctions: FilterFunction[];
    setFilterFunctions: (functions: FilterFunction[]) => void;
    addFilterFunction: (filterFunction: FilterFunction) => void;
    removeFilterFunction: (name: string) => void;
    clearFilterFunctions: () => void;
    getFilteredData: (data: any[]) => any[];
    
    // Sorting related state and functions
    toggleSort: boolean;
    setToggleSort: () => void;
    getSortedData: (data: any[]) => any[];
    getFilteredAndSortedData: (data: any[]) => any[];
}

export const useFilteringEngineStore = create<FilteringEngineState>()((set, get) => ({
    // Filtering related state and functions
    filterFunctions: [],
    setFilterFunctions: (functions: FilterFunction[]) => set({ filterFunctions: functions }),
    addFilterFunction: (filterFn: FilterFunction) => set((state) => ({
        filterFunctions: [...state.filterFunctions, filterFn],
    })),
    removeFilterFunction: (name: string) => set((state) => {
        
        const newFilterFunctions = [...state.filterFunctions.filter((filter) => filter.name !== name)];
                
        return {
            filterFunctions: newFilterFunctions,
        };
    }),
    clearFilterFunctions: () => set({ filterFunctions: [] }),
    getFilteredData: (data: any[]) => {
        return data.filter((item) => {
            return get().filterFunctions.every((filterFn) => filterFn.filterFn(item));
        });
    },
    
    // Sorting related state and functions
    toggleSort: false,
    setToggleSort: () => set({ toggleSort: !get().toggleSort }),
    getSortedData: (data: any[]) => {
        if (get().toggleSort) {
            return [...data].sort((a, b) => {
                // Handle cases where the id property might not exist
                const idA = a?.id !== undefined ? a.id : 0;
                const idB = b?.id !== undefined ? b.id : 0;
                return idA - idB; // Ascending order
            });
        } else {
            return [...data].sort((a, b) => {
                // Handle cases where the id property might not exist
                const idA = a?.id !== undefined ? a.id : 0;
                const idB = b?.id !== undefined ? b.id : 0;
                return idB - idA; // Descending order
            });
        }
    },
    getFilteredAndSortedData: (data: any[]) => {
        // First apply filtering, then sorting
        const filteredData = get().getFilteredData(data);
        return get().getSortedData(filteredData);
    },
}));


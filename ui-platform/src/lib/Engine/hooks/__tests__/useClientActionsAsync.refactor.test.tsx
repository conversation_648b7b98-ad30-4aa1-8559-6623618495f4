import { describe, it, expect, vi } from 'vitest';

// Simple test to verify the refactoring works
describe('useClientActionsAsync - Refactoring Verification', () => {
  it('should verify ActionContext has getStoreState function', () => {
    // Mock store state
    const mockStoreState = { testValue: 'fresh', user: { name: '<PERSON>' } };
    const mockGetState = vi.fn(() => mockStoreState);
    
    // Create a mock ActionContext with the new getStoreState function
    const mockActionContext = {
      navigate: vi.fn(),
      location: { pathname: '/test', state: null, key: 'test', search: '', hash: '' },
      keycloak: undefined,
      fetcher: undefined,
      submit: undefined,
      formContext: undefined,
      envObject: undefined,
      tokenPrefix: 'Bearer' as const,
      setState: vi.fn(),
      setModalState: vi.fn(),
      setAsyncLoading: vi.fn(),
      addError: vi.fn(),
      clearError: vi.fn(),
      clearAllErrors: vi.fn(),
      fc: null,
      offlineHandler: vi.fn(),
      debug: false,
      // This is the new function we added
      getStoreState: mockGetState,
    };

    // Test that getStoreState returns fresh values
    const result = mockActionContext.getStoreState();
    expect(result).toEqual(mockStoreState);
    expect(mockGetState).toHaveBeenCalledTimes(1);

    // Test that multiple calls get fresh values
    const updatedState = { testValue: 'updated', user: { name: 'Jane' } };
    mockGetState.mockReturnValue(updatedState);
    
    const result2 = mockActionContext.getStoreState();
    expect(result2).toEqual(updatedState);
    expect(mockGetState).toHaveBeenCalledTimes(2);
  });

  it('should verify evaluateExpression signature change', () => {
    // This test verifies that evaluateExpression now accepts getStoreState function
    // instead of storeState parameter
    
    const mockStoreState = { value: 'test' };
    const mockGetStoreState = vi.fn(() => mockStoreState);
    
    // Mock the evaluateExpression function with new signature
    const evaluateExpression = (
      payload: any,
      param?: any,
      getStoreState?: () => any,
      visited = new WeakSet(),
      options?: { debug?: boolean }
    ): any => {
      const storeState = getStoreState ? getStoreState() : {};
      
      if (typeof payload === 'string' && payload.startsWith('#{')) {
        const key = payload.slice(2, -1);
        return storeState[key];
      }
      
      return payload;
    };

    // Test with fresh store state function
    const result = evaluateExpression('#{value}', undefined, mockGetStoreState);
    expect(result).toBe('test');
    expect(mockGetStoreState).toHaveBeenCalledTimes(1);

    // Test that it gets fresh values on subsequent calls
    mockGetStoreState.mockReturnValue({ value: 'updated' });
    const result2 = evaluateExpression('#{value}', undefined, mockGetStoreState);
    expect(result2).toBe('updated');
    expect(mockGetStoreState).toHaveBeenCalledTimes(2);
  });

  it('should verify that stale store values issue is resolved', () => {
    // This test simulates the stale store values problem and verifies it's fixed
    
    let currentStoreState = { counter: 0 };
    const mockGetState = vi.fn(() => currentStoreState);
    
    // Simulate the old approach (stale values)
    const oldApproach = () => {
      const storeState = mockGetState(); // Called once, cached
      return {
        getValue: () => storeState.counter,
      };
    };
    
    // Simulate the new approach (fresh values)
    const newApproach = (getStoreState: () => any) => {
      return {
        getValue: () => getStoreState().counter, // Called each time, fresh
      };
    };
    
    // Test old approach (demonstrates the problem)
    const oldHandler = oldApproach();
    expect(oldHandler.getValue()).toBe(0);
    
    // Update store state
    currentStoreState = { counter: 5 };
    
    // Old approach still returns stale value
    expect(oldHandler.getValue()).toBe(0); // Still 0 (stale)
    
    // Test new approach (demonstrates the fix)
    const newHandler = newApproach(mockGetState);
    expect(newHandler.getValue()).toBe(5); // Fresh value
    
    // Update store state again
    currentStoreState = { counter: 10 };
    
    // New approach returns fresh value
    expect(newHandler.getValue()).toBe(10); // Fresh value
  });
});

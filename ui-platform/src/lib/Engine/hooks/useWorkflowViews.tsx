import { useState, useCallback } from 'react';

export type WorkflowViewType = 'normal' | 'thin';

export interface UseWorkflowViewsReturn {
  currentView: WorkflowViewType;
  toggleView: () => void;
  setView: (view: WorkflowViewType) => void;
  isNormalView: boolean;
  isThinView: boolean;
}

/**
 * Hook to manage workflow view state (normal vs thin job card views)
 * @param initialView - The initial view type ('normal' or 'thin')
 * @returns Object containing view state and control functions
 */
export function useWorkflowViews(initialView: WorkflowViewType = 'normal'): UseWorkflowViewsReturn {
  const [currentView, setCurrentView] = useState<WorkflowViewType>(initialView);

  const toggleView = useCallback(() => {
    setCurrentView(prevView => prevView === 'normal' ? 'thin' : 'normal');
  }, []);

  const setView = useCallback((view: WorkflowViewType) => {
    setCurrentView(view);
  }, []);

  const isNormalView = currentView === 'normal';
  const isThinView = currentView === 'thin';

  return {
    currentView,
    toggleView,
    setView,
    isNormalView,
    isThinView,
  };
}

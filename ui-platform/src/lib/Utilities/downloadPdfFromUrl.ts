export function downloadPdfFromUrl(pdfUrl: string, filename: string) {
  fetch(pdfUrl)
  .then(response => {
      if (!response.ok) throw new Error("Failed to fetch PDF");
      return response.blob();
    })
    .then(blob => {
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      a.click();
      URL.revokeObjectURL(url); // cleanup
    })
    .catch(error => {
      console.error("Error downloading the PDF:", error);
    });
}

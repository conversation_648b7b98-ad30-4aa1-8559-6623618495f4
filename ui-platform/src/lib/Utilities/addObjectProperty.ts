import { clone } from 'ramda';

/**
 * Function to add a property to an object. It always returns a new object with the new property added.
 * @param propName The name of the property to add to the object
 * @param value The value assigned to the property in the object.
 * @param obj The object in context, to which the new property is added.
 */
export function addObjectProperty<T, K extends string, V>(
  propName: K,
  value: V,
  obj: T
): T & {
  [propName: string]: V;
} {
  const cloned = clone(obj);
  const objWithProp = { ...cloned, [propName]: value };
  return objWithProp;
}

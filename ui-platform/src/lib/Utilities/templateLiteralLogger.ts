import { flattenObjectWithArrays } from './flattenObject';

const ConsoleLogFunctions = [
  'debug',
  'info',
  'warn',
  'error',
  'table',
  'group',
  'groupCollapsed',
  'groupEnd',
  'time',
  'timeEnd',
  'timeLog',
  'count',
  'countReset',
  'timeStamp',
  'trace',
  'log',
  'assert',
] as const;

const AliasToMethodMap = {
  i: 'info',
  t: 'time',
  err: 'error',
  dbg: 'debug',
  tab: 'table',
} as const;

type ConsoleMethod = (typeof ConsoleLogFunctions)[number];
type Alias = keyof typeof AliasToMethodMap;
type LogLevel = ConsoleMethod | Alias;

const consoleMethods: Record<LogLevel, (...args: any[]) => void> =
  Object.fromEntries(
    ConsoleLogFunctions.map((method) => [
      method,
      console[method as keyof Console],
    ])
  ) as Record<LogLevel, (...args: any[]) => void>;

Object.entries(AliasToMethodMap).forEach(([alias, method]) => {
  consoleMethods[alias as Alias] = console[
    method as keyof Console
  ] as unknown as (...args: any[]) => void;
});

const baseLevelOrder = {
  assert: 1,
  debug: 0,
  info: 1,
  table: 1,
  time: 1,
  timeEnd: 1,
  timeLog: 1,
  count: 1,
  countReset: 1,
  timeStamp: 1,
  group: 1,
  groupCollapsed: 1,
  groupEnd: 1,
  warn: 2,
  trace: 2,
  error: 3,
  log: 1,
};

const levelOrder: Record<LogLevel, number> = { ...baseLevelOrder } as Record<
  LogLevel,
  number
>;

Object.entries(AliasToMethodMap).forEach(([alias, method]) => {
  levelOrder[alias as Alias] =
    baseLevelOrder[method as keyof typeof baseLevelOrder];
});

type LogType = 'client' | 'server';
type PrimitiveType =
  | 'bigint'
  | 'boolean'
  | 'number'
  | 'string'
  | 'function'
  | 'object'
  | 'symbol'
  | 'undefined'
  | 'nullish';

// interface GroupedOptions {
//   collapsed?: boolean;
//   customStructure?: Record<string, any[]>;
// }

interface AdditionalOptions {
  //TODO:  multiLine?: GroupedOptions; // if object is excluded then no grouping
  type?: LogType; // TODO: unify logging utility for both client and server
  style?: Record<string, string>;
  primitivesAllowedInTemplateString?: readonly PrimitiveType[];
  skipPrimitivesIncludedInMessage?: boolean;
  excludeOutputObject?: boolean;
  flattenOutputObject?: boolean;
  flattenedObjectIndexPrefix?: string;
  flattenedObjectIndexDelimeter?: string;
}

export interface LoggerConfig {
  enabled?: boolean;
  prefix?: string;
  minLevel?: LogLevel;
  options?: AdditionalOptions;
}

const DEFAULT_PRIMITIVES_ALLOWED: PrimitiveType[] = [
  'bigint',
  'boolean',
  'number',
  'string',
];

const defaultConfig: LoggerConfig = {
  enabled: true,
  prefix: '🔍[Template Literal Logger]:',
  minLevel: 'debug',
  options: {
    type: 'client',
    primitivesAllowedInTemplateString: DEFAULT_PRIMITIVES_ALLOWED,
    style: {},
    flattenedObjectIndexPrefix: '',
    flattenedObjectIndexDelimeter: '.',
    flattenOutputObject: false,
    skipPrimitivesIncludedInMessage: false,
    excludeOutputObject: false,
  },
};

type INSPECT_KEYS = keyof typeof defaultConfig;

interface TabulatedOutputObjectOptions {
  tableIndexPrefix?: string;
  tableIndexDelimeter?: string;
}

type TTemplateLiteralLogger = {
  [K in LogLevel]: (message: TemplateStringsArray, ...args: any[]) => void;
};
/**
 * A logger class that uses template literals to format log messages.
 *
 * @class TemplateLiteralLogger
 */
export class TemplateLiteralLogger implements TTemplateLiteralLogger {
  private config: LoggerConfig;
  private allLevels: LogLevel[] = [
    ...ConsoleLogFunctions,
    ...(Object.keys(AliasToMethodMap) as Alias[]),
  ] satisfies LogLevel[];
  public debug!: (message: TemplateStringsArray, ...args: any[]) => void;
  public info!: (message: TemplateStringsArray, ...args: any[]) => void;
  public warn!: (message: TemplateStringsArray, ...args: any[]) => void;
  public error!: (message: TemplateStringsArray, ...args: any[]) => void;
  public table!: (message: TemplateStringsArray, ...args: any[]) => void;
  public group!: (message: TemplateStringsArray, ...args: any[]) => void;
  public groupCollapsed!: (
    message: TemplateStringsArray,
    ...args: any[]
  ) => void;
  public groupEnd!: (message: TemplateStringsArray, ...args: any[]) => void;
  public time!: (message: TemplateStringsArray, ...args: any[]) => void;
  public timeEnd!: (message: TemplateStringsArray, ...args: any[]) => void;
  public timeLog!: (message: TemplateStringsArray, ...args: any[]) => void;
  public count!: (message: TemplateStringsArray, ...args: any[]) => void;
  public countReset!: (message: TemplateStringsArray, ...args: any[]) => void;
  public timeStamp!: (message: TemplateStringsArray, ...args: any[]) => void;
  public trace!: (message: TemplateStringsArray, ...args: any[]) => void;
  public log!: (message: TemplateStringsArray, ...args: any[]) => void;
  public i!: (message: TemplateStringsArray, ...args: any[]) => void;
  public t!: (message: TemplateStringsArray, ...args: any[]) => void;
  public err!: (message: TemplateStringsArray, ...args: any[]) => void;
  public dbg!: (message: TemplateStringsArray, ...args: any[]) => void;
  public tab!: (message: TemplateStringsArray, ...args: any[]) => void;
  public assert!: (message: TemplateStringsArray, ...args: any[]) => void;

  /**
   * Constructs a new instance of the TemplateLiteralLogger class with the given configuration.
   *
   * @param {Partial<LoggerConfig>} [config={}] - The configuration options for the logger. If not provided, the logger will use the default configuration.
   * @param {boolean} [config.enabled=false] - Whether the logger is enabled or not.
   * @param {string} [config.prefix=''] - A prefix to add to the beginning of each log message.
   * @param {LogLevel} [config.minLevel='debug'] - The minimum log level that will be logged.
   * @param {AdditionalOptions} [config.options={}] - Additional options for the logger.
   * @param {LogType} [config.options.type='client'] - The type of logger to use. Can be either 'client' or 'server'.
   * @param {Record<string, string>} [config.options.style={}] - A record of CSS styles to apply to the log messages.
   * @param {readonly PrimitiveType[]} [config.options.primitivesAllowedInTemplateString=[ 'bigint', 'boolean', 'number', 'string', ]] - The primitives that are allowed to be used in template strings.
   * @param {boolean} [config.options.skipPrimitivesIncludedInMessage=false] - Whether to skip primitives included in the message when logging.
   * @param {boolean} [config.options.excludeOutputObject=false] - Whether to exclude the output object from the log message.
   * @param {string} [config.options.tableIndexPrefix=''] - The prefix to use for table indices.
   * @param {string} [config.options.tableIndexDelimeter='.'] - The delimiter to use for table indices.
   */
  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      ...defaultConfig,
      ...config,
      options: { ...defaultConfig.options, ...config.options },
    };

    this.allLevels.forEach((level) => {
      if (level in AliasToMethodMap) {
        this[level] = (message: TemplateStringsArray, ...args: any[]) => {
          this.setLogMethod(AliasToMethodMap[level as Alias], message, ...args);
        };
      } else {
        this[level] = (message: TemplateStringsArray, ...args: any[]) => {
          this.setLogMethod(level, message, ...args);
        };
      }
    });

    if (this.config?.options?.primitivesAllowedInTemplateString) {
      const uniqueTypes = Array.from(
        new Set(this.config?.options?.primitivesAllowedInTemplateString)
      );
      this.config.options.primitivesAllowedInTemplateString =
        uniqueTypes as readonly PrimitiveType[];
    }
  }

  /**
   * Returns true if the logger should log messages at the given level.
   *
   * @param {LogLevel} level - The log level to check.
   * @returns {boolean} Whether the logger should log at the given level.
   * @private
   */
  private shouldLog(level: LogLevel): boolean {
    if (!this.config.enabled) return false;
    const minLevel = this.config.minLevel ?? 'warn';
    return levelOrder[level] >= levelOrder[minLevel];
  }

  /**
   * Prepends a prefix to a message if the prefix is provided.
   *
   * @param {string} prefix - The prefix to add before the message.
   * @param {string} message - The message to be prefixed.
   * @returns {string} The message with the prefix, or the original message if no prefix is provided.
   * @private
   */

  private prefixMessage(prefix: string, message: string) {
    return prefix ? `${prefix} ${message}` : message;
  }

  /**
   * Creates an object from an array of arguments, which can be used as the output object for a log message.
   * If the logger is configured to skip primitives included in the message, then primitives that are included
   * in the message will not be included in the output object. Non-primitive non-array non-function objects will be
   * merged into the output object, while all other values will be assigned to a key in the format 'argX', where X
   * is the index of the argument in the array.
   * @param {any[]} [args] - The arguments to create the object from.
   * @param {any[]} [templateAllowedPrimitives] - The primitives that are allowed to be used in template strings.
   * @returns {Record<string, any>} The object created from the arguments.
   * @private
   */
  private createLogOutputObject(
    args?: any[],
    templateAllowedPrimitives?: any[]
  ) {
    // if (args?.length === 1) return args[0];
    return args?.reduce((acc, currentArg, index) => {
      const isNonNullNonArrayNonFunctionObj =
        currentArg !== null &&
        typeof currentArg === 'object' &&
        typeof currentArg !== 'function' &&
        !Array.isArray(currentArg);
      if (
        this.config.options?.skipPrimitivesIncludedInMessage &&
        templateAllowedPrimitives?.includes(currentArg)
      ) {
        return acc;
      }
      if (typeof currentArg === 'string') {
        return { ...acc, [currentArg]: currentArg };
      }
      if (isNonNullNonArrayNonFunctionObj) {
        return { ...acc, ...currentArg };
      }
      return { ...acc, [`arg${index}`]: currentArg };
    }, {} satisfies Record<string, any>);
  }

  /**
   * Prepares a message and an output object for logging.
   *
   * @param {string} message - The message to be logged.
   * @param {any[]} templateAllowedPrimitives - The primitives that are allowed to be used in template strings.
   * @param {LogLevel} level - The log level of the message.
   * @param {OutputObjectOptions} [options] - The options for creating the output object.
   * @param {string} [options.tableIndexDelimeter] - The delimiter to use for table indices.
   * @param {string} [options.tableIndexPrefix] - The prefix to use for table indices.
   * @param {any[]} [args] - The arguments to create the output object from.
   *
   * @returns { { messagePart: string, outputObj: Record<string, any> | undefined } } The prepared message and output object.
   * @private
   */
  private structureMessage(
    message: string,
    templateAllowedPrimitives: any[],
    options: TabulatedOutputObjectOptions = {
      tableIndexDelimeter: this.config.options?.flattenedObjectIndexDelimeter,
      tableIndexPrefix: this.config.options?.flattenedObjectIndexPrefix,
    },
    ...args: any[]
  ) {
    const messagePart = this.prefixMessage(
      this.config.prefix || '',
      message || ''
    );
    const resultObj = this.createLogOutputObject(
      args,
      templateAllowedPrimitives
    );
    let outputObj;
    if (this.config.options?.excludeOutputObject) {
      outputObj = undefined;
      return { messagePart, outputObj };
    }
    outputObj = {};
    if (this.config.options?.flattenOutputObject) {
      outputObj = flattenObjectWithArrays(resultObj, {
        prefix: options.tableIndexPrefix,
        delimiter: options.tableIndexDelimeter,
      });
    } else {
      outputObj = resultObj;
    }
    return { messagePart, outputObj };
  }

  /**
   * Converts a camelCase string to kebab-case.
   * @param {string} camelCaseString - The camelCase string to be converted.
   * @returns {string} The kebab-case string.
   * @private
   */
  private camelToKebab(camelCaseString: string) {
    return camelCaseString.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();
  }

  /**
   * Sets the logging method for a given log level, formats the message, and logs it.
   * Determines if the message should be logged based on the log level and configuration.
   * Formats the message with template strings and allowed primitives, and applies styles if specified.
   * Handles various log levels and their specific logging behaviors.
   *
   * @param {LogLevel} [level='log'] - The log level to use for logging.
   * @param {TemplateStringsArray} [message] - The template string message to log.
   * @param {...any[]} args - Additional arguments for the log message.
   * @returns {void}
   * @private
   */

  /**
   * Sets the logging method for a given log level, formats the message, and logs it.
   * Determines if the message should be logged based on the log level and configuration.
   * Formats the message with template strings and allowed primitives, and applies styles if specified.
   * Handles various log levels and their specific logging behaviors.
   *
   * @param {LogLevel} [level='log'] - The log level to use for logging.
   * @param {TemplateStringsArray} [message] - The template string message to log.
   * @param {...any[]} args - Additional arguments for the log message.
   * @returns {void}
   * @private
   */
  private setLogMethod(
    level: LogLevel = 'log',
    message?: TemplateStringsArray,
    ...args: any[]
  ): void {
    if (!this.shouldLog(level)) return;

    const primitives: any[] = [];

    args.forEach((arg) => {
      const nullish =
        arg === null ||
        (Array.isArray(arg) && arg.length === 0) ||
        (typeof arg === 'object' && Object.keys(arg).length === 0)
          ? 'nullish'
          : undefined;
      const type = typeof arg;
      if (
        (nullish &&
          this.config?.options?.primitivesAllowedInTemplateString?.includes(
            nullish
          )) ||
        this.config.options?.primitivesAllowedInTemplateString?.includes(type)
      ) {
        primitives.push(arg);
      }
    });

    const formattedMessage = this.formatMessage(message, ...primitives);
    const tabulatedOutputObjectOptions = {
      tableIndexDelimeter: '.',
      tableIndexPrefix: '',
    };
    const { messagePart, outputObj } = this.structureMessage(
      formattedMessage,
      primitives,
      tabulatedOutputObjectOptions,
      ...args
    );
    let prefixedMessage = '';
    let styles = '';
    if (this.config?.options?.style) {
      prefixedMessage = '%c' + messagePart;
      styles = this.camelToKebab(
        JSON.stringify(this.config.options.style)
          .replace(/[{"}]/g, '')
          .replace(/[,]/g, ';')
      );
    }

    switch (level) {
      case 'table':
        consoleMethods[level](outputObj);
        break;
      case 'assert': {
        let assertion = args[0];
        if (typeof assertion !== 'boolean') {
          assertion = !!assertion;
        }
        if (
          !(typeof outputObj === 'object') ||
          (outputObj && Object.keys(outputObj).length === 0)
        ) {
          consoleMethods[level](assertion, prefixedMessage, styles);
          break;
        }
        consoleMethods[level](assertion, prefixedMessage, styles, outputObj);
        break;
      }
      case 'groupEnd':
        consoleMethods[level]();
        break;
      case 'time':
      case 'timeEnd':
      case 'timeStamp':
      case 'group':
      case 'groupCollapsed':
      case 'count':
      case 'countReset':
        consoleMethods[level](prefixedMessage);
        break;
      default:
        if (
          !(typeof outputObj === 'object') ||
          (outputObj && Object.keys(outputObj).length === 0)
        ) {
          consoleMethods[level](prefixedMessage, styles);
          break;
        }
        consoleMethods[level](prefixedMessage, styles, outputObj);
        break;
    }
  }

  /**
   * Format a template string with the given arguments.
   * If the given arguments don't match the template string placeholders,
   * it will return an empty string.
   *
   * @param {TemplateStringsArray} [strings] - The template string.
   * @param {...any[]} args - The arguments to format the template string with.
   * @returns {string} The formatted string.
   */
  private formatMessage(
    strings?: TemplateStringsArray,
    ...args: any[]
  ): string {
    if (!strings || strings.length === 0) {
      return args?.join('') || '';
    }
    if (!args || args.length === 0) {
      return strings.join('');
    }
    let result = '';
    const maxLength = Math.max(strings.length, args.length);

    for (let index = 0; index < maxLength; index++) {
      result += strings[index];
      if (index < args.length) result += String(args[index]);
    }
    return result;
  }

  /**
   * Logs a message with the given arguments at the "log" level using the
   * default log method.
   *
   * @param {TemplateStringsArray} message - The template string.
   * @param {...any[]} args - The arguments to format the template string with.
   */
  defaultBehaviour(message: TemplateStringsArray, ...args: any[]): void {
    this.setLogMethod('log', message, ...args);
  }

  /**
   * Configures the logger instance with the given configuration.
   * The configuration is merged with the existing configuration.
   *
   * @param {Partial<LoggerConfig>} config - The configuration to merge.
   */
  configure(config: Partial<LoggerConfig>): void {
    this.config = {
      ...this.config,
      ...config,
      options: { ...this.config.options, ...config.options },
    };
  }

  /**
   * Inspects the logger configuration.
   *
   * @param {INSPECT_KEYS} [key] - The key to inspect. If not provided, the
   * entire configuration is returned.
   * @returns {LoggerConfig | LoggerConfig[INSPECT_KEYS]} The
   * configuration or the value of the given key.
   */
  inspectLoggerConfig(
    key?: INSPECT_KEYS
  ): LoggerConfig | LoggerConfig[INSPECT_KEYS] {
    return key ? this.config[key] : this.config;
  }

  /**
   * Determines if the given property is a valid property of the TemplateLiteralLogger class.
   *
   * @param {string | symbol} prop - The property name or symbol to check.
   * @returns {boolean} True if the property is a key of TemplateLiteralLogger, false otherwise.
   */
  public static isPropertyOfDebugLogger(
    prop: string | symbol
  ): prop is keyof TemplateLiteralLogger {
    return prop in TemplateLiteralLogger.prototype;
  }

  /**
   * Wraps the given logger instance with a proxy which returns the logger
   * configuration when an invalid property is accessed.
   *
   * @param {TemplateLiteralLogger} obj - The logger instance to wrap.
   * @returns {TemplateLiteralLogger} The wrapped logger instance.
   */
  public static setDefault(obj: TemplateLiteralLogger): TemplateLiteralLogger {
    return new Proxy(obj, {
      get: function (
        target: TemplateLiteralLogger,
        prop: string | symbol,
        receiver: any
      ) {
        if (!TemplateLiteralLogger.isPropertyOfDebugLogger(prop))
          return target.inspectLoggerConfig();
        return Reflect.get(target, prop, receiver);
      },
    }) as TemplateLiteralLogger;
  }

  /**
   * Creates a new instance of the TemplateLiteralLogger class with the specified prefix.
   *
   * @param {string} prefix - A prefix to add to the beginning of each log message.
   * @returns {TemplateLiteralLogger} A new instance of the TemplateLiteralLogger with the specified prefix.
   */
  public static logger(prefix: string): TemplateLiteralLogger {
    return new TemplateLiteralLogger({ prefix });
  }

  /**
   * Creates a log method bound to a logger instance with the specified configuration and log level.
   *
   * @param {LoggerConfig} config - The configuration to initialize the logger.
   * @param {LogLevel} [type='log'] - The log level to bind the log method to.
   * @returns {Function} The log method bound to the logger instance.
   */
  public static createLog(config: LoggerConfig, type: LogLevel = 'log') {
    const logger = new TemplateLiteralLogger(config);
    return logger[type].bind(logger);
  }

  /**
   * Creates a higher-order function that returns a log method bound to a logger instance
   * with the specified configuration.
   *
   * @param {LoggerConfig} config - The configuration to initialize the logger.
   * @returns {(type: LogLevel) => Function} A function that takes a log level and
   * returns the log method bound to the logger instance.
   */
  public static createLoggerHOF(config: LoggerConfig) {
    const logger = new TemplateLiteralLogger(config);
    return (type: LogLevel) => logger[type].bind(logger);
  }
}

export const logger = new TemplateLiteralLogger(defaultConfig);

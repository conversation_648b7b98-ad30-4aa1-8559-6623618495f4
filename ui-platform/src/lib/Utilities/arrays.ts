/**
 * Utility functions for comparing objects in arrays
 */

/**
 * Exclude objects in an array which match the given object
 * @param {T[]} ArrData The array of objects to filter
 * @param {Partial<T>} itemData The object to compare against
 * @returns {T[]} An array of objects that do not match the given object
 */
export const excludeMatchingDataInArray = <
  T extends Record<string | number, any>,
  U extends Partial<T>
>(
  ArrData: T[],
  itemData: U,
  refColumnName?: string
) => {
  if (refColumnName) {
    const res = ArrData.filter(
      (itmData) => itmData[refColumnName] !== itemData[refColumnName]
    );
    return res;
  }
  return ArrData.filter(
    (itmData) =>
      !JSON.stringify(itmData).match(JSON.stringify(itemData).slice(1, -1))
  );
};

/**
 * Filters and returns objects in an array that match the given object.
 * @param {T[]} ArrData The array of objects to search.
 * @param {Partial<T>} itemData The object to compare against.
 * @returns {T[]} An array of objects that match the given object.
 */
export const matchingDataInArray = <
  T extends Record<string | number, any>,
  U extends Partial<T>
>(
  ArrData: T[],
  itemData: U,
  refColumnName?: string
) => {
  if (refColumnName) {
    return ArrData.filter(
      (itmData) => itmData[refColumnName] === itemData[refColumnName]
    );
  }
  return ArrData.filter(
    (itmData) =>
      !!JSON.stringify(itmData).match(JSON.stringify(itemData).slice(1, -1))
  );
};

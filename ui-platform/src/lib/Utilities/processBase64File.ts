import { fileTypes } from './fileUploadMimeTypes';
import { logDebug } from './logDebug';

interface Base64ProcessingResult {
  blob: Blob;
  mimeType: string;
  extension: string;
  cleanB64Data: string;
}

interface FileConfig {
  base64: string;
  filename: string;
  purpose: string;
  created: string;
  mimeType?: string;
}

interface ViewerOptions {
  debug?: boolean;
  previewFileSizeThreshold?: number;
  largeFilePreviewBrowserExclusion?: string[];
  downloadable?: boolean;
  closePreviewFunc?: () => void;
}

const DEFAULT_MIME_TYPE = 'application/octet-stream';
const DEFAULT_EXTENSION = 'file';
const SUPPORTED_MIME_TYPES = fileTypes;

/**
 * Determines the media type based on the given MIME type string.
 *
 * @param mimeType - The MIME type string to evaluate (e.g., 'application/pdf', 'image/jpeg').
 * @returns 'pdf' if the MIME type corresponds to a PDF document, 'image' if it corresponds to an image,
 *          or 'other' for any other types or invalid input.
 */

const getMediaType = (mimeType: string): 'pdf' | 'image' | 'other' => {
  if (!mimeType || typeof mimeType !== 'string') {
    return 'other';
  }

  const normalizedMimeType = mimeType.toLowerCase();
  if (
    normalizedMimeType === 'application/pdf' ||
    normalizedMimeType === 'image/pdf'
  ) {
    return 'pdf';
  }
  if (
    normalizedMimeType.startsWith('image/') &&
    normalizedMimeType !== 'image/pdf'
  ) {
    return 'image';
  }
  return 'other';
};

const BASE64_REGEX = {
  VALID_CHARS: /^[A-Za-z0-9+/]+={0,2}$/,
  DATA_URI: /^data:([^;]+);base64,(.+)$/,
  MIME_TYPE: /^data:([^;]+);base64$/,
};

const BASE64_CHUNK_SIZE = 8192;
const STREAM_CHUNK_SIZE = 1024 * 1024;
const MAX_MEMORY_THRESHOLD = 5 * 1024 * 1024;

const findExtension = (mimeType: string): string | undefined => {
  return Object.entries(SUPPORTED_MIME_TYPES)
    .flatMap(([_, mimeObj]) => Object.entries(mimeObj))
    .find(([mime]) => mime === mimeType)?.[1][0]
    ?.slice(1);
};

interface ICleanB64String {
  b64: string;
  explicitMimeType?: string;
  debug: boolean;
}

interface CleanBase64Result {
  cleanBase64: string;
  deducedMimeType?: string;
}

/**
 * Takes a base64 string and returns a cleaned version of it with any
 * irrelevant data URI prefixes removed and whitespace, line breaks, and
 * invalid characters removed.
 *
 * @param {string} b64 The base64 string to be processed.
 * @param {string} [explicitMimeType] The MIME type of the file, if known.
 * @param {boolean} [debug=false] Whether to log debug information.
 *
 * @returns {CleanBase64Result} An object containing the cleaned base64
 * string and the deduced MIME type.
 *
 * @throws {Error} If the input string is empty, not in valid base64 format,
 * or cannot be decoded.
 */
const cleanBase64String = ({
  b64,
  explicitMimeType,
  debug = false,
}: ICleanB64String): CleanBase64Result => {
  let cleanBase64: string;
  let deducedMimeType = explicitMimeType;

  const log = logDebug('CleanBase64String', debug);
  if (debug) {
    const previewLength = Math.min(50, b64.length);
    log('Start cleaning base 64 string', {
      previewStart: b64.slice(0, previewLength),
      previewEnd: b64.slice(-previewLength),
      totalLength: b64.length,
    });
  }
  const startOfString = b64.slice(0, 100);
  const dataUriMatch = startOfString.match(BASE64_REGEX.DATA_URI);
  if (dataUriMatch) {
    deducedMimeType = deducedMimeType || dataUriMatch[1];
    const prefixLength = `data:${dataUriMatch[1]};base64,`.length;
    // cleanBase64 = dataUriMatch[2];
    cleanBase64 = b64.slice(prefixLength);
    log('Extracted from data URI', { mimeType: deducedMimeType, prefixLength });
  } else {
    cleanBase64 = b64;
  }

  cleanBase64 = cleanBase64.replace(/[\s\r\n]+|[^A-Za-z0-9+/=]/g, '');
  if (cleanBase64.length > STREAM_CHUNK_SIZE) {
    const isValidStart = BASE64_REGEX.VALID_CHARS.test(
      cleanBase64.slice(0, 1000)
    );
    const isValidEnd = BASE64_REGEX.VALID_CHARS.test(cleanBase64.slice(-1000));
    if (!isValidStart || !isValidEnd) {
      throw new Error('BASE64_INVALID_FORMAT');
    }
  } else if (!BASE64_REGEX.VALID_CHARS.test(cleanBase64)) {
    throw new Error('BASE64_INVALID_FORMAT');
  }
  return { cleanBase64, deducedMimeType };
};

const processBase64Stream = async function* (
  base64: string,
  chunkSize: number,
  debug = false
) {
  const log = logDebug('ProcessBase64Stream', debug);
  const totalLength = base64.length;
  log('Initialising base64 stream processing', {
    startOfB64: base64.slice(0, 50),
    endOfB64: base64.slice(-50),
    chunkSize,
  });
  for (let offset = 0; offset < totalLength; offset += chunkSize) {
    const chunk = base64.slice(offset, offset + chunkSize);
    log('Processed chunk', {
      chunkStart: offset,
      chunkLength: chunk.length,
    });
    yield chunk;
  }
};

/**
 * Processes a base64 string and returns a blob and associated metadata.
 *
 * @param {string} base64 The base64 string to be processed.
 * @param {string} [explicitMimeType] The MIME type of the file, if known.
 * @param {boolean} [debug=false] Whether to log debug information.
 *
 * @returns {Promise<Base64ProcessingResult>} A promise that resolves with an object containing the processed blob, MIME type, file extension, and the cleaned base64 string.
 *
 * @throws {Error} If the input string is empty, not in valid base64 format, or cannot be decoded.
 */
const processBase64 = async (
  base64: string,
  explicitMimeType?: string,
  debug = false
): Promise<Base64ProcessingResult> => {
  const startTime = debug ? performance.now() : 0;

  const log = logDebug('ProcessBase64', debug);
  const errorLog = logDebug('ProcessBase64', debug, 'error');
  try {
    if (!base64?.trim()) {
      throw new Error('BASE64_EMPTY_INPUT');
    }

    const useStreaming = base64.length > MAX_MEMORY_THRESHOLD;
    log('Starting base64 processing', {
      inputLength: base64.length,
      mode: useStreaming ? 'streaming' : 'memory',
    });
    const cleanResult = cleanBase64String({
      b64: base64,
      explicitMimeType,
      debug,
    });
    let cleanedBase64 = cleanResult.cleanBase64;
    const deducedMimeType = cleanResult.deducedMimeType;

    const paddingNeeded = (4 - (cleanedBase64.length % 4)) % 4;
    if (paddingNeeded > 0) {
      cleanedBase64 = cleanedBase64 + '='.repeat(paddingNeeded);
    }

    log('Base64 string prepared', {
      originalLength: base64.length,
      cleanedLength: cleanedBase64.length,
      padding: paddingNeeded,
    });

    // const byteCharacters = atob(cleanedBase64);
    const byteArrays: Uint8Array[] = [];

    if (useStreaming) {
      let offset = 0;
      for await (const chunk of processBase64Stream(
        cleanedBase64,
        BASE64_CHUNK_SIZE
      )) {
        try {
          const byteCharacters = atob(chunk);
          const byteNumbers = new Uint8Array(byteCharacters.length);

          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
            // log('Decoded units', {
            //   decodedLength: byteNumbers.length,
            // });
          }
          byteArrays.push(byteNumbers);
          offset += BASE64_CHUNK_SIZE;
        } catch (error) {
          errorLog('Chunk decoding failed', {
            offset,
            chunkLength: chunk.length,
            error,
          });
          throw new Error('BASE64_CHUNK_DECODE_FAIL');
        }
      }
    } else {
      try {
        const byteCharacters = atob(cleanedBase64);
        const byteNumbers = new Uint8Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
          // log('Decoded units', {
          //   decodedLength: byteNumbers.length,
          // });
        }
        byteArrays.push(byteNumbers);
      } catch (error) {
        errorLog('B64 string decoding failed', {
          error,
        });
        throw new Error('BASE64_DECODE_FAIL');
      }
    }

    const finalMimeType =
      deducedMimeType?.toLowerCase() === 'image/pdf'
        ? 'application/pdf'
        : deducedMimeType || DEFAULT_MIME_TYPE;
    const extension = findExtension(finalMimeType) || DEFAULT_EXTENSION;
    const blob = new Blob(byteArrays, { type: finalMimeType });
    const processedResult: Base64ProcessingResult = {
      blob,
      mimeType: finalMimeType,
      extension,
      cleanB64Data: cleanedBase64,
    };

    if (debug) {
      const endTime = performance.now();
      log('Processing completed', {
        processingTime: `${(endTime - startTime).toFixed(2)}ms`,
        outputSize: processedResult.blob.size,
        mimeType: processedResult.mimeType,
        extension: processedResult.extension,
      });
    }

    return processedResult;
  } catch (error) {
    const errorDetails = {
      code: error instanceof Error ? error.message : 'UNKNOWN_ERROR',
      inputLength: base64.length,
      inputPreview: base64.substring(0, 50) + '...',
      timestamp: new Date().toISOString(),
      stack: error instanceof Error ? error.stack : undefined,
    };
    errorLog('Processing failed', errorDetails);

    const errorMessages: Record<string, string> = {
      BASE64_EMPTY_INPUT: 'The provided base64 string is empty',
      BASE64_INVALID_FORMAT:
        'The provided string is not in valid base64 format',
      UNKNOWN_ERROR: 'An unexpected error occurred while processing the file',
      BASE64_CHUNK_DECODE_FAIL: 'Failed to decode a chunk of the base64 string',
      BASE64_DECODE_FAIL: 'Failed to decode the entire base64 string',
    };

    throw new Error(
      errorMessages[errorDetails.code] || `Unknown error: ${errorDetails.code}`
    );
  }
};

/**
 * Takes a base64 encoded string and associated metadata and returns a viewer props object
 * that can be used to display the file in the browser.
 *
 * @param base64 - The base64 encoded string
 * @param filename - Optional filename for the file
 * @param mimeType - Optional mime type for the file, will be deduced if not provided
 * @param purpose - Optional purpose of the file, for example "document" or "image"
 * @param created - Optional timestamp for when the file was created
 * @param debug - Optional flag to enable debug logging
 * @param previewFileSizeThreshold - Optional size threshold in MB above which the file will not be previewable in the browser
 * @param largeFilePreviewBrowserExclusion - Optional list of browser names that will not be able to preview large files
 * @param closePreviewFunc - Optional function to call when the preview is closed
 * @returns A viewer props object that can be used to display the file in the browser
 */
export const previewFile = async ({
  base64,
  filename,
  mimeType,
  purpose,
  created,
  debug = false,
  previewFileSizeThreshold = 2,
  largeFilePreviewBrowserExclusion = ['KHTML', 'Chrome', 'OPR'],
  closePreviewFunc,
}: FileConfig & ViewerOptions) => {
  if (!base64) return;

  const log = logDebug('PreviewFile', debug);
  const errorLog = logDebug('PreviewFile', debug, 'error');
  const b64FileSizeMB = base64.length / (1024 * 1024);
  const approximateDecodedSizeMB = b64FileSizeMB * 0.75;
  log('Base64 file size:', {
    b64FileSizeMB: `${b64FileSizeMB}MB`,
    approximateDecodedSizeMB: `${approximateDecodedSizeMB}MB`,
  });
  let browserWillNotPreviewB64File = false;
  if (approximateDecodedSizeMB > previewFileSizeThreshold) {
    browserWillNotPreviewB64File = largeFilePreviewBrowserExclusion.some(
      (brwsr: string) => {
        const browserProperties = navigator.userAgent;
        const browserHasProperty = browserProperties.includes(brwsr);
        log(`Checking if browser has - ${brwsr} in its user agent string:`, {
          browserProperties,
          browserHasProperty,
        });
        return browserHasProperty;
      }
    );
  }
  log('Browser will preview b64 file:', !browserWillNotPreviewB64File);
  try {
    const normalizedBase64 = await normalizeFileBase64(base64, filename);
    const {
      blob,
      mimeType: determinedMimeType,
      extension,
      cleanB64Data,
    } = await processBase64(normalizedBase64, mimeType, debug);

    log('Base64 processing outcome', {
      file: blob,
      fileMimeType: determinedMimeType,
      fileExtension: extension,
      cleanedB64DataSample: cleanB64Data.slice(0, 100),
    });

    const fileURL = URL.createObjectURL(blob);
    const finalFilename = filename || `document.${determinedMimeType}`;
    const mediaType = !browserWillNotPreviewB64File
      ? getMediaType(determinedMimeType)
      : 'other';

    const src = mediaType === 'pdf' ? cleanB64Data : fileURL;
    const viewerProps = {
      src: src,
      purpose,
      fileName: finalFilename,
      mediaType,
      onClose: () => {
        closePreviewFunc?.();
        URL.revokeObjectURL(fileURL);
      }, // Cleanup on close
      created,
      onDownload: () =>
        downloadFile({
          base64,
          filename: finalFilename,
          mimeType: determinedMimeType,
          created,
          purpose,
          debug,
        }),
      onOpenInTab: () => {
        const newWindow = window.open(fileURL, '_blank');
        if (!newWindow) {
          throw new Error(
            'Failed to open preview - popup blocker might be enabled'
          );
        }
        setTimeout(() => URL.revokeObjectURL(fileURL), 30000);
      },
    };
    return viewerProps;
  } catch (error) {
    const errorDetails = {
      code: error instanceof Error ? error.message : 'UNKNOWN_ERROR',
      inputLength: base64.length,
      inputPreview: base64.substring(0, 50) + '...',
      timestamp: new Date().toISOString(),
      stack: error instanceof Error ? error.stack : undefined,
    };
    const errorMessage =
      error instanceof Error
        ? `${error.message}: Please try refreshing the page`
        : 'Unknown error: Try refreshing the website';
    errorLog('Failed to preview file', error);
    const errorMessages: Record<string, string> = {
      BASE64_EMPTY_INPUT: 'The provided base64 string is empty',
      BASE64_INVALID_FORMAT:
        'The provided string is not in valid base64 format',
      UNKNOWN_ERROR: 'An unexpected error occurred while processing the file',
      BASE64_CHUNK_DECODE_FAIL: 'Failed to decode a chunk of the base64 string',
    };
    throw new Error(`Failed to preview file: ${errorMessage}`);
  }
};

/**
 * Downloads a file from a base64 string.
 *
 * @param base64 - The base64 string to download.
 * @param filename - The filename to use for the downloaded file.
 * @param mimeType - The mime type of the file.
 * @param created - The creation date or timestamp of the file.
 * @param purpose - A description or label for the document.
 * @param debug - Whether to log debug information.
 * @param downloadable - Whether the file should be downloadable.
 * @returns A promise that resolves when the download is complete.
 */
const downloadFile = async ({
  base64,
  filename,
  mimeType,
  created,
  purpose,
  debug = false,
  downloadable = true,
}: FileConfig & ViewerOptions): Promise<void> => {
  if (!downloadable) return;
  if (!base64) return;
  const log = logDebug('DownloadFile', debug);
  const errorLog = logDebug('DownloadFile', debug, 'error');

  try {
    const { blob, extension } = await processBase64(base64, mimeType, debug);
    const finalFilename = filename || `document.${extension}`;

    const downloadLink = document.createElement('a');
    downloadLink.href = URL.createObjectURL(blob);
    downloadLink.download = finalFilename;

    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);

    setTimeout(() => URL.revokeObjectURL(downloadLink.href), 60000);
  } catch (error) {
    const errorMessage =
      error instanceof Error
        ? `${error.message}: Please try refreshing the page`
        : 'Unknown error: Try refreshing the website';
    errorLog('Download failed:', errorMessage);
    throw new Error(`Failed to download file: ${errorMessage}`);
  }
};

type ExtensionMap = Record<string, string>; // ext → MIME
type MimeMap = Record<string, string>; // MIME → ext

const EXT_TO_MIME: ExtensionMap = {
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.gif': 'image/gif',
  '.webp': 'image/webp',
  '.pdf': 'application/pdf',
  '.txt': 'text/plain',
  '.json': 'application/json',
  '.bin': 'application/octet-stream',
};

const MIME_TO_EXT: MimeMap = Object.fromEntries(
  Object.entries(EXT_TO_MIME).map(([ext, mime]) => [mime, ext])
);

interface ExtensionResult {
  ext: string;
  mime: string;
}

/**
 * Determine the file extension and MIME type from a filename, base64 string,
 * or fallback extension.
 *
 * If the filename has an extension, use it. Otherwise, attempt to detect the
 * file type from the base64 string. If that fails, use the fallback extension if
 * given. If all else fails, default to `.bin` and `application/octet-stream`.
 *
 * @param fileName - The filename to process
 * @param fallbackExt - Optional fallback extension to use if all else fails
 * @param base64String - Optional base64 string to detect the file type from
 * @param fileContext - Optional context to prioritize file types for detection
 * @returns An object with the file extension and MIME type
 */
async function pickExtension(
  fileName: string,
  fallbackExt?: string,
  base64String?: string,
  fileContext?: 'image' | 'pdf'
): Promise<ExtensionResult> {
  const match = fileName.match(/(\.[^./\\]+)$/i);
  if (match) {
    const ext = match[1].toLowerCase();
    const mime = EXT_TO_MIME[ext] || 'application/octet-stream';
    return { ext, mime };
  }

  if (base64String) {
    const ext = await detectFileExtension(base64String, fileContext);
    if (ext && EXT_TO_MIME[ext]) {
      return { ext, mime: EXT_TO_MIME[ext] };
    }
  }

  if (fallbackExt && EXT_TO_MIME[fallbackExt.toLowerCase()]) {
    const ext = fallbackExt.startsWith('.') ? fallbackExt : `.${fallbackExt}`;
    const mime = EXT_TO_MIME[ext.toLowerCase()] || 'application/octet-stream';
    return { ext, mime };
  }
  return { ext: '.bin', mime: 'application/octet-stream' };
}

/**
 * Normalize a base64-encoded string to always have a "data:" header with a MIME type.
 *
 * If the input string already has a "data:" header, it will be replaced with a new one
 * containing a MIME type, unless `options.replaceExistingMime` is false.
 *
 * @param {string} input The base64-encoded string to normalize
 * @param {string} [fileName=''] The name of the file, used to detect MIME type from extension
 * @param {{
 *   replaceExistingMime?: boolean;
 *   fallbackExt?: string;
 *   debug?: boolean;
 *   fileContext?: 'image'|'pdf';
 * }} [options] Options object
 * @returns {Promise<string>} The normalized base64 string
 */
export async function normalizeFileBase64(
  input: string,
  fileName = '',
  options?: {
    replaceExistingMime?: boolean;
    fallbackExt?: string;
    debug?: boolean;
    fileContext?: 'image' | 'pdf';
  }
): Promise<string> {
  const log = logDebug('NormalizeFileBase64', options?.debug);
  log('Normalizing base64 string', {
    inputLength: input.length,
    fileName,
    options,
  });
  const comma = input.indexOf(',');
  let payload: string;
  let header: string;

  if (comma === -1) {
    payload = input.trim();
    header = '';
  } else {
    header = input.slice(0, comma);
    payload = input.slice(comma + 1);
  }
  log('Extracted header and payload', {
    header,
    payloadLength: payload.length,
  });

  const { ext, mime } = await pickExtension(
    fileName,
    options?.fallbackExt,
    payload,
    options?.fileContext
  );
  log('Picked extension and mime type', { ext, mime });

  const newHeader = header.startsWith('data:')
    ? options?.replaceExistingMime
      ? header.replace(/data:[^;]*/, `data:${mime}`)
      : header
    : `data:${mime};base64`;
  log('Constructed new header', { newHeader });
  return `${newHeader},${payload}`;
}

interface FileSignature {
  signature: string; // ASCII or hex representation of the magic number
  ext: string; // File extension
  maxBytes: number; // Maximum bytes needed for this signature
}

/**
 * Detects the file extension from a Base64 string, given an optional context.
 *
 * @param base64String - The Base64 string to detect the file extension from.
 * @param context - An optional string context to use when detecting the file extension.
 * @returns The detected file extension as a string, or `null` if unknown.
 */
export async function detectFileExtension(
  base64String: string,
  context?: string
): Promise<string | null> {
  try {
    // Define signatures with ASCII or hex strings and their required byte lengths
    const signatures: FileSignature[] = [
      { signature: '\x89PNG\r\n\x1a\n', ext: 'png', maxBytes: 8 }, // PNG
      { signature: '\xff\xd8\xff', ext: 'jpg', maxBytes: 3 }, // JPEG
      { signature: '%PDF-', ext: 'pdf', maxBytes: 5 }, // PDF
      { signature: 'GIF89a', ext: 'gif', maxBytes: 6 }, // GIF
      { signature: 'PK\x03\x04', ext: 'zip', maxBytes: 4 }, // ZIP
    ];

    // Determine the maximum bytes needed across all signatures
    const maxSignatureBytes = Math.max(...signatures.map((s) => s.maxBytes));

    // Decode only the first portion of the Base64 string
    const isNode = typeof Buffer !== 'undefined';
    let bytes: Uint8Array | Buffer;

    if (isNode) {
      // Node.js: Use Buffer for efficient decoding
      bytes = Buffer.from(base64String, 'base64').slice(0, maxSignatureBytes);
    } else {
      // Browser: Use atob with limited decoding
      const binaryString = atob(base64String);
      const len = Math.min(binaryString.length, maxSignatureBytes);
      bytes = new Uint8Array(len);
      for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
    }

    // Convert the first bytes to an ASCII string for comparison
    let header: string;
    if (isNode) {
      header = (bytes as Buffer).toString('ascii', 0, maxSignatureBytes);
    } else {
      // Browser: Convert Uint8Array to string
      const decoder = new TextDecoder('ascii');
      header = decoder.decode(bytes);
    }

    // Check signatures using string comparison
    for (const { signature, ext, maxBytes } of signatures) {
      if (header.length >= maxBytes && header.startsWith(signature)) {
        return ext;
      }
    }

    // Fallback to context if provided
    if (context) {
      if (context.includes('image')) return 'png'; // Default to PNG for images
      if (context.includes('pdf')) return 'pdf';
    }

    return null; // Unknown type
  } catch (error) {
    console.error('Error detecting file extension:', error);
    return null;
  }
}

import {
  But<PERSON><PERSON>ink,
  <PERSON>lient<PERSON>ogos,
  FragmentStyleHOC,
  ScrollableContent,
  TemplateLiteralLogger,
  Text,
  normalizeUrl,
} from '@4-sure/ui-platform';
import styled from 'styled-components';

// instantiate logger for debugging generate control component
const log = TemplateLiteralLogger.createLog(
  {
    prefix: '🪵[Landing page debug]:',
    enabled: false,
    options: { style: { backgroundColor: '#efefef', color: '#017812' } },
  },
  'log'
);

const inspect = TemplateLiteralLogger.createLog(
  {
    prefix: '🧐[Landing page note]:',
    enabled: true,
    options: { style: { color: '#003d8c' } },
  },
  'i'
);

const warn = TemplateLiteralLogger.createLog(
  {
    prefix: '[Landing page warning]:',
    enabled: true,
    options: { style: { color: '#a03f0b' } },
  },
  'warn'
);

const DescriptionSection = FragmentStyleHOC({ Component: Text });

const ContentWrapper = styled.div`
  display: grid;
  place-items: center;
  padding: 4rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  backdrop-filter: blur(10px);
  margin: 1rem 0 6rem;
  /* gap: 3.5rem; */
  max-width: 1200px;
  width: 60%;
  /* height: 70%; */
  justify-self: center;
  align-self: center;

  @media (max-width: 800px) {
    padding: 3rem;
  }
  @media only screen and (max-width: 800px) {
    padding: 3rem;
  }
  @media (max-width: 500px) {
    padding: 2rem;
  }

  @media only screen and (max-width: 500px) {
    padding: 2rem;
  }
`;

const LargeText = styled.div`
  text-align: center;
  color: #e5e5e5;
  font-size: clamp(50px, 80%, 70px);
  font-family: 'Inter', sans-serif;
  font-weight: 100;
  word-wrap: break-word;
  margin-bottom: 1rem;

  @media (max-width: 800px) {
    font-size: 55px;
  }
  @media only screen and (max-width: 800px) {
    font-size: 55px;
  }
  @media (max-width: 500px) {
    font-size: 50px;
  }

  @media only screen and (max-width: 500px) {
    font-size: 30px;
  }
`;
const DescriptionText = styled(DescriptionSection)`
  text-align: center;
  color: #e5e5e5;
  word-wrap: break-word;
  margin: 1rem 0;
  display: grid;
  grid-auto-flow: row;
  justify-items: center;
  font-weight: 200;
  font-size: 31.5px;

  @media (max-width: 800px) {
    font-size: 20px;
  }
  @media only screen and (max-width: 800px) {
    font-size: 20px;
  }
  @media (max-width: 500px) {
    font-size: 16px;
  }

  @media only screen and (max-width: 500px) {
    font-size: 12px;
  }
`;

const MediumText = styled.div`
  text-align: center;
  color: #e5e5e5;
  font-size: 31.5px;
  font-family: 'Inter', sans-serif;
  font-weight: 200;
  word-wrap: break-word;
  line-height: 1.5;
  margin-bottom: 2rem;

  @media mobile and (max-width: 800px) {
    font-size: 26.5px;
  }
  @media only screen and (max-width: 800px) {
    font-size: 26.5px;
  }
  @media (max-width: 500px) {
    font-size: 21.5px;
  }

  @media only screen and (max-width: 500px) {
    font-size: 21px;
  }
`;

const Wrapper = styled.div`
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
`;

const logos: { [key: string]: any } = {
  builders: <ClientLogos {...{ client: 'builders' }} />,
  game: <ClientLogos {...{ client: 'game', label: 'Game' }} />,
  multichoice: (
    <ClientLogos
      {...{
        client: 'multichoice',
        label: 'MultiChoice',
      }}
    />
  ),
  sil: <ClientLogos {...{ client: 'sil', label: 'SIL' }} />,
  pinggo: <ClientLogos {...{ client: 'pinggo' }} />,
  bettersure: <ClientLogos {...{ client: 'bettersure' }} />,
  pinghoc: (
    <ClientLogos
      {...{
        client: 'pinghoc',
        label: 'ClaimConnect',
      }}
    />
  ),
  spmanagement: (
    <ClientLogos
      {...{
        client: '',
        label: 'SP Management',
        theme: 'dark',
        style: { width: '10rem', height: 'auto' },
      }}
    />
  ),
  fieldops: (
    <ClientLogos
      {...{
        client: '',
        label: 'Field Ops',
        theme: 'dark',
        style: { width: '10rem', height: 'auto' },
      }}
    />
  ),
};

const clients: { [key: string]: any } = {
  pinggo: import.meta.env.VITE_PINGGOSP_URL,
  bettersure: import.meta.env.VITE_BETSP_URL,
  pinghoc: import.meta.env.VITE_PINGSP_URL,
};

const ContentScroll = styled(ScrollableContent)`
  display: grid;
  place-items: center;
  height: 92vh;
  width: 100%;
  overflow-y: auto;

  & .simplebar-content {
    height: 92vh;
    display: grid;
  }
`;

const FixIssuesButton = styled(ButtonLink)`
  height: auto;
  width: auto;
  padding: ${(props) => props.theme.SpacingMd};
  ${(props) => props.theme.SpacingXl};
  & > div {
    margin-bottom: 0;
    font-weight: 200;
    font-size: 21px;
  }
`;

export const LandingPage = ({
  headerText,
  line1Text,
  line2Text,
  displayPlatforms,
  staffType,
  staffRoles,
  associatedCompanies,
  description,
  hideClients,
}: any) => {
  log`${'(Line 217)'} Staffmember details: ${{ staffType, staffRoles }}`;
  log`${'(Line 218)'} Is field-ops: ${
    staffType === 1 && hasRoles(staffRoles, [44])
  }`;
  function hasRoles(teamMemberRoles: number[], requiredRoles: number[]) {
    return requiredRoles.every((element) => teamMemberRoles.includes(element));
  }

  const availableClients =
    associatedCompanies && Array.isArray(associatedCompanies)
      ? associatedCompanies.map(
          (company: {
            short_name: keyof typeof logos;
            name: string;
            meta: { mobile: { filename: string; host: string } };
          }) => ({
            ...company,
            logo: logos[company.short_name],
          })
        )
      : [];
  log`${'(Line 239)'} Available clients ${availableClients}`;
  return (
    // <div
    //   style={{
    //     display: 'grid',
    //     placeItems: 'center',
    //     height: '100vh',
    //     width: '100%',
    //     overflowY: 'auto',
    //   }}
    // >
    <ContentScroll>
      <ContentWrapper data-testid="content-wrapper">
        <LargeText>{headerText}</LargeText>
        {description && <DescriptionText props={description} />}
        {(line1Text || line2Text) && (
          <MediumText>
            {line1Text} <br />
            {line2Text}
          </MediumText>
        )}
        {line2Text === 'Company Status: Fixes required' &&
          !displayPlatforms && (
            <FixIssuesButton
              label="Fix issues"
              href={`${normalizeUrl(
                'fixes-required/details/overview',
                import.meta.env.VITE_REGISTRATION_APP_URL
              )}`}
              iconSrc={undefined}
              altText={''}
            />
          )}
        {displayPlatforms && (
          <div>
            <div
              style={{
                textAlign: 'center',
                color: '#E5E5E5',
                fontSize: 'clamp(31.5px, 125%, 47.3px)',
                fontFamily: 'Inter',
                fontWeight: '200',
                wordWrap: 'break-word',
                marginBottom: '2rem',
              }}
            >
              Visit our platforms
            </div>

            <Wrapper>
              {staffType === 1 && hasRoles(staffRoles, [44]) && (
                <ButtonLink
                  label=""
                  iconSrc={logos.fieldops}
                  altText="Field Ops"
                  href={import.meta.env.VITE_FIELD_OPS_URL}
                />
              )}

              {!hideClients &&
                staffType === 2 &&
                hasRoles(staffRoles, [9]) &&
                availableClients.length > 0 &&
                availableClients.map(
                  (company: (typeof availableClients)[number], index) => (
                    <ButtonLink
                      key={index}
                      label={''}
                      iconSrc={logos[company.short_name as keyof typeof logos]}
                      altText={`${company.name} SP`}
                      href={
                        clients[company.short_name]
                          ? clients[company.short_name]
                          : ''
                      }
                    />
                  )
                )}

              {!hideClients &&
                staffType === 2 &&
                hasRoles(staffRoles, [10]) &&
                availableClients.length > 0 &&
                availableClients.map(
                  (company: (typeof availableClients)[number], index) => {
                    if (!company?.meta)
                      return (
                        <ButtonLink
                          key={index}
                          label={`${company.name} Mobile App`}
                          // iconSrc={''}
                          altText={`${company.name} Mobile App`}
                          href={''}
                          download={''}
                          onClick={() =>
                            alert(
                              `No download link available for ${company.name} mobile, please contact support`
                            )
                          }
                        />
                      );
                    const downloadUrl = `${company.meta.mobile.host}${company.meta.mobile.filename}`;
                    return (
                      <ButtonLink
                        key={index}
                        label={'Mobile App'}
                        iconSrc={
                          logos[company.short_name as keyof typeof logos]
                        }
                        altText={`${company.name} Mobile App`}
                        href={downloadUrl}
                        download={downloadUrl}
                      />
                    );
                  }
                )}

              {staffType === 2 && hasRoles(staffRoles, [43]) && (
                <ButtonLink
                  label=""
                  iconSrc={logos.spmanagement}
                  altText="SP Management"
                  href={import.meta.env.VITE_SP_MANAGEMENT_URL}
                />
              )}
            </Wrapper>
          </div>
        )}
      </ContentWrapper>
    </ContentScroll>
  );
};

import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { DynamicTable } from './DynamicTable';

// Mock data for testing
const mockData = Array.from({ length: 100 }, (_, i) => ({
  id: i + 1,
  name: `User ${i + 1}`,
  email: `user${i + 1}@example.com`,
  isActive: i % 2 === 0,
  role: ['Admin', 'User', 'Manager'][i % 3],
  createdAt: new Date(2024, i % 12, (i % 28) + 1).toISOString(),
}));

describe('DynamicTable Performance Optimizations', () => {
  it('should render without crashing with large dataset', () => {
    render(
      <DynamicTable
        data={mockData}
        enableVirtualScrolling={true}
        height="500px"
        virtualScrollConfig={{
          enabled: true,
          itemHeight: 48,
          threshold: 50,
          overscan: 10,
        }}
      />
    );

    // Should render the table
    expect(screen.getByRole('application')).toBeInTheDocument();
  });

  it('should handle virtual scrolling without errors', () => {
    const { container } = render(
      <DynamicTable
        data={mockData}
        enableVirtualScrolling={true}
        height="500px"
        virtualScrollConfig={{
          enabled: true,
          itemHeight: 48,
          threshold: 50,
          overscan: 10,
        }}
      />
    );

    // Should not throw errors and table should still be present
    expect(screen.getByRole('application')).toBeInTheDocument();
  });

  it('should render form controls without excessive re-renders', () => {
    const onControlChange = vi.fn();

    const testData = mockData.slice(0, 5);

    render(
      <DynamicTable
        data={testData}
        columnConfig={{
          name: {
            key: 'name',
            header: 'Name',
            controlType: 'plain-text',
            onControlChange,
          },
          isActive: {
            key: 'isActive',
            header: 'Active',
            controlType: 'checkbox',
            onControlChange,
          },
        }}
      />
    );

    // Should render the table
    expect(screen.getByRole('application')).toBeInTheDocument();
  });

  it('should handle empty state correctly', () => {
    render(<DynamicTable data={[]} />);

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('should respect displayColumns prop and only show specified columns', () => {
    const testData = [
      {
        id: 1,
        name: 'Alice',
        email: '<EMAIL>',
        role: 'Manager',
        department: 'Engineering',
      },
      {
        id: 2,
        name: 'Bob',
        email: '<EMAIL>',
        role: 'Developer',
        department: 'Engineering',
      },
    ];

    const columnConfig = {
      id: { key: 'id', header: 'ID' },
      name: { key: 'name', header: 'Name' },
      email: { key: 'email', header: 'Email' },
      role: { key: 'role', header: 'Role' },
      department: { key: 'department', header: 'Department' },
    };

    render(
      <DynamicTable
        data={testData}
        displayColumns={['id', 'name', 'role']}
        columnConfig={columnConfig as any}
      />
    );

    // Should show only the specified columns
    expect(screen.getByText('ID')).toBeInTheDocument();
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Role')).toBeInTheDocument();

    // Should not show hidden columns
    expect(screen.queryByText('Email')).not.toBeInTheDocument();
    expect(screen.queryByText('Department')).not.toBeInTheDocument();

    // Should still show the data for visible columns
    expect(screen.getByText('Alice')).toBeInTheDocument();
    expect(screen.getByText('Bob')).toBeInTheDocument();
    expect(screen.getByText('Manager')).toBeInTheDocument();
    expect(screen.getByText('Developer')).toBeInTheDocument();
  });

  it('should handle basic functionality', () => {
    render(
      <DynamicTable
        data={mockData.slice(0, 10)}
        enablePagination={true}
        pageSize={5}
        enableFiltering={true}
        enableSorting={true}
      />
    );

    // Should render the table
    expect(screen.getByRole('application')).toBeInTheDocument();
  });
});
